import { createRouter, createWebHistory } from 'vue-router'
import otherRouter from './otherRouter'

const routes = [
  {
    path: '/',
    name: '首页',
    component: () => import('@/views/index.vue'),
    children: otherRouter,
  },
  // {
  //   path: '/login',
  //   name: '登录',
  //   component: () => import('@/views/login.vue'),
  // },
  {
    path: '/loding',
    name: '登录中',
    component: () => import('@/views/loading.vue'),
  },
  {
    path: '/error',
    name: '错误页面',
    component: () => import('@/views/ErrorPage.vue'),
    props: (route) => ({
      errorCode: parseInt(route.query.code as string) || 1000,
      errorMessage: (route.query.message as string) || '您暂时没有权限',
    }),
  },
  {
    path: '/UmcAuth/LoginCallback',
    name: 'UMC登录回调',
    component: () => import('@/views/loading.vue'),
  },
  {
    path: '/UmcAuth/LogoutCallback',
    name: 'UMC退出登录回调',
    component: () => import('@/views/logoutCallback.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    return { top: 0 }
  },
})

export default router
