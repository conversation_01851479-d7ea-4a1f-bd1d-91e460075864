<template>
  <div class="w-100 flex flex-col relative" @click.stop>
    <img :src="file.viewUrl || file.url" class="w-100 h-60" loading="lazy" />
    <div class="file-card-mask">
      <DeleteOutlined class="c-#fe5359 absolute top-4 right-4" v-show="file.status === 'done' && !disabled" @click.stop="handleDelete" />
      <div class="absolute top-50% left-50% -translate-x-1/2 -translate-y-1/2 c-#fff" v-show="file.status !== 'uploading'" @click.stop="handlePreview">
        <EyeOutlined />
      </div>
      <LoadingOutlined class="c-primary absolute top-50% left-50% -translate-x-1/2 -translate-y-1/2" v-show="file.status === 'uploading'" />
    </div>
    <div class="flex mt-4">
      <FileTextFilled class="c-#3c82fe" v-show="file.type && file.type?.indexOf('text') != -1" />
      <FileImageFilled class="c-#f6ad00" v-show="file.type && file.type?.indexOf('image') != -1" />
      <FilePdfFilled class="c-#fe5359" v-show="file.type && file.type?.indexOf('pdf') != -1" />
      <span class="ml-4 hover:c-primary truncate">{{ file.name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FileTextFilled, FileImageFilled, FilePdfFilled, LoadingOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons-vue'

defineProps<{
  file: any
  disabled?: boolean
}>()

const emit = defineEmits(['delete', 'preview'])

const handleDelete = () => {
  emit('delete')
}

const handlePreview = () => {
  emit('preview')
}
</script>
<style scoped lang="scss">
.file-card-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.45);
  opacity: 0;
  transition: all 0.3s;
  &:hover {
    opacity: 1;
  }
}
</style>
