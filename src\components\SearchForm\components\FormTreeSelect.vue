<template>
  <a-tree-select
    v-if="item.showDescription != 1"
    v-model:value="item.value"
    :placeholder="item.label"
    allow-clear
    :tree-data="item.selectArr"
    :fieldNames="item.fieldNames ? item.fieldNames : ''"
    :maxTagCount="1"
    :listHeight="400"
    :dropdownMatchSelectWidth="250"
    v-bind="item"
  />
  <a-select v-else :getPopupContainer="(triggerNode) => triggerNode.parentNode" style="width: 100%" :placeholder="item.label">
    <template #dropdownRender="{ menuNode: menu }">
      <component :is="menu" />
      <div style="padding: 8px 0; text-align: center">
        {{ item.description }}
      </div>
    </template>
  </a-select>
</template>

<script setup lang="ts">
defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<any>('item', { required: true })
</script>

<style scoped></style>
