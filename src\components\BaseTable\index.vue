<template>
  <TableHeader v-model:type="lineHeightType" :check-items-arr="checkItemsArr">
    <template v-if="$slots['left-btn']" #left-btn>
      <slot name="left-btn"></slot>
    </template>
    <template v-if="$slots['right-btn']" #right-btn>
      <slot name="right-btn"></slot>
    </template>
  </TableHeader>
  <div class="table-box">
    <div class="box" v-show="tableVisble">
      <vxe-table
        :loading="tableLoading"
        round
        stripe
        :border="true"
        ref="tableRef"
        size="mini"
        @sort-change="sortChangeEvent"
        @resizable-change="({ $table, column }) => tableWColumnWidthChange(column, tableKey, pageType, $table)"
        :row-config="{
          keyField: keyField,
          isHover: true,
          height: lineHeightMap[lineHeightType],
        }"
        :custom-config="{ mode: 'popup' }"
        :data="tableData"
        :show-overflow="true"
        :show-header-overflow="true"
        :show-footer-overflow="true"
        height="100%"
        max-hegiht="100%"
        :column-config="{ resizable: true }"
        class="tableBoxwidth"
        :seq-config="{ startIndex: seqStartIndex }"
        @checkbox-all="selectChangeEvent"
        @checkbox-change="selectChangeEvent"
        :sort-config="{ remote: true }"
        v-bind="$attrs"
      >
        <slot name="column">
          <vxe-column v-if="isCheckbox" type="checkbox" field="checkbox" width="50" fixed="left"></vxe-column>
          <vxe-column v-if="isIndex" type="seq" title="序号" field="seq" :width="60" fixed="left" align="center"></vxe-column>
          <slot name="append"></slot>

          <template v-for="i in filteredTableKey" :key="i.key">
            <vxe-column
              :visible="i.is_show"
              :field="i.key"
              :title="i.name"
              :sortable="i.is_sort"
              :width="i.width"
              :fixed="i.freeze == 1 ? 'left' : i.freeze == 2 ? 'right' : ''"
              :formatter="i.formatter"
              :align="i.align"
              v-bind="i"
            >
              <template v-if="$slots[i.key + '_header']" #header>
                <slot :name="i.key + '_header'" :item="i" />
              </template>
              <template #header>
                {{ i.name }}
                <a-tooltip v-if="i.tooltip">
                  <template #title>{{ i.tooltip }}</template>
                  <InfoCircleOutlined></InfoCircleOutlined>
                </a-tooltip>
              </template>

              <template v-if="$slots[i.key]" #default="attr">
                <div v-if="i.key === 'operate'" class="table-btns">
                  <slot :name="i.key" v-bind="attr" :item="i"></slot>
                </div>
                <slot v-else :name="i.key" v-bind="attr" :item="i" />
              </template>
            </vxe-column>
          </template>
        </slot>
      </vxe-table>
    </div>
  </div>
  <TablePagination v-model:current="page" v-model:page-size="pageSize" :total="total" @change="pageChange" />
  <TableSetting v-model:columns="tableKey" v-model:visible="visible" v-model:form-arr="form" @save="handleSaveTableSetting" @reset="handleResetTableSetting" :page-type="pageType" @search="search" />
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { checkFormParams, initTable, tableWColumnWidthChange, setTableConfig } from '@/utils/index'
// import { useTable } from '@/hook/useTable'
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { VxeTableInstance, VxeToolbarInstance, VxeTablePropTypes } from 'vxe-table'
import { PageType } from '@/common/enum'
import TableSetting from './components/TableSetting.vue'
import TablePagination from './components/TablePagination.vue'
import TableHeader from './components/TableHeader.vue'

const emits = defineEmits<{
  (e: 'initFinish', val: any): void
}>()
const tableLoading = ref(false)
const tableVisble = ref(false)
const toolbarRef = ref<VxeToolbarInstance>()
const tableRef = ref<VxeTableInstance>()
const pageSize = ref(20)
const total = ref(0)
const page = ref(1)
const tableData = ref()
const lineHeightType = ref(2)

const lineHeightMap = {
  1: 36,
  2: 52,
  3: 68,
}

const form = defineModel('form', { default: [] })

const props = withDefaults(
  defineProps<{
    getList: (params: any) => Promise<any>
    keyField?: VxeTablePropTypes.RowConfig['keyField']
    isCheckbox?: boolean
    isIndex?: boolean
    pageType: PageType
    checkCb?: (params: any) => void
    dataAsyncFormat?: (params: any) => Promise<any>
    dataFormat?: (params: any) => void
    formFormat?: (params: any) => any
    autoSearch?: boolean
  }>(),
  {
    keyField: 'id',
    isCheckbox: false,
    isIndex: false,
    autoSearch: true,
  },
)

const tableKey = ref([] as any)

// 过滤掉序号列，因为我们使用 vxe-table 的内置序号列
const filteredTableKey = computed(() => {
  return tableKey.value.filter((item) => item.key !== 'seq')
})

// 计算序号列的起始值
const seqStartIndex = computed(() => {
  return (page.value - 1) * pageSize.value
})

const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})
const ordersort = ref<string | boolean | null>(null)
const orderby = ref<string | null>(null)

const search = () => {
  page.value = 1
  clearCheckbox()
  tapQueryForm()
}

const maintainSearch = () => {
  clearCheckbox()
  tapQueryForm()
}

const pageChange = () => {
  const obj: any = {}
  obj.page = page.value
  obj.pageSize = pageSize.value
  checkFormParams({ formArr: form.value, obj, callBack: getCustomerList }) // 校验完传入回调 -> 回调执行请求接口
}

// 查询
const tapQueryForm = () => {
  const obj: any = {}
  obj.page = page.value
  obj.pageSize = pageSize.value
  checkFormParams({ formArr: form.value, obj, callBack: getCustomerList }) // 校验完传入回调 -> 回调执行请求接口
}

// table表头排序
const sortChangeEvent = ({ sortList }) => {
  orderby.value = null
  ordersort.value = null
  if (sortList.length != 0) {
    sortList.forEach((x) => {
      orderby.value = x.field
      ordersort.value = x.order // 直接使用 'asc' 或 'desc' 字符串
    })
  }

  page.value = 1
  clearCheckbox()
  tapQueryForm()
}
// 查询表格数据
const getCustomerList = (obj) => {
  obj.sortField = orderby.value
  obj.sortType = ordersort.value
  tableLoading.value = true
  if (props.formFormat) obj = props.formFormat(obj)
  props
    .getList(obj)
    .then(async (res) => {
      const data = res.data.data || res.data.list
      tableLoading.value = false
      if (props.dataAsyncFormat) {
        tableData.value = await props.dataAsyncFormat(data)
      } else {
        tableData.value = (props.dataFormat ? props.dataFormat(data) : data) || []
      }
      total.value = res.data.total
    })
    .catch((error) => {
      tableLoading.value = false
      tableData.value = []
      total.value = 0
      console.error('获取表格数据失败:', error)
    })
}
// 表格设置弹窗显示/隐藏
const visible = ref(false)
// 显示表格设置弹窗
const showTableSetting = () => {
  visible.value = true
}

const checkItemsArr = ref<any[]>([])
const selectChangeEvent = () => {
  const $table = tableRef.value
  // 当前页选中的数据
  const currentSelectedRows = $table?.getCheckboxRecords() || []
  // 其他页选中的数据
  const otherSelectedRows = $table?.getCheckboxReserveRecords() || []
  checkItemsArr.value = [...currentSelectedRows, ...otherSelectedRows]
  if (props.checkCb) {
    props.checkCb(checkItemsArr.value)
  }
}

const clearCheckbox = () => {
  tableRef.value?.clearCheckboxRow()
  tableRef.value?.clearCheckboxReserve()
  selectChangeEvent()
}

// 保存表格设置
const handleSaveTableSetting = async (arr) => {
  tableKey.value = []
  await setTableConfig(arr, props.pageType, lineHeightType.value)
  nextTick(() => {
    tableKey.value = arr
  })
  orderby.value = null
  ordersort.value = null
  search()
}

const handleResetTableSetting = async () => {
  const arr = await initTable(props.pageType, tableRef.value, tableKey, lineHeightType, true)
  orderby.value = null
  ordersort.value = null
  setTableConfig(arr, props.pageType, lineHeightType.value)
  search()
}

// 获取参数
const getParams = () => {
  let obj: any = {}
  form.value.forEach((item: any) => {
    if (item.formKeys && item.formKeys.length && item.value) {
      item.formKeys.forEach((v, i) => {
        obj[v] = item.value[i] || null
      })
    } else obj[item.key] = item.value
  })
  for (const key in obj) {
    if ((!obj[key] && obj[key] != 0) || obj[key].length < 1) {
      obj[key] = null
    }
  }
  if (props.formFormat) obj = props.formFormat(obj)
  return obj
}

onMounted(() => {
  const $table = tableRef.value
  const $toolbar = toolbarRef.value
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
  initTable(props.pageType, tableRef.value, tableKey, lineHeightType, false)
    .then((val) => {
      tableVisble.value = true
      emits('initFinish', val)
      if (props.autoSearch) tapQueryForm()
    })
    .catch((_e) => {})
})

// 监听序号列相关变化，强制刷新表格列
watch(
  () => props.isIndex,
  () => {
    nextTick(() => {
      const $table = tableRef.value
      if ($table) {
        $table.refreshColumn()
      }
    })
  },
  { immediate: false },
)

let isFirst = true
watch(
  () => lineHeightType.value,
  () => {
    if (isFirst) {
      isFirst = false
      return
    }
    setTableConfig(tableKey.value, props.pageType, lineHeightType.value)
  },
)

defineExpose({
  refresh: tapQueryForm,
  tableData,
  checkItemsArr,
  tableRef,
  page,
  pageSize,
  ordersort,
  orderby,
  tableKey,
  lineHeightType,
  search,
  maintainSearch,
  showTableSetting,
  clearCheckbox,
  getParams,
})
</script>

<style lang="scss" scoped>
.table-box {
  position: relative;
  flex: 1;
  border-bottom: 1px solid #ddd;
  --vxe-ui-table-row-striped-background-color: #fafafb;
  --vxe-ui-font-primary-color: #1890ff;
  --vxe-ui-table-header-font-color: #333;
  --vxe-ui-font-color: #333;
  --vxe-ui-table-cell-padding-left: 8px;
  --vxe-ui-table-column-padding-small: 8px 0;
  .toolbarBtn {
    position: absolute;
    right: 0;
    bottom: 100%;
    padding: 0;
    padding-bottom: 0.6em;
    margin-block: -5px;
  }
  .box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // overflow-y: scroll;
    .editbtn {
      color: #1890ff;
      cursor: pointer;
    }
    .movesea {
      margin-left: 20px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.selected-box {
  display: flex;
  align-items: center;
  padding: 5px 12px;
  color: #3d3d3d;
  background-color: #f4f7fe;
  border: 1px solid #409eff;
  border-radius: 4px;

  .selected-text {
    color: #409eff;
  }
}

:deep(.stripe) {
  //  斑马纹
  tr:nth-of-type(even) {
    background-color: #f8f8f8;
  }
}

:deep(.vxe-table--body-wrapper.fixed-left--wrapper) {
  padding-bottom: 16px;
  overflow: hidden !important;
}

:deep(.ant-pagination-options .ant-select) {
  width: 82px;
}

.table-btns {
  display: flex;
  gap: 4px;
  :deep(.ant-btn-text) {
    padding: 2px 4px !important;
    color: #1890ff !important;
    transition: none !important;
    &:hover:not([disabled]) {
      background: linear-gradient(0deg, rgba(255, 255, 255, 0.86), rgba(255, 255, 255, 0.86)), #1890ff !important;
      color: #1890ff !important;
    }
    &:active:not([disabled]) {
      background: linear-gradient(0deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)), #1890ff !important;
      color: #1890ff !important;
    }
    &:disabled {
      color: #999 !important;
    }
  }
}

::v-deep(.treeTable.vxe-table .vxe-body--row.row-green) {
  background-color: #fff;
}
::v-deep(.treeTable.vxe-table .vxe-body--row.row-blue) {
  background-color: rgb(242, 247, 250);
}
::v-deep(.treeTable.vxe-table .vxe-body--row.row-red) {
  background-color: rgb(226, 234, 245);
}
::v-deep(.row--hover) {
  background-color: #f1f8ff !important;
  .copy-btn {
    display: flex !important;
  }
}
::v-deep(.treeTable.vxe-table .row--hover) {
  background-color: rgb(244, 247, 255) !important;
}
// ::v-deep(.vxe-table--body-wrapper .vxe-body--row .vxe-body--column) {
//   padding-top: var(--cell-padding, 8px);
//   padding-bottom: var(--cell-padding, 8px);
//   // line-height: var(--cell-lineheight, 20px);
//   white-space: normal;
//   word-break: break-all;
//   overflow: visible;
// }

:deep(.vxe-body--column.col--ellipsis:not(.col--active)) {
  .vxe-cell {
    text-overflow: clip;
  }
}

:deep(.vxe-header--row) {
  .col--ellipsis {
    .vxe-cell--title {
      text-overflow: clip;
    }
  }
}

// 针对 .cell-content 让内容可换行
::v-deep(.cell-content) {
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  // line-height: var(--cell-lineheight, 20px);
  padding: 0 !important;
  .btnBox {
    margin-left: -4px !important;
  }
  .aLink {
    text-decoration: none !important;
    &:hover {
      text-decoration: underline !important;
    }
  }
}

::v-deep(.vxe-checkbox--icon) {
  font-weight: 400 !important;
  color: #c0c0c0 !important;
}

::v-deep(.is--checked) {
  .vxe-checkbox--icon {
    color: #1890ff !important;
  }
}
::v-deep(.is--indeterminate) {
  .vxe-checkbox--icon {
    color: #1890ff !important;
  }
}

// 处理checkbox 的样式
::v-deep(.col--checkbox) {
  .c--tooltip {
    width: 100% !important;
  }
}

:deep(.vxe-icon-caret-up):not(.sort--active) {
  color: #c0c0c0 !important;
  width: 8px !important;
  height: 4px !important;
  margin-bottom: 2px !important;
  scale: 0.7;
}
:deep(.vxe-icon-caret-down):not(.sort--active) {
  color: #c0c0c0 !important;
  width: 8px !important;
  height: 4px !important;
  margin-bottom: 3px !important;
  scale: 0.7;
}
:deep(.vxe-icon-caret-down) {
  width: 8px !important;
  height: 4px !important;
  margin-bottom: 3px !important;
  scale: 0.7;
}
:deep(.vxe-icon-caret-up) {
  width: 8px !important;
  height: 4px !important;
  margin-bottom: 2px !important;
  scale: 0.7;
}
:deep(.vxe-cell--sort) {
  margin-left: 4px !important;
  padding-inline: 0 !important;
  padding-top: 3px !important;
}
:deep(.vxe-table--fixed-right-wrapper) {
  .vxe-table--body {
    padding-right: 0 !important;
  }
}

::v-deep(.is--sortable) {
  .vxe-cell--title {
    cursor: pointer;
    user-select: none;
    transition: color 0.3s;

    &:hover {
      color: #409eff;
    }
  }
}

:deep(.vxe-cell) {
  &:has(.vxe-render-image) {
    height: 100%;
    padding: 6px;
    display: flex;
    align-items: center;
  }
  .vxe-render-image {
    height: 100%;
    object-fit: cover; /* 保持比例裁剪填充 */
    border-radius: 4px;
  }
}
</style>
