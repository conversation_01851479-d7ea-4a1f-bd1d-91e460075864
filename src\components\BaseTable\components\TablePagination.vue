<template>
  <div class="table-pagination">
    <div class="table-pagination-text">
      <span>共</span>
      <span>{{ total }}</span>
      <span>条</span>
    </div>
    <a-pagination
      show-quick-jumper
      :total="total"
      show-size-changer
      v-model:current="current"
      v-model:page-size="pageSize"
      :item-render="itemRender"
      :page-size-options="pageSizeOptions"
      @change="emits('change')"
      size="small"
    >
      <template #buildOptionText="props">
        <span>{{ props.value }}条/页</span>
      </template>
    </a-pagination>
  </div>
</template>

<script setup lang="ts">
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'

const emits = defineEmits<{
  (e: 'change'): void
}>()

const current = defineModel<number>('current', { required: true })
const pageSize = defineModel<number>('pageSize', { required: true })
const total = defineModel<number>('total', { required: true })

const pageSizeOptions = ref(['20', '50', '100', '250'])

const itemRender = ({ page, type, originalElement }) => {
  // 获取当前分页信息
  const pageCount = Math.ceil(total.value / pageSize.value)

  if (type === 'page') {
    return h(
      'div',
      {
        type: 'default',
        size: 'small',
        class: 'page-button',
        style: {
          width: '100%',
        },
      },
      page,
    )
  }
  if (type === 'prev') {
    return h(
      'div',
      {
        type: 'default',
        size: 'small',
        class: 'ant-pagination-item page-prev',
        disabled: current.value === 1,
      },
      h(LeftOutlined),
    )
  }
  if (type === 'next') {
    return h(
      'div',
      {
        type: 'default',
        size: 'small',
        class: 'ant-pagination-item page-next',
        disabled: current.value === pageCount || pageCount === 0,
      },
      h(RightOutlined),
    )
  }
  return originalElement
}
</script>

<style scoped lang="scss">
.table-pagination {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.table-pagination-text {
  display: flex;
  align-items: center;
  color: #333;
  gap: 8px;
  margin-right: 8px;
}
</style>
