<template>
  <div class="role-group-left-panel">
    <!-- 搜索框 -->
    <div class="search-box">
      <a-input v-model:value="searchValue" placeholder="搜索分组名称" @input="handleSearch" allowClear>
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
    </div>

    <!-- 分组列表 -->
    <div class="group-list">
      <a-spin :spinning="loading">
        <div class="group-item-container">
          <!-- 全部分组 -->
          <div class="group-item" v-if="false" :class="{ active: selectedGroupId === 'all' }" @click="handleSelectGroup('all')">
            <div class="group-content">
              <div class="group-name">全部</div>
              <div class="group-count">({{ totalCount }})</div>
            </div>
          </div>

          <!-- 具体分组 -->
          <div v-for="group in filteredGroupList" :key="group.id" class="group-item" :class="{ active: selectedGroupId === group.id }" @click="handleSelectGroup(group.id)">
            <div class="group-content">
              <div class="group-name" :title="group.group_name">{{ group.group_name }}</div>
              <div class="group-count">({{ group.role_count || 0 }})</div>
            </div>
            <div class="group-actions" v-if="group.id !== null && Number(group.id) > 0">
              <a-tooltip title="编辑分组">
                <EditOutlined class="action-icon" @click.stop="handleEditGroup(group)" />
              </a-tooltip>
              <a-tooltip title="删除分组" v-if="group.id !== null && Number(group.id) > 0">
                <DeleteOutlined class="action-icon delete-icon" @click.stop="handleDeleteGroup(group)" />
              </a-tooltip>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 新增分组按钮 -->
    <div class="add-group-btn">
      <a-button type="primary" block @click="handleCreateGroup">
        <PlusOutlined />
        新增分组
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { SearchOutlined, EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { buttonDebounce } from '@/utils'

interface RoleGroup {
  id: string | number
  group_name: string
  role_count: number
}

const props = defineProps<{
  groupList: RoleGroup[]
  selectedGroupId: string | number | null
  loading?: boolean
}>()

const emit = defineEmits<{
  selectGroup: [groupId: string | number]
  createGroup: []
  editGroup: [group: RoleGroup]
  deleteGroup: [group: RoleGroup]
  search: [value: string]
}>()

const searchValue = ref('')
const filteredGroupList = ref<RoleGroup[]>([])

// 计算总数
const totalCount = computed(() => {
  return props.groupList.reduce((sum, group) => sum + (group.role_count || 0), 0)
})

// 过滤分组
const filterGroups = () => {
  if (!searchValue.value) {
    filteredGroupList.value = props.groupList
  } else {
    filteredGroupList.value = props.groupList.filter((group) => group.group_name.toLowerCase().includes(searchValue.value.toLowerCase()))
  }
}

// 监听分组列表变化，更新过滤列表
watch(
  () => props.groupList,
  () => {
    filterGroups()
  },
  { immediate: true },
)

// 监听搜索值变化
watch(searchValue, () => {
  filterGroups()
})

// 选择分组
const handleSelectGroup = (groupId: string | number) => {
  emit('selectGroup', groupId)
}

// 搜索
const handleSearch = () => {
  emit('search', searchValue.value)
}

// 新增分组
const handleCreateGroup = buttonDebounce(() => {
  emit('createGroup')
}, 500)

// 编辑分组
const handleEditGroup = buttonDebounce((group: RoleGroup) => {
  emit('editGroup', group)
}, 500)

// 删除分组
const handleDeleteGroup = buttonDebounce((group: RoleGroup) => {
  emit('deleteGroup', group)
}, 500)
</script>

<style lang="scss" scoped>
.role-group-left-panel {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 100%;
  padding: 16px;
  padding-left: 0;
  background: #fff;
  border-right: 1px solid #e8e8e8;

  .search-box {
    margin-bottom: 16px;
  }

  .group-list {
    flex: 1;
    margin-bottom: 16px;
    overflow-y: auto;

    .group-item-container {
      .group-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        margin-bottom: 4px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s;

        &:hover {
          background-color: #f5f5f5;

          .group-actions {
            opacity: 1;
          }
        }

        &.active {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }

        .group-content {
          display: flex;
          flex: 1;
          align-items: center;
          min-width: 0;

          .group-name {
            max-width: 120px;
            overflow: hidden;
            font-size: 14px;
            color: #333;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .group-count {
            margin-left: 4px;
            font-size: 12px;
            color: #999;
            white-space: nowrap;
          }
        }

        .group-actions {
          display: flex;
          gap: 8px;
          align-items: center;
          opacity: 0;
          transition: opacity 0.2s;

          .action-icon {
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: color 0.2s;

            &:hover {
              color: #1890ff;
            }

            &.delete-icon:hover {
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }

  .add-group-btn {
    margin-top: auto;
  }
}

// 滚动条样式
.group-list::-webkit-scrollbar {
  width: 4px;
}

.group-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.group-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.group-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
