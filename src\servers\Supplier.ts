import { request } from './request'

// 通过用户id获取供应商信息
export const GetSupplierInfo = () => {
  return request({ url: '/api/Supplier/GetSupplier' }, 'GET', null, false)
}

// 添加修改基础供应商信息
export const saveSupplierSettlement = (data: any) => {
  return request({ url: '/api/Supplier/AddorUpdateSupplierBasic', data }, 'POST')
}

// 保存供应商拓展信息
export const saveSupplierExpand = (data: any) => {
  return request({ url: '/api/Supplier/UpdateSupplierExpand', data }, 'POST')
}

// 保存供应商跟进信息
export const saveSupplierFollow = (data: any) => {
  return request({ url: '/api/Supplier/UpdateSupplierFollow', data }, 'POST')
}

// 添加证书
export const updateSupplierLicenseFileIds = (data: any) => {
  return request({ url: '/api/Supplier/UpdateSupplierLicenseFileIds', data }, 'POST')
}

// OCR识别身份证和营业执照
export const ocrRecognize = (query: any, data: any) => {
  // 构建查询字符串
  const queryString = Object.entries(query)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&')

  // 若存在查询参数，将其添加到 URL 后面
  const url = queryString ? `/api/Files/GetOcrRecognizeFromFile?${queryString}` : '/api/Files/GetOcrRecognizeFromFile'
  return request({ url, data, isFormData: true }, 'POST')
}

// 新增入驻企业
export const newSupplier = () => {
  return request({ url: '/api/Supplier/NewSupplier' }, 'GET')
}

// 供应商入驻保存草稿
export const saveDraftApi = (data: any) => {
  return request({ url: '/api/Supplier/AddSupplier', data })
}

// 获取同意协议状态
export const getAgreement = () => {
  return request({ url: '/api/Supplier/GetAgreement' }, 'GET')
}

// 同意协议
export const addAgreement = () => {
  return request({ url: '/api/Supplier/AddAgreement' }, 'GET')
}

// 通过统一社会信用代码检查供应商是否存在
export const checkSupplier = (data: any) => {
  return request({ url: '/api/Supplier/CheckSupplier', data }, 'GET')
}
