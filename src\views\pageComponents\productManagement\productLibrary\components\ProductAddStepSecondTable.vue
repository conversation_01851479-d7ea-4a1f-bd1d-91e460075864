<template>
  <div class="w-full">
    <vxe-table :data="[{}]" class="w-full" size="small" border>
      <vxe-column v-for="i in item.children" :key="i.name" :field="i.name" :title="i.name" width="250">
        <a-button type="link" size="small" @click="handleSelectFile(i.id)">{{ disabled ? '查看文件' : '点击上传' }}</a-button>
        <a-badge :count="form[i.id]?.length" />
      </vxe-column>
    </vxe-table>
    <ProductFileSelectModal v-if="visible" v-model:visible="visible" :select-file-list="selectFileList" @change="handleChange" :disabled="disabled" />
  </div>
</template>

<script setup lang="ts">
import ProductFileSelectModal from './ProductFileSelectModal.vue'

defineProps<{
  item: any
  disabled?: boolean
}>()

// 选择文件弹窗是否显示
const visible = ref(false)
// 选中的附件列表
const selectFileList = ref<any[]>([])

const selectId = ref<number>()
// 表单
const form = defineModel<any>('form')

const handleSelectFile = (id: number) => {
  selectId.value = id
  visible.value = true
  const fileList = form.value[id]
  selectFileList.value = fileList || []
}

const handleChange = (fileList: any[]) => {
  console.log(fileList)

  if (selectId.value) {
    form.value[selectId.value] = fileList
  }
}
</script>

<style scoped lang="scss"></style>
