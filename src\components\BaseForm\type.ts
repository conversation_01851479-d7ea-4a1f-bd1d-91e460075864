import type { InputProps, SelectProps, TextAreaProps, FormProps, CascaderProps, DatePickerProps, CheckboxGroupProps, RadioGroupProps, InputNumberProps } from 'ant-design-vue'
import type { VNode, CSSProperties } from 'vue'

type CommonHTMLAttributes = {
  class?: string | string[]
  style?: CSSProperties
}

type BaseFormCommon = {
  /** 是否隐藏 */
  hidden?: boolean
  /** 名称 */
  label: string | (() => VNode)
  /** 跨度 最大/默认为24 */
  span?: number
}

type BaseFormComponentCommon = {
  key: string
  /** 组件后的插槽 */
  afterSlot?: () => VNode
  /** ant-design-vue组件的插槽 */
  slots?: {
    [key: string]: () => VNode | VNode[]
  }
}

type BaseFormTitle = {
  /** 类型-标题 */
  type: 'title'
}

type BaseFormText = {
  /** 类型-文本 */
  type: 'text'
  /** 默认值 */
  default?: string
  props?: CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormSlot = {
  /** 类型-插槽 */
  type: 'slot'
  slots: () => VNode
} & Omit<BaseFormComponentCommon, 'slots'>

type BaseFormInput = {
  /** 类型-输入框 */
  type: 'input'
  /** ant-design-vue输入框的属性 */
  props?: Omit<InputProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormInputNumber = {
  /** 类型-输入框 */
  type: 'input-number'
  /** ant-design-vue输入框的属性 */
  props?: Omit<InputProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormAInputNumber = {
  /** 类型-输入框 */
  type: 'a-input-number'
  /** ant-design-vue输入框的属性 */
  props?: Omit<InputNumberProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormSelect = {
  /** 类型-选择框 */
  type: 'select'
  /** ant-design-vue选择框的属性 */
  props?: Omit<SelectProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormTextarea = {
  /** 类型文本框 */
  type: 'textarea'
  /** ant-design-vue文本框的属性 */
  props?: Omit<TextAreaProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormCascader = {
  /** 类型-级联选择 */
  type: 'cascader'
  /** ant-design-vue级联选择框的属性 */
  props?: Omit<CascaderProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormDatePicker = {
  /** 类型-日期选择器 */
  type: 'date-picker'
  /** ant-design-vue日期选择器的属性 */
  props?: Omit<DatePickerProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormRadioGroup = {
  /** 类型-单选框组 */
  type: 'radio-group'
  /** ant-design-vue单选框组的属性 */
  props?: Omit<RadioGroupProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

type BaseFormCheckbox = {
  /** 类型-多选框 */
  type: 'checkbox-group'
  /** ant-design-vue多选框的属性 */
  props?: Omit<CheckboxGroupProps, keyof CommonHTMLAttributes> & CommonHTMLAttributes
} & BaseFormComponentCommon

/** 组件 */
type BaseComponent =
  | BaseFormInput
  | BaseFormSelect
  | BaseFormTextarea
  | BaseFormSlot
  | BaseFormTitle
  | BaseFormText
  | BaseFormCascader
  | BaseFormInputNumber
  | BaseFormAInputNumber
  | BaseFormDatePicker
  | BaseFormRadioGroup
  | BaseFormCheckbox

/** 表单项 */
export type BaseFormItem = BaseFormCommon & BaseComponent

export interface BaseFormProps extends FormProps {
  /** 表单数据 */
  modelValue: Ref<any>
  /** 表单配置 */
  formConfig: Ref<BaseFormItem[]>
  /** 是否为文本 */
  isText?: Ref<boolean> | boolean | undefined
  rules?: any
}
