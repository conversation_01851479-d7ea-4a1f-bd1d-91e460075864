import { request } from './request'
import { get } from './mockRequest'
// 商品库列表
export const GetList = (data: any) => request({ url: '/api/ProductInfo/GetList', data }, 'POST')

// 商品库详情
export const GetDetail = () => get('/product/product/detail')

// 商品类目选项
export const GetCategoryOption = () => request({ url: '/plm/Category/GetCategorySelectOption', data: { type: 1 } }, 'POST')

// 商品类目模板
export const GetCategoryTemplate = (data) => request({ url: '/plm/CategoryTemplate/GetCategoryTemplateDetail', data }, 'POST')

// 获取计量单位选项
export const GetMeteringUnitSelectOption = () => request({ url: '/plm/CategoryTemplate/GetMeteringUnitSelectOption' }, 'POST')

// 添加商品信息
export const CreateProduct = (data) => request({ url: '/api/ProductInfo/Create', data }, 'POST')

// 通过id获取单个商品信息
export const GetProduct = (data) => request({ url: '/api/ProductInfo/Get', data }, 'GET')

// 更新商品信息
export const UpdateProduct = (data) => request({ url: '/api/ProductInfo/Update', data }, 'POST')

// 启用停用
export const EnableProduct = (data) => request({ url: '/api/ProductInfo/UpdateStatus', data }, 'GET')
// 导出商品
export const ExportProduct = (data) => request({ url: '/api/ProductInfo/Export', data, responseType: 'blob' })
// 获取商品预览地址
export const GetProductPreviewUrl = (data) => request({ url: '/api/Files/PreViewUrlByFileId', data }, 'GET')

// 下载图片文件压缩包
export const DownloadImagesZip = (data) => request({ url: `/api/ProductInfo/DownloadImagesZip`, responseType: 'blob', data }, 'POST')

// 获取草稿列表
export const GetPublishStatusList = (data) => request({ url: 'api/ProductInfo/GetPublishStatusList', data }, 'GET')

// 删除草稿
export const DeleteDraft = (data) => request({ url: '/api/ProductInfo/DeleteDraft', data }, 'GET')

// 获取红点数
export const GetLabelStatusCount = (data) => request({ url: '/api/ProductInfo/GetLabelStatusCount', data }, 'GET')
