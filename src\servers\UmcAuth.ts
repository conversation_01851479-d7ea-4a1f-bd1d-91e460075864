// UMC认证模块
import { requestXY } from './request'

/**
 * 配置说明：
 * USE_FIXED_BACKEND_URL: 是否使用固定后端地址
 * FIXED_BACKEND_URL: 固定的后端地址
 */
const USE_FIXED_BACKEND_URL = false
const FIXED_BACKEND_URL = import.meta.env.VITE_APP_BASE_API
// UMC 登录重定向 - 获取UMC登录页面URL
export const LoginRedirect = (ip?: string) => {
  if (USE_FIXED_BACKEND_URL) {
    const url = ip ? `${FIXED_BACKEND_URL}/XY/UmcAuth/LoginRedirect?ip=${encodeURIComponent(ip)}` : `${FIXED_BACKEND_URL}/XY/UmcAuth/LoginRedirect`
    return Promise.resolve({
      success: true,
      data: url,
    })
  }

  // 构建请求参数
  const params = ip ? { ip } : {}
  return requestXY({ url: '/UmcAuth/LoginRedirect', data: params }, 'GET')
}

// UMC 登录回调
export const LoginCallback = async (data: { code: string; scope?: string; state?: string; session_state?: string }) => {
  if (USE_FIXED_BACKEND_URL) {
    // 使用固定地址时的处理逻辑
    const axios = await import('axios')
    const umcService = axios.default.create({
      baseURL: FIXED_BACKEND_URL,
      withCredentials: true,
      timeout: 60000,
    })

    // 添加LoginToken到请求头
    umcService.interceptors.request.use((config) => {
      let userData = localStorage.getItem('userData') || ''
      let LoginToken = ''
      if (userData) {
        userData = JSON.parse(userData) as any
        LoginToken = (userData as any).login_token || ''
      }
      if (config.headers) {
        config.headers.LoginToken = LoginToken
      }
      return config
    })

    // 构建请求URL
    const params = new URLSearchParams()
    params.append('code', data.code)
    if (data.scope) params.append('scope', data.scope)
    if (data.state) params.append('state', data.state)
    if (data.session_state) params.append('session_state', data.session_state)

    const requestUrl = `/XY/UmcAuth/LoginCallback?${params.toString()}`
    const response = await umcService.get(requestUrl)

    // 转换响应格式
    const result = response.data || {}
    return {
      success: (result.code === 0 || result.code === 1) && result.success,
      data: result.data,
      message: result.message,
      code: result.code, // 添加错误代码字段
    }
  }
  return requestXY({ url: '/UmcAuth/LoginCallback', data }, 'GET')
}

// UMC 退出登录重定向
export const LogoutRedirect = async (loginToken?: string) => {
  try {
    console.log('=== 开始调用LogoutRedirect接口 ===')
    console.log('环境变量:', import.meta.env.VITE_APP_ENV)

    // 获取LoginToken - 优先级：传入参数 > sessionStorage > localStorage
    let LoginToken = loginToken || ''

    // 如果没有传入token，先从sessionStorage获取（用于权限错误时的退出登录）
    if (!LoginToken) {
      LoginToken = sessionStorage.getItem('logoutLoginToken') || ''
    }
    if (!LoginToken) {
      let userData = localStorage.getItem('userData') || ''
      if (userData) {
        userData = JSON.parse(userData) as any
        LoginToken = (userData as any).login_token || ''
      }
    }

    const baseURL = import.meta.env.VITE_APP_ENV === 'development' ? '/XY' : `${(window as any).serverConfig.VITE_APP_BASE_API}/XY`

    console.log('=== 准备发送请求 ===')
    console.log('请求URL:', `${baseURL}/UmcAuth/LogoutRedirect`)
    console.log('请求头LoginToken:', LoginToken ? `${LoginToken.substring(0, 20)}...` : '无')

    // 直接调用axios来处理可能的重定向
    const axios = await import('axios')

    try {
      const response = await axios.default({
        method: 'GET',
        url: `${baseURL}/UmcAuth/LogoutRedirect`,
        headers: {
          LoginToken,
        },
        withCredentials: true,
        timeout: 60000,
        // 不自动跟随重定向，我们需要获取重定向的URL
        maxRedirects: 0,
        // 自定义状态验证，让302也被认为是成功
        validateStatus(status) {
          return status >= 200 && status < 400
        },
      })

      console.log('=== 收到响应 ===')
      console.log('响应状态:', response.status)
      console.log('响应头:', response.headers)
      console.log('响应数据:', response.data)

      // 如果是302重定向，从Location头获取重定向URL
      if (response.status === 302) {
        const location = response.headers.location || response.headers.Location
        console.log('=== 检测到302重定向 ===')
        console.log('Location头:', location)
        if (location) {
          console.log('=== 成功获取重定向URL ===')
          console.log('重定向URL:', location)
          return {
            success: true,
            data: location,
            message: '获取退出登录地址成功',
          }
        }
        console.log('=== 302重定向但没有Location头 ===')
      }

      // 如果是200响应，检查响应数据类型
      const result = response.data
      console.log('=== 处理200响应 ===')
      console.log('响应数据类型:', typeof result)
      console.log('响应数据内容:', result)
      console.log('=== 跳转到UMC退出登录页面 ===', response.data)
      window.location.href = response.data

      // 如果响应是字符串且包含UMC域名，说明是直接返回的退出登录URL
      if (typeof result === 'string' && result.includes('umc.dev.local.westmonth.cn')) {
        console.log('=== 检测到直接返回的UMC退出登录URL ===')
        return {
          success: true,
          data: result,
          message: '获取退出登录地址成功',
        }
      }

      // 否则按JSON格式处理
      return {
        success: (result.code === 0 || result.code === 1) && result.success,
        data: result.data,
        message: result.message,
      }
    } catch (error: any) {
      console.log('=== 请求发生错误 ===')
      console.log('错误对象:', error)
      console.log('错误响应状态:', error.response?.status)
      console.log('错误响应头:', error.response?.headers)
      console.log('错误响应数据:', error.response?.data)
      console.log('错误消息:', error.message)
      console.log('错误代码:', error.code)

      // 如果是302重定向导致的"错误"，从error.response获取重定向URL
      if (error.response && error.response.status === 302) {
        const location = error.response.headers.location || error.response.headers.Location
        console.log('=== 从错误中检测到302重定向 ===')
        console.log('Location头:', location)
        if (location) {
          console.log('=== 从错误中成功获取重定向URL ===')
          console.log('重定向URL:', location)
          return {
            success: true,
            data: location,
            message: '获取退出登录地址成功',
          }
        }
      }

      // 显示详细的CORS错误信息
      if (error.code === 'ERR_NETWORK' || error.message.includes('CORS')) {
        console.log('=== CORS跨域错误 ===')
        console.log('这是一个跨域请求错误，浏览器阻止了请求')
      }

      throw error
    }
  } catch (error) {
    console.error('LogoutRedirect接口调用失败:', error)
    return {
      success: false,
      data: null,
      message: '获取退出登录地址失败',
    }
  }
}

// 获取UMC退出登录URL（用于直接跳转）
export const getLogoutRedirectUrl = (loginToken: string) => {
  const baseURL = import.meta.env.VITE_APP_ENV === 'development' ? '/XY' : `${(window as any).serverConfig.VITE_APP_BASE_API}/XY`
  return `${baseURL}/UmcAuth/LogoutRedirect?loginToken=${encodeURIComponent(loginToken)}`
}

// UMC 退出登录回调
export const LogoutCallback = (
  params?: {
    app_key?: string
    timestamp?: string
    signature?: string
    accountId?: string
  },
  loginToken?: string,
) => {
  // 如果没有传入参数，尝试从URL中获取
  if (!params) {
    const urlParams = new URLSearchParams(window.location.search)
    params = {
      app_key: urlParams.get('app_key') || '',
      timestamp: urlParams.get('timestamp') || '',
      signature: urlParams.get('signature') || '',
      accountId: urlParams.get('accountId') || urlParams.get('accountld') || '', // 注意可能是accountld
    }
  }

  // 获取LoginToken - 优先使用传入的参数，否则从localStorage获取
  let LoginToken = loginToken || ''
  if (!LoginToken) {
    let userData = localStorage.getItem('userData') || ''
    if (userData) {
      userData = JSON.parse(userData) as any
      LoginToken = (userData as any).login_token || ''
    }
  }

  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('LogoutCallback - LoginToken:', LoginToken ? `${LoginToken.substring(0, 20)}...` : '无')
    console.log('LogoutCallback - UMC参数:', params)
  }

  // 构建URL查询参数（不包含accountId）
  const queryParams = new URLSearchParams()
  if (params.app_key) queryParams.append('app_key', params.app_key)
  if (params.timestamp) queryParams.append('timestamp', params.timestamp)
  if (params.signature) queryParams.append('signature', params.signature)

  const url = `/UmcAuth/LogoutCallback?${queryParams.toString()}`

  // 在请求体中以JSON格式传递accountId
  const requestBody = {
    accountId: params.accountId,
  }

  // 在请求头中添加LoginToken和Content-Type
  return requestXY({ url, data: requestBody }, 'POST', {
    LoginToken,
    'Content-Type': 'application/json',
  })
}

// UMC 切换用户
export const Switch = (data: { id: string }) => {
  return requestXY({ url: '/UmcAuth/Switch', data }, 'POST')
}

// === 以下接口已迁移到新的 UserInfo.ts 文件 ===

// 获取当前用户账号列表 - 使用新接口
export { GetUserAccountList } from './UserInfo'

// 获取当前用户信息（备用接口，Switch接口已包含完整信息）- 使用新接口
export { GetUserInfo } from './UserInfo'

// 获取用户权限信息（备用接口，Switch接口已包含完整信息）- 使用新接口
export { GetUserPermissions } from './UserInfo'

// 检查LoginToken是否过期（10分钟）
export const checkLoginTokenExpiry = () => {
  const userData = localStorage.getItem('userData')
  if (!userData) return true

  try {
    const userDataObj = JSON.parse(userData)
    const loginTime = userDataObj.login_time || 0
    const currentTime = Math.floor(Date.now() / 1000)
    const expireTime = 10 * 60 // 10分钟

    return currentTime - loginTime > expireTime
  } catch (error) {
    console.error('检查LoginToken过期时间失败:', error)
    return true
  }
}

// 刷新UMC access_token
export const refreshUmcToken = async () => {
  const userData = localStorage.getItem('userData')
  if (!userData) return { success: false, message: '没有用户数据' }

  try {
    const userDataObj = JSON.parse(userData)
    const refreshToken = userDataObj.refresh_token || ''

    if (!refreshToken) {
      return { success: false, message: '没有refresh_token' }
    }

    if (USE_FIXED_BACKEND_URL) {
      const axios = await import('axios')
      const umcService = axios.default.create({
        baseURL: FIXED_BACKEND_URL,
        withCredentials: true,
        timeout: 60000,
      })

      const response = await umcService.post('/XY/UmcAuth/RefreshToken', {
        refresh_token: refreshToken,
      })

      const result = response.data || {}
      return {
        success: (result.code === 0 || result.code === 1) && result.success,
        data: result.data,
        message: result.message,
      }
    }
    return requestXY(
      {
        url: '/UmcAuth/RefreshToken',
        data: { refresh_token: refreshToken },
      },
      'POST',
    )
  } catch (error) {
    console.error('刷新UMC token失败:', error)
    return { success: false, message: '刷新token失败' }
  }
}
// UMC 退出登录V4 - 直接调用接口退出，不需要跳转
export const LogoutV4 = (loginToken?: string) => {
  // 获取LoginToken - 优先级：传入参数 > sessionStorage > localStorage
  let LoginToken = loginToken || ''

  // 如果没有传入token，先从sessionStorage获取
  if (!LoginToken) {
    LoginToken = sessionStorage.getItem('logoutLoginToken') || ''
  }

  // 如果sessionStorage中也没有，再从localStorage获取
  if (!LoginToken) {
    let userData = localStorage.getItem('userData') || ''
    if (userData) {
      try {
        userData = JSON.parse(userData) as any
        LoginToken = (userData as any).login_token || ''
      } catch (e) {
        console.warn('解析userData失败:', e)
      }
    }
  }

  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('LogoutV4 - LoginToken:', LoginToken ? `${LoginToken.substring(0, 20)}...` : '无')
  }

  // 调用LogoutV4接口
  return requestXY(
    {
      url: '/UmcAuth/LogoutV4',
      data: { token: LoginToken },
    },
    'POST',
  )
}
