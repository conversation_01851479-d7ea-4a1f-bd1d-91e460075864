<template>
  <a-modal title="发布状态列表" :width="800" v-model:open="visible" :maskClosable="false" :footer="false">
    <vxe-table :data="tableData" size="small" max-height="40vh">
      <vxe-column field="publish_status_string" title="发布状态"></vxe-column>
      <vxe-column field="modifier" title="修改人"></vxe-column>
      <vxe-column field="modified_at" title="修改时间"></vxe-column>
      <vxe-column field="operate" title="操作" :width="150">
        <template #default="{ row }">
          <a-space>
            <a-button size="small" @click="handleView(row.id, row.publish_status === 0)">查看</a-button>
            <a-button v-if="row.publish_status === 0" size="small" @click="handelDelete">删除</a-button>
          </a-space>
        </template>
      </vxe-column>
    </vxe-table>
  </a-modal>
  <ProductViewDrawer v-if="detailVisible" v-model:visible="detailVisible" :id="id" :isEdit="isEdit" />
</template>

<script setup lang="ts">
import { GetPublishStatusList, DeleteDraft } from '@/servers/ProductLibrary'
import { message } from 'ant-design-vue'
import ProductViewDrawer from './ProductViewDrawer.vue'

const props = defineProps<{
  id: number
}>()

const isEdit = ref(false)

const emits = defineEmits(['query'])

const { proxy }: any = getCurrentInstance()
const visible = defineModel('visible', { required: true })

// 详情显示
const detailVisible = ref(false)
const selectId = ref()

const tableData = ref<any[]>([])

// 获取发布状态列表
const getReleaseList = async () => {
  const res = await GetPublishStatusList({ id: props.id })
  tableData.value = res.data
}

// 查看详情
const handleView = (id: number, isEditVal: boolean) => {
  selectId.value = id
  isEdit.value = isEditVal
  detailVisible.value = true
}

// 删除
const handelDelete = () => {
  proxy.$confirm.show({
    title: '删除草稿',
    content: h('div', {}, [h('div', {}, '即将删除该草稿，删除后：'), h('div', {}, '此操作不可恢复，确定要删除该草稿吗？')]),
    onConfirm: () => {
      DeleteDraft({ id: props.id }).then(() => {
        message.success('删除成功')
        if (tableData.value.length === 1) {
          tableData.value = []
        } else {
          getReleaseList()
        }
        emits('query')
      })
    },
  })
}

onMounted(() => {
  getReleaseList()
})
</script>

<style scoped lang="scss"></style>
