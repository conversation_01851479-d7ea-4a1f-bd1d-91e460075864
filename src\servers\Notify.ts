// 系统通知管理接口 - 新的 /XY/Notify/ 路径
import { requestXY } from './request'

// 获取通知相关下拉框
export const GetDropsSource = () => {
  return requestXY({ url: '/Notify/GetDropsSource' }, 'GET')
}

// 添加系统通知草稿
export const Add = (data) => {
  // 字段映射：旧字段名 -> 新字段名
  const params = { ...data }
  if (params.notice_type !== undefined) {
    params.type = params.notice_type
    delete params.notice_type
  }
  if (params.scheduled_publish_at !== undefined) {
    params.plan_publish_time = params.scheduled_publish_at
    delete params.scheduled_publish_at
  }
  if (params.expired_at !== undefined) {
    params.exp_time = params.expired_at
    delete params.expired_at
  }
  return requestXY({ url: '/Notify/Add', data: params }, 'POST')
}

// 删除系统通知
export const Delete = (data) => {
  return requestXY({ url: '/Notify/Delete', data }, 'POST')
}

// 编辑系统通知草稿
export const Update = (data) => {
  // 字段映射：旧字段名 -> 新字段名
  const params = { ...data }
  if (params.notice_type !== undefined) {
    params.type = params.notice_type
    delete params.notice_type
  }
  if (params.scheduled_publish_at !== undefined) {
    params.plan_publish_time = params.scheduled_publish_at
    delete params.scheduled_publish_at
  }
  if (params.expired_at !== undefined) {
    params.exp_time = params.expired_at
    delete params.expired_at
  }
  return requestXY({ url: '/Notify/Update', data: params }, 'POST')
}

// 获取系统通知列表
export const GetList = (data) => {
  // 字段映射：旧字段名 -> 新字段名
  const params = { ...data }

  // 时间范围字段映射
  if (params.publish_start_at !== undefined) {
    params.published_start = params.publish_start_at
    delete params.publish_start_at
  }
  if (params.publish_end_at !== undefined) {
    params.published_end = params.publish_end_at
    delete params.publish_end_at
  }
  if (params.create_start_at !== undefined) {
    params.created_start = params.create_start_at
    delete params.create_start_at
  }
  if (params.create_end_at !== undefined) {
    params.created_end = params.create_end_at
    delete params.create_end_at
  }
  if (params.update_start_at !== undefined) {
    params.modified_start = params.update_start_at
    delete params.update_start_at
  }
  if (params.update_end_at !== undefined) {
    params.modified_end = params.update_end_at
    delete params.update_end_at
  }
  params.status = '30'
  // 状态字段映射
  if (params.notice_status_list !== undefined && params.notice_status_list !== null && params.notice_status_list !== '') {
    // 新API期望的是字符串状态值
    if (Array.isArray(params.notice_status_list)) {
      // 如果是数组（多选），取第一个值
      params.status = params.notice_status_list.length > 0 ? params.notice_status_list[0] : undefined
    } else {
      // 如果是单个值，直接使用
      params.status = params.notice_status_list
    }
    delete params.notice_status_list
  }

  return requestXY({ url: '/Notify/GetList', data: params }, 'POST').then((response) => {
    // 响应字段映射：新字段名 -> 旧字段名
    if (response && response.data && response.data.list) {
      response.data.list = response.data.list.map((item) => {
        const mappedItem = { ...item }
        if (item.type !== undefined) {
          mappedItem.notice_type = item.type
        }
        if (item.plan_publish_time !== undefined) {
          mappedItem.scheduled_publish_at = item.plan_publish_time
        }
        if (item.exp_time !== undefined) {
          mappedItem.expired_at = item.exp_time
        }
        if (item.publish_time !== undefined) {
          mappedItem.publish_at = item.publish_time
        }
        if (item.created !== undefined) {
          mappedItem.create_at = item.created
        }
        if (item.modified !== undefined) {
          mappedItem.update_at = item.modified
        }
        // 状态字段映射
        if (item.status !== undefined) {
          mappedItem.notice_status = item.status
        }
        // 编码字段映射
        if (item.no !== undefined) {
          mappedItem.code = item.no
        }
        return mappedItem
      })
    }
    return response
  })
}

// 保存并发布系统通知
export const SaveAndPublish = (data) => {
  // 字段映射：旧字段名 -> 新字段名
  const params = { ...data }
  if (params.notice_type !== undefined) {
    params.type = params.notice_type
    delete params.notice_type
  }
  if (params.scheduled_publish_at !== undefined) {
    params.plan_publish_time = params.scheduled_publish_at
    delete params.scheduled_publish_at
  }
  if (params.expired_at !== undefined) {
    params.exp_time = params.expired_at
    delete params.expired_at
  }
  return requestXY({ url: '/Notify/SaveAndPublish', data: params }, 'POST')
}

// 发布系统通知
export const Publish = (data) => {
  return requestXY({ url: '/Notify/Publish', data }, 'POST')
}

// 批量发布系统通知
export const BatchPublishs = (data) => {
  // 兼容旧的参数格式：将 notice_ids 转换为 ids
  const params = { ...data }
  if (params.notice_ids && !params.ids) {
    params.ids = params.notice_ids
    delete params.notice_ids
  }
  return requestXY({ url: '/Notify/BatchPublishs', data: params }, 'POST')
}

// 撤回通知
export const Revocation = (data) => {
  return requestXY({ url: '/Notify/Revocation', data }, 'POST')
}

// 获取通知详情
export const GetNotifyDetails = (params) => {
  // 如果传入的是对象，提取id；如果是直接的id值，直接使用
  const id = typeof params === 'object' && params !== null ? params.id : params
  return requestXY({ url: `/Notify/GetNotifyDetails?id=${id}` }, 'POST').then((response) => {
    // 响应字段映射：新字段名 -> 旧字段名
    if (response && response.data) {
      const data = response.data
      if (data.type !== undefined) {
        data.notice_type = data.type
      }
      if (data.plan_publish_time !== undefined) {
        data.scheduled_publish_at = data.plan_publish_time
      }
      if (data.exp_time !== undefined) {
        data.expired_at = data.exp_time
      }
      if (data.publish_time !== undefined) {
        data.publish_at = data.publish_time
      }
      if (data.created !== undefined) {
        data.create_at = data.created
      }
      if (data.modified !== undefined) {
        data.update_at = data.modified
      }
      // 状态字段映射
      if (data.status !== undefined) {
        data.notice_status = data.status
      }
    }
    return response
  })
}

// 获取最新通知内容
export const GetNewNotify = () => {
  return requestXY({ url: '/Notify/GetNewNotify' }, 'GET').then((response) => {
    // 根据API文档，新接口返回的是字符串，但前端期望的是对象格式
    // 如果返回的是字符串，需要转换为前端期望的格式
    if (response && typeof response.data === 'string') {
      // 如果是字符串格式，转换为对象格式以保持兼容性
      response.data = {
        content: response.data,
        id: null, // 新接口可能不返回id，设置为null
      }
    }
    return response
  })
}

// === 以下是为了兼容旧接口而添加的别名接口 ===

// 批量发布系统通知 - 兼容旧接口名
export const BatchPublishNotify = BatchPublishs

// 获取通知列表 - 兼容旧接口名
export const GetnoticeList = GetList

// 新增保存系统通知 - 兼容旧接口名
export const AddNotification = Add

// 编辑保存系统通知 - 兼容旧接口名
export const UpdateNotification = Update

// 撤回系统通知 - 兼容旧接口名
export const revocation = Revocation

// 删除系统通知 - 兼容旧接口名
export const deleteNotification = Delete

// 获取系统通知详情 - 兼容旧接口名
export const DetailByEdit = GetNotifyDetails
