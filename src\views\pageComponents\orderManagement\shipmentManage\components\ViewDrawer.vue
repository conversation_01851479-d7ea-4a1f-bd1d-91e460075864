<template>
  <a-drawer width="1200" @close="handleClose" v-model:open="openDrawer">
    <template #title>
      <div class="flex justify-between items-center">
        <span>发货单详情</span>
        <div class="flex gap-2">
          <a-button @click="handleChangeInfo('prev')" :disabled="currentIdx === 0">上一条</a-button>
          <a-button @click="handleChangeInfo('next')" class="ml-5" :disabled="currentIdx === tableIds.length - 1">下一条</a-button>
        </div>
      </div>
    </template>
    <div>
      <a-form ref="formRef" :model="shipmentDetails" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }" layout="vertical" :rules="rules">
        <div class="drawer-title">发货单基本信息</div>
        <a-row :gutter="24" class="pl-20 pr-20">
          <a-col :span="6">
            <a-form-item>
              <template #label>
                <a-space>
                  <span class="c-#999">预约单号</span>
                  <CopyOutlined v-if="shipmentDetails.booking_order_number" class="c-primary cursor-pointer" v-copy="shipmentDetails.booking_order_number" />
                </a-space>
              </template>
              <span>{{ shipmentDetails.booking_order_number || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item>
              <template #label>
                <a-space>
                  <span class="c-#999">关联采购单号</span>
                  <CopyOutlined v-if="shipmentDetails.purchase_order_numbers" class="c-primary cursor-pointer" v-copy="shipmentDetails.purchase_order_numbers?.join(', ')" />
                </a-space>
              </template>
              <span>{{ shipmentDetails.purchase_order_numbers?.join(', ') || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="收货仓库">
              <span>{{ shipmentDetails.warehouse_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="供应商">
              <span>{{ shipmentDetails.company_supplier_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="发货单状态">
              <span>{{ shipmentDetails.audit_status_str || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="预计到货日期">
              <span>{{ shipmentDetails.scheduled_arrival_time || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="创建人">
              <span>{{ shipmentDetails.creator_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="创建时间">
              <span>{{ shipmentDetails.create_at || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="实际到货时间">
              <span>{{ shipmentDetails.actual_arrival_time || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="到货凭证">
              <span class="c-primary cursor-pointer" v-if="shipmentDetails.files" @click="onClickFiles">查看附件</span>
              <span v-else>-</span>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="drawer-title">
          <a-space>
            <span class="font-size-14 color-#333">收货信息</span>
            <CopyOutlined
              class="c-primary cursor-pointer"
              v-copy="
                () => `收货人：${shipmentDetails.warehouse_receiver_name || ''}，联系电话：${shipmentDetails.warehouse_receiver_phone || ''}，收货地址：${shipmentDetails.receiver_address || ''}`
              "
            />
          </a-space>
        </div>
        <a-row :gutter="24" class="pl-20 pr-20">
          <a-col :span="6">
            <a-form-item label="收货人">
              <span>{{ shipmentDetails.warehouse_receiver_name || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="联系电话">
              <span>{{ shipmentDetails.warehouse_receiver_phone || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="收货地址">
              <span>{{ shipmentDetails.receiver_address || '-' }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="drawer-title">物流信息</div>
        <a-row :gutter="24" class="pl-20 pr-20">
          <a-col :span="6">
            <a-form-item label="物流公司" name="logistics_company_no">
              <a-select
                v-if="formType == 'editLogist'"
                v-model:value="shipmentDetails.logistics_company_no"
                show-search
                placeholder="请选择物流公司"
                style="width: 200px"
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                :options="logisticsCompanyList"
                @search="handleSearchLoistics"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                allow-clear
                @focus="handleFocusLoistics"
              ></a-select>
              <span v-else>{{ shipmentDetails.logistics_company || '-' }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="物流单号" name="tracking_number">
              <a-input v-if="formType == 'editLogist'" placeholder="请输入物流单号" v-model:value="shipmentDetails.tracking_number"></a-input>
              <span v-else>{{ shipmentDetails.tracking_number || '-' }}</span>
            </a-form-item>
          </a-col>
        </a-row>
        <div class="drawer-title">发货&入库明细</div>
        <vxe-table
          :data="tableData"
          border
          size="small"
          class="!text-12px overflow-x-auto"
          max-height="400"
          show-overflow
          :row-config="{ isHover: true, height: 72 }"
          :column-config="{
            resizable: true,
          }"
        >
          <vxe-column type="seq" title="序号" width="60" align="center"></vxe-column>
          <vxe-column field="sku_name" title="商品" width="350">
            <template #default="{ row }">
              <div class="flex">
                <BaseImage :src="row.image_url" :width="60" :height="60" />
                <div class="flex flex-col ml-4">
                  <div class="lh-20">{{ row.sku_name }}</div>
                  <div class="lh-20">
                    <span class="c-#999">类目：</span>
                    <span>{{ row.all_category || '--' }}</span>
                  </div>
                  <div class="lh-20">
                    <span class="c-#999">规格：</span>
                    <span>{{ row.type_specification || '--' }}</span>
                  </div>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="srs_supplier_prod_code" title="商品编码" min-width="200">
            <template #default="{ row }">
              <div>
                <div>
                  <span class="c-#999">供应商：</span>
                  <span>{{ row.srs_supplier_prod_code || '--' }}</span>
                </div>
                <div>
                  <span class="c-#999">平台：</span>
                  <span>{{ row.srs_platform_prod_code || '--' }}</span>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="type_specification" title="规格" width="120"></vxe-column>
          <vxe-column field="total_purchase_quantity" title="采购总数" width="120"></vxe-column>
          <vxe-column field="total_purchase_scheduled_quantity" title="已发货数(已预约入库数)" width="180"></vxe-column>
          <vxe-column field="scheduled_quantity" title="该次发货数(该次预约入库数)" width="180"></vxe-column>
          <vxe-column field="total_actual_inbound" title="入库数" width="120"></vxe-column>
        </vxe-table>
      </a-form>
    </div>
    <template #footer v-if="formType == 'editLogist'">
      <a-space>
        <a-button type="primary" @click="onSubmit">确定</a-button>
        <a-button style="margin-right: 8px" @click="handleClose">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
  <a-modal v-model:open="openModal" title="附件" :footer="null">
    <div class="min-h-[250px]">
      <div v-for="item in shipmentDetails.files" :key="item.id" class="cursor-pointer">
        <a-space class="cursor-point" @click="previewAuthFile(item)">
          <LinkOutlined />
          <span class="text-blue-500 font-size-14">{{ item.name }}</span>
        </a-space>
      </div>
    </div>
  </a-modal>
  <!-- 图片预览组件 -->
  <a-image
    :width="0"
    :style="{ display: 'none' }"
    :preview="{
      visible: imageVisible,
      onVisibleChange: setImageVisible,
    }"
    :src="previewImageUrl"
  />
</template>

<script setup lang="ts">
import { getShipOrderDetail, getShipOrderDetailList, getLogisticsCompanyList, editLogistics } from '@/servers/ShipmentManage'
import BaseImage from '@/components/BaseImage/index.vue'
import { message } from 'ant-design-vue'
import { LinkOutlined, CopyOutlined } from '@ant-design/icons-vue'

const VITE_APP_ENV = ref(import.meta.env.VITE_APP_ENV)
const userData = ref(JSON.parse(localStorage.getItem('userData') || '{}'))
// 发货单详情
const shipmentDetails = ref<any>({
  logistics_company_no: '',
  tracking_number: '',
})
// 发货&入库明细数据
const tableData = ref<any[]>([
  {
    images_view_url: 'https://img.yzcdn.cn/vant/ipad.png',
    product_name: 'aa',
    category_name: '商品aa类目',
    supplier_product_number: '供应商商品编码aaaa',
    product_number: '平台商品编码XXXXX',
    specification: '规格aaaa',
    purchase_total_quantity: '采购总数aaaa',
    shipment_quantity: '已发货数(已预约入库数)aaaa',
    purchase_price: '该次发货数(该次预约入库数)aaaa',
    in_stock_quantity: '入库数aaaa',
  },
])
// 抽屉是否打开
const openDrawer = ref(false)
const tableIds = ref<number[]>([])
// 当前的id
const selectId = ref()
const companySupplierId = ref()
// 表单类型：detail、editLogist
const formType = ref('detail')
const formRef = ref()
const logisticsCompanyList = ref<any[]>([])
const editLogistRules = {
  logistics_company_no: [{ required: true, message: '请选择物流公司' }],
  tracking_number: [{ required: true, message: '请输入物流单号', trigger: ['blur', 'change'] }],
}
const openModal = ref(false)
const imageVisible = ref(false)
const previewImageUrl = ref('')

const rules = ref()

// 当前的索引（第几条数据）
const currentIdx = computed(() => {
  return tableIds.value.findIndex((id) => id === selectId.value)
})

// 显示抽屉
const showDrawer = async (id: number, company_supplier_id: number, ids: number[], type: string) => {
  formType.value = type
  selectId.value = id
  openDrawer.value = true
  tableIds.value = ids
  companySupplierId.value = company_supplier_id
  // 不需要传company_supplier_id
  // const params = { id, company_supplier_id }
  const params = { id }
  console.log('showDrawer-params', params)
  getShipmentDetailList(params)
  await getShipmentDetail(params)
  if (type == 'editLogist') {
    rules.value = JSON.parse(JSON.stringify(editLogistRules))
    initLogisticsCompanyList()
  }
}

// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
  formRef.value.resetFields()
}

const onSubmit = async () => {
  await formRef.value.validate()
  const params = {
    id: selectId.value,
    logistics_company_no: shipmentDetails.value.logistics_company_no,
    tracking_number: shipmentDetails.value.tracking_number,
    // company_supplier_id: companySupplierId.value,
  }
  editLogistics(params).then((res) => {
    console.log('editLogistics', res)
    message.success('修改成功')
    handleClose()
  })
}

// 获取发货单详情
const getShipmentDetail = async (params) => {
  const res = await getShipOrderDetail(params)
  shipmentDetails.value = res.data
  shipmentDetails.value.receiver_address = res.data.warehouse_receiver_province + res.data.warehouse_receiver_city + res.data.warehouse_receiver_area + res.data.warehouse_receiver_address
}

// 获取发货单详情列表
const getShipmentDetailList = async (params) => {
  const reqParams = {
    // "page": 1,
    // "pageSize": 10,
    booking_order_id: params.id,
    is_check: true,
    // company_supplier_id: params.company_supplier_id,
  }
  const res = await getShipOrderDetailList(reqParams)
  tableData.value = res.data.list
}

// 上一条/下一条切换
const handleChangeInfo = (type: 'prev' | 'next') => {
  shipmentDetails.value.logistics_company_no = ''
  shipmentDetails.value.tracking_number = ''

  if (type === 'prev') {
    selectId.value = tableIds.value[currentIdx.value - 1]
  } else {
    selectId.value = tableIds.value[currentIdx.value + 1]
  }
  const params = {
    id: selectId.value,
    // company_supplier_id: companySupplierId.value,
  }
  getShipmentDetail(params)
  getShipmentDetailList(params)
}

const onClickFiles = () => {
  openModal.value = true
}

// 图片预览控制
const setImageVisible = (visible: boolean) => {
  imageVisible.value = visible
}

const previewAuthFile = async (files) => {
  const fileId = files.id
  if (!fileId) {
    message.warning('文件不存在')
    return
  }
  try {
    let url = ''
    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${fileId}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${fileId}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })
    if (!response.ok) {
      message.warning('获取授权书失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 检查文件类型
    const contentType = response.headers.get('content-type') || ''
    if (contentType.startsWith('image/')) {
      // 图片文件使用图片预览组件
      previewImageUrl.value = previewUrl
      imageVisible.value = true
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    } else {
      // PDF等其他文件在新窗口打开
      window.open(previewUrl, '_blank')
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    }
  } catch (error) {
    console.error('授权书预览失败:', error)
    // message.error('授权书预览失败')
  }
}

let timeout: any
let currentValue = ''

function requestOptions(value: string, callback: any) {
  if (timeout) {
    clearTimeout(timeout)
    timeout = null
  }
  currentValue = value

  function fake() {
    const params = {
      page: 1,
      pageSize: 200,
      is_get_total_only: false,
      logistics_company_name: currentValue,
    }
    getLogisticsCompanyList(params).then((d) => {
      if (currentValue === value) {
        const result = d.data.list
        const data: any[] = []
        result.forEach((r: any) => {
          data.push({
            value: r.company_no,
            label: r.company_name,
          })
        })
        callback(data)
      }
    })
  }

  timeout = setTimeout(fake, 300)
}

const handleSearchLoistics = (val: string) => {
  requestOptions(val, (d: any[]) => (logisticsCompanyList.value = d))
}

const handleFocusLoistics = () => {
  // 点清空键后聚焦要重新获取列表以显示选项
  if (!shipmentDetails.value.logistics_company_no) {
    currentValue = ''
    logisticsCompanyList.value = []
    requestOptions(currentValue, (d: any[]) => (logisticsCompanyList.value = d))
  }
}

const initLogisticsCompanyList = () => {
  requestOptions('', (d: any[]) => (logisticsCompanyList.value = d))
  if (shipmentDetails.value.logistics_company_no && shipmentDetails.value.logistics_company && !logisticsCompanyList.value.find((item) => item.value === shipmentDetails.value.logistics_company_no)) {
    setTimeout(() => {
      logisticsCompanyList.value.push({
        value: shipmentDetails.value.logistics_company_no,
        label: shipmentDetails.value.logistics_company,
      })
    }, 400)
  }
}

defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
:deep(.ant-table-cell) {
  white-space: nowrap;
}
:deep(.drawer-title) {
  position: relative;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  &::after {
    content: '';
    position: absolute;
    left: 0;
    height: 12px;
    width: 2px;
    background-color: #1890ff;
    top: 50%;
    transform: translateY(-50%);
  }
}

:deep(.ant-form) {
  .ant-col {
    .ant-form-item {
      .ant-form-item-label {
        label {
          color: #999 !important;
        }
      }

      .ant-form-item-control {
        .ant-form-item-control-input-content {
          color: #333;
        }
      }
    }
  }
}
</style>
