import { request } from './request'

// 下载中心相关接口

// 获取下载中心列表
export const GetList = (data: {
  is_page?: boolean
  page?: number
  pageSize?: number
  is_get_total?: boolean
  is_get_total_only?: boolean
  sortField?: string
  sortType?: string
  filter_items?: Array<{
    field: string
    dynamic_filter_operator: string
    value: string
  }>
  keyword?: string
  file_name?: string
  status?: number // 导出状态筛选
  start_time?: string // 开始时间
  end_time?: string // 结束时间
}) => {
  return request({ url: '/api/DownloadCenter/GetList', data }, 'POST')
}

// 添加下载任务队列
export const Add = (data: { file_name: string; export_type_identifier: string; export_params: string }) => {
  return request({ url: '/api/DownloadCenter/Add', data }, 'POST')
}

// 重新导出
export const ReExport = (id: number) => {
  return request({ url: `/api/DownloadCenter/ReExport?id=${id}` }, 'POST')
}

// 兼容旧接口名称 - 获取下载中心列表
export const GetDownloadCenterList = (data: { file_name?: string; page?: number; pageSize?: number; sortField?: string; sortType?: string }) => {
  return GetList({
    is_page: true,
    page: data.page || 1,
    pageSize: data.pageSize || 20,
    is_get_total: true,
    file_name: data.file_name || '',
    sortField: data.sortField || 'completion_time',
    sortType: data.sortType || 'desc',
  })
}

// 兼容旧接口名称 - 点击下载（标记已下载）
export const ClickDownload = () => {
  // 这个接口可能需要根据实际后端实现来调整
  // 暂时返回一个成功的Promise，如果后端有对应接口可以替换
  return Promise.resolve({ success: true, message: '标记下载成功' })
}

// 下载文件
export const DownloadFile = async (fileId: number, fileName: string) => {
  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建下载URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/DownloadFile' : '/api/Files/DownloadFile'

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        logintoken: loginToken,
      },
      body: JSON.stringify({
        file_id: fileId,
        original_name: fileName,
        data_source: 'SRS',
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取文件名
    // const contentDisposition = response.headers.get('Content-Disposition')
    const downloadFileName = fileName
    // if (contentDisposition) {
    //   const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
    //   if (fileNameMatch) {
    //     downloadFileName = fileNameMatch[1].replace(/['"]/g, '')
    //   }
    // }

    // 创建blob并下载
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = downloadFileName
    a.style.display = 'none'

    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    return { success: true, message: '文件下载成功' }
  } catch (error) {
    console.error('文件下载失败:', error)
    throw new Error('文件下载失败')
  }
}

// 预览文件
export const PreviewFile = (fileId: number) => {
  return request(
    {
      url: `/api/Files/ViewByFileId?fileId=${fileId}`,
      responseType: 'blob',
    },
    'GET',
  )
}

// 导出类型枚举
export const ExportTypeEnum = {
  PORTAL_CERTIFICATE_LICENSE: 1, // 门户证书许可证导出
  BRAND_AUTH_FILES: 2, // 品牌授权书导出
  SUPPLIER_INFO: 3, // 供应商信息导出
  PRODUCT_LIST: 4, // 产品列表导出
  CERTIFICATE_LIST: 5, // 证书列表导出
  // 可以根据实际需要添加更多类型
}

// 导出状态枚举
export const ExportStatusEnum = {
  WAITING: 0, // 等待导出
  IN_PROGRESS: 1, // 进行中
  COMPLETED: 2, // 导出完成
  FAILED: 3, // 导出失败
}

// 状态文本映射
export const getStatusText = (status: number): string => {
  const statusMap = {
    [ExportStatusEnum.WAITING]: '等待导出',
    [ExportStatusEnum.IN_PROGRESS]: '进行中',
    [ExportStatusEnum.COMPLETED]: '导出完成',
    [ExportStatusEnum.FAILED]: '导出失败',
  }
  return statusMap[status] || '未知状态'
}

// 检查文件是否过期（24小时有效期）
export const isFileExpired = (completionTime: string): boolean => {
  if (!completionTime) return false

  const completionDate = new Date(completionTime)
  const now = new Date()
  const diffHours = (now.getTime() - completionDate.getTime()) / (1000 * 60 * 60)

  return diffHours > 24
}

// 获取支持预览的文件类型
export const getSupportedPreviewTypes = (): string[] => {
  return ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'txt', 'log', 'xml', 'csv']
}

// 检查文件是否支持预览
export const isSupportedPreviewType = (fileName: string): boolean => {
  const extension = fileName.split('.').pop()?.toLowerCase()
  return extension ? getSupportedPreviewTypes().includes(extension) : false
}
