<template>
  <a-drawer
    v-model:visible="drawerVisible"
    @afterVisibleChange="formRef?.clearValidate()"
    width="45vw"
    title="角色分组新建"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    rootClassName="detail-drawer"
  >
    <LoadingOutlined v-show="drawerLoading" class="loadingIcon" />
    <a-form layout="vertical" ref="formRef" :model="addRoleData" v-show="!drawerLoading">
      <a-form-item label="分组名称" name="group_name" :rules="[{ required: true }, { validator: (_rule, value) => validateStr(_rule, value, 5), message: '输入内容不可超过5字符' }]">
        <a-input id="group_name" :maxlength="5" v-model:value="addRoleData.group_name" placeholder="请输入分组名称" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space size="small">
        <a-button type="primary" @click="tapAddRoleSubmit">确认</a-button>
        <a-button @click="drawerVisible = false">取消</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { AddRoleGroup, UpdateRoleGroup } from '@/servers/RoleNew'
import { validateStr, buttonDebounce } from '@/utils/index'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { LoadingOutlined } from '@ant-design/icons-vue'

interface RowVO {
  id: number | string
  role_name: string
  scope: number
  status: number
  group_name: string
}

const emit = defineEmits(['addGroup'])
const formRef = ref<FormInstance>()
const drawerVisible = ref(false)
const drawerLoading = ref(false)
const addRoleData = ref<RowVO>({
  id: '',
  role_name: '',
  scope: 1,
  status: 1,
  group_name: '',
})

const open = (value) => {
  // 重置表单数据
  addRoleData.value = {
    id: '',
    role_name: '',
    scope: 1,
    status: 1,
    group_name: '',
  }

  if (value) {
    addRoleData.value.group_name = value.label
    addRoleData.value.id = value.key
  }
  drawerVisible.value = true
}

const updateRoleGroup = () => {
  const params = {
    id: addRoleData.value.id,
    group_name: addRoleData.value.group_name,
  }
  UpdateRoleGroup(params)
    .then(() => {
      message.success('修改成功')
      drawerVisible.value = false
      emit('addGroup')
    })
    .catch((error: any) => {
      // 显示接口返回的错误信息
      const errorMessage = error?.message || '修改失败'
      message.error(errorMessage)
    })
}

// 分组提交的核心逻辑
const groupSubmitCore = async () => {
  await formRef.value?.validateFields()

  if (addRoleData.value.id) {
    updateRoleGroup()
  } else {
    const params = {
      group_name: addRoleData.value.group_name,
    }
    await AddRoleGroup(params)
      .then(() => {
        message.success('新增成功')
        drawerVisible.value = false
        emit('addGroup')
      })
      .catch((rej) => {
        message.error(rej.message)
      })
  }
}

// 防抖版本的分组提交函数
const tapAddRoleSubmit = buttonDebounce(groupSubmitCore, 1000)

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped></style>
