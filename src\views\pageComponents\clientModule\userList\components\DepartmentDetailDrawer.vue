<template>
  <a-drawer :width="850" :visible="visible" @close="onClose">
    <template #title>
      <div class="flex w-full items-center justify-between">
        <span>查看部门</span>
        <div class="flex gap-2">
          <a-button @click="handleOperationLog" v-if="checkPermission && checkPermission()">操作日志</a-button>
        </div>
      </div>
    </template>
    <a-form :model="deptInfo" :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 17 }">
      <a-form-item label="部门名称">
        <span>{{ deptInfo.name || '/' }}</span>
      </a-form-item>
      <a-form-item label="所属企业">
        <span>{{ deptInfo.company_name || '/' }}</span>
      </a-form-item>
      <a-form-item label="上级部门">
        <span>{{ deptInfo.parent_company_name || '/' }}</span>
      </a-form-item>
      <a-form-item label="部门负责人">
        <div v-if="deptInfo.header_info && deptInfo.header_info.length > 0">
          <div v-for="(head, index) in deptInfo.header_info" :key="head.id || index">
            <a-tooltip :title="head.full_name || head.name || ''" placement="topLeft">
              <span>{{ head.name || '' }}</span>
              <div class="text-11px c-#999" v-if="head.full_name && head.full_name !== head.name">
                {{ head.full_name }}
              </div>
            </a-tooltip>
          </div>
        </div>
        <span v-else>/</span>
      </a-form-item>
    </a-form>

    <div class="drawer-title">其他信息</div>
    <a-form :model="deptInfo" :label-col="{ style: { width: '120px' } }" :wrapper-col="{ span: 17 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="创建时间">
            <span>{{ deptInfo.create_at || '/' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="创建人">
            <span>{{ deptInfo.create_name || '/' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="最后修改时间">
            <span>{{ deptInfo.update_at || '/' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="最后修改人">
            <span>{{ deptInfo.update_name || '/' }}</span>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-drawer>
  <OperateLog ref="operateLogEl" logType="department" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { GetCompanyById, type CompanyDetailInfo } from '@/servers/CompanyArchitecture'
import OperateLog from './OperateLog.vue'

const { checkPermission = () => true } = defineProps<{
  checkPermission?: () => boolean
}>()

const operateLogEl = ref<InstanceType<typeof OperateLog> | null>(null)
const visible = ref(false)
const deptInfo = ref<CompanyDetailInfo>({} as CompanyDetailInfo)
const selectId = ref<string>('')

const getDepInfo = async (id: string, type: number = 3) => {
  try {
    const res = await GetCompanyById({ id, type })
    const data = res.data || {}

    // 确保header_info始终是数组
    if (!data.header_info) {
      data.header_info = []
    } else if (!Array.isArray(data.header_info)) {
      data.header_info = [data.header_info]
    }

    deptInfo.value = data
  } catch (error) {
    console.error('获取部门详情失败:', error)
    message.error('获取部门详情失败')
  }
}

// 查看操作日志
const handleOperationLog = () => {
  operateLogEl.value?.open(selectId.value || '')
}

const open = async (id: string, type: number = 3) => {
  selectId.value = id
  visible.value = true
  await getDepInfo(id, type)
}

const onClose = () => {
  visible.value = false
  // 关闭时重置数据
  deptInfo.value = {} as CompanyDetailInfo
  selectId.value = ''
}

defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.drawer-title {
  padding-bottom: 8px;
  margin: 24px 0 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
}

.ellipsis {
  display: inline-block;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
}
</style>
