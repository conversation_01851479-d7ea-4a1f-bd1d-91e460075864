import { request } from './request'

// 获取品牌列表
export const GetList = (data) => {
  // 根据新的API文档，使用POST方法调用GetList接口
  return request({ url: '/api/BrandInfo/GetList', data }, 'POST').then((res) => {
    // 适配BaseTable组件期望的数据结构
    // BaseTable期望 res.data.data 或 res.data.list，并且总数在 res.data.total 中

    let brandList = []
    let total = 0

    // 处理不同的响应格式
    if (res.data) {
      if (Array.isArray(res.data)) {
        // 如果 res.data 直接是数组（旧格式）
        brandList = res.data
        total = res.total || brandList.length
      } else if (res.data.data && Array.isArray(res.data.data)) {
        // 如果 res.data.data 是数组（新格式）
        brandList = res.data.data
        total = res.data.total || brandList.length
      } else if (res.data.list && Array.isArray(res.data.list)) {
        // 如果 res.data.list 是数组（另一种新格式）
        brandList = res.data.list
        total = res.data.total || brandList.length
      }
    }

    // 返回BaseTable期望的格式
    return {
      ...res,
      data: {
        data: brandList, // 将数组数据放到 data.data 中
        total, // 总数
      },
    }
  })
}

// 新建品牌
export const Add = (data) => request({ url: '/api/BrandInfo/Create', data })

// 查看品牌详情
export const Details = (data) => request({ url: '/api/BrandInfo/Get', data }, 'GET')

// 编辑品牌
export const Update = (data) => request({ url: '/api/BrandInfo/Update', data })

// 修改品牌状态
export const UpdateStatus = (data) => request({ url: '/api/BrandInfo/UpdateStatus', data })

// 删除品牌
export const Delete = (data) => request({ url: '/api/BrandInfo/Delete', data }, 'GET')

// 获取品牌授权书记录
export const GetAuthFiles = (data) => request({ url: '/api/BrandInfo/GetAuthFiles', data }, 'GET')

// 上传品牌LOGO
export const UploadLogo = (data) => request({ url: '/api/Files/UploadFile?fileModule=Default', data, isFormData: true })

// 上传授权书
export const UploadAuthorization = (data) => request({ url: '/api/Files/UploadFile?fileModule=Default', data, isFormData: true })

// 上传营业执照授权书
export const UploadLicense = (data) => request({ url: '/api/Files/UploadFile?fileModule=License', data, isFormData: true })

// 品牌授权证书certificatefile
export const Uploadcertificatefile = (data) => request({ url: '/api/Files/UploadFile?fileModule=certificatefile', data, isFormData: true })

// 获取国家地区下拉选项
export const GetCountryOptions = (data) => request({ url: '/api/Common/GetCountryOptions', data })
