import { defineStore } from 'pinia'
import { GetRedPoint } from '@/servers/Common'

interface BadgeCounts {
  productLabelStatusCount: {
    menu_count: number
    total_count: number
    complete_count: number
    waiting_count: number
    reject_count: number
    refuse_count: number
  }
  managementProductLabelStatusCount: {
    menu_count: number
    total_count: number
    complete_count: number
    waiting_count: number
    reject_count: number
    refuse_count: number
    under_review_count: number
  }
  managementAuditLabelStatusCount: {
    menu_count: number
    total_count: number
    waiting_count: number
    pass_count: number
    reject_count: number
    refuse_count: number
  }
  bookingOrderLabelStatusCount: {
    approved_count: number
    pending_submit_count: number
  }
}

const useBadgeStore = defineStore('badgeStore', {
  state: () => ({
    badgeCounts: {
      productLabelStatusCount: {
        menu_count: 0,
        total_count: 0,
        complete_count: 0,
        waiting_count: 0,
        reject_count: 0,
        refuse_count: 0,
      },
      managementProductLabelStatusCount: {
        menu_count: 0,
        total_count: 0,
        complete_count: 0,
        waiting_count: 0,
        reject_count: 0,
        refuse_count: 0,
        under_review_count: 0,
      },
      managementAuditLabelStatusCount: {
        menu_count: 0,
        total_count: 0,
        waiting_count: 0,
        pass_count: 0,
        reject_count: 0,
        refuse_count: 0,
      },
      bookingOrderLabelStatusCount: {
        approved_count: 0,
        pending_submit_count: 0,
      },
    } as BadgeCounts,
    timer: undefined as ReturnType<typeof setInterval> | undefined,
  }),

  actions: {
    // 更新 badgeCounts 数据
    async fetchBadgeCounts() {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await GetRedPoint()
          // 如果请求成功，更新 badgeCounts 数据
          if (res && res.success && res.data) {
            this.badgeCounts = res.data
            resolve(res.data)
          }
        } catch (error) {
          console.error('获取红点数字失败', error)
          reject(error)
        }
      })
    },

    // 启动定时刷新
    startTimer(interval = 5000) {
      // 默认5秒
      if (this.timer) clearInterval(this.timer)
      this.timer = setInterval(this.fetchBadgeCounts, interval)
    },

    // 停止定时刷新
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = undefined
      }
    },
  },
})

export default useBadgeStore
