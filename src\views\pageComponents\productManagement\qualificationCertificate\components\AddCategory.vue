<template>
  <a-drawer width="1200" @close="handleClose" v-model:open="openDrawer" :maskClosable="false">
    <template #title>
      <span>新增资质</span>
    </template>
    <div class="drawer-title">
      <div class="text1">1、请按照您经营的类目所需的必备资质/证书，以确保后续商品审核顺利通过。若选项中没有您所需的资质/证书，可选择【其他】后上传。</div>
      <div class="text2">2、若商品由多个生产商供货，需分别上传每家工厂的许可证。</div>
      <div class="text3">3、资质/证书过期将导致商品自动下架，请注意资质/证书效期</div>
      <div class="text4">4、支持 PDF、JPG、JPEG、GIF、PNG 格式上传，文件大小不超过5MB</div>
    </div>
    <a-form ref="formRef" :model="{ CertificateData }">
      <!-- @checkbox-change="selectChangeEvent" :checkbox-config="{ showHeader: false }" -->
      <vxe-table :data="CertificateData" ref="CertificateTableRef">
        <!-- <vxe-column type="checkbox" width="60"></vxe-column> -->
        <vxe-column field="certificate_license_type" title="资质/证书" :width="400">
          <template #default="{ row }">
            <div class="flex">
              <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'certificate_license_type']" :rules="[{ required: true, message: '请选择资质/证书' }]">
                <a-select
                  v-model:value="row.certificate_license_type"
                  :style="{ width: row.certificate_license_type != 99 ? '200px' : '80px' }"
                  :options="certificateOptions"
                  allowClear
                  placeholder="请选择资质/证书"
                />
              </a-form-item>
              <a-form-item
                v-if="row.certificate_license_type === 99"
                :name="['CertificateData', CertificateData.indexOf(row), 'certificate_name']"
                :rules="[{ required: true, message: '请正确填写资质/证书名称' }]"
              >
                <a-input v-model:value="row.certificate_name" placeholder="请正确填写资质/证书名称" style="width: 220px; margin-left: 8px" showCount :maxlength="50" />
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="redText1">*</span>
            <span>资质/证书</span>
          </template>
        </vxe-column>
        <vxe-column field="manufacturer_name" title="所属生产商">
          <template #default="{ row }">
            <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'manufacturer_name']" :rules="[{ required: true, message: '请填写资质/证书所属生产商' }]">
              <a-input v-model:value="row.manufacturer_name" placeholder="请填写资质/证书所属生产商" style="width: 220px" :maxlength="50" showCount />
            </a-form-item>
          </template>
          <template #header>
            <span class="redText2">*</span>
            <span>所属生产商</span>
            <a-tooltip>
              <template #title>
                <span>① 若您自己就是生产商，则所属生产商默认均是您自身；</span>
                <br />
                <span>② 若您有多个生产商，则需分别上传每家工厂的许可证。</span>
              </template>
              <QuestionCircleOutlined class="ml-8px" />
            </a-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="validity_period" title="有效期">
          <template #default="{ row }">
            <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'validity_period']" :rules="[{ required: true, message: '请选择有效期' }, { validator: validityPeriodValidator }]">
              <a-range-picker :valueFormat="'YYYY-MM-DD'" v-model:value="row.validity_period" />
            </a-form-item>
          </template>
          <template #header>
            <span class="redText3">*</span>
            <span>有效期</span>
          </template>
        </vxe-column>
        <vxe-column field="fileList" title="上传文件" :width="220">
          <template #default="{ row, rowIndex }">
            <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'fileList']" :rules="[{ required: true, message: '请上传文件' }]">
              <span>共{{ row.fileNumber }}个</span>
              <span class="ml-12 c-#333">|</span>
              <a-button type="link" @click="handleUploadFile(row, rowIndex)">上传文件</a-button>
              <a-button type="link" @click="handleDeleteRow(rowIndex)">删除</a-button>
            </a-form-item>
          </template>
          <template #header>
            <span class="redText4">*</span>
            <span>上传文件</span>
          </template>
        </vxe-column>
      </vxe-table>
    </a-form>

    <div class="add-row" @click="handleAddRow">添加行</div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleSubmit">提交</a-button>
        <!-- <span class="c-#D33333 ml-20">请勾选需要提交的内容后，再进行提交。未勾选的内容不会提交。</span> -->
      </a-space>
    </template>
  </a-drawer>
  <UploadFileModal ref="uploadFileRef" v-model:file-list="currentFileList" @update="handleUploadModalOk" />
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { VxeTableInstance } from 'vxe-table'
import dayjs from 'dayjs'
import { AddCertificate } from '@/servers/Certificate'
import { getCommonOption } from '@/utils'
import UploadFileModal from './UploadFileModal.vue'

const emit = defineEmits(['search'])

// 抽屉是否打开
const openDrawer = ref(false)

interface CertificateRow {
  certificate_license_type: number | null // 资质/证书(下拉选中的值)
  manufacturer_name: string // 所属生产商
  validity_period: [dayjs.Dayjs | null, dayjs.Dayjs | null] // 有效期开始时间
  fileList: any[] // 上传文件
  fileNumber: number // 上传文件数量
  certificate_name: string // 其他资质/证书名称(输入的值)
}
// 表单引用
const formRef = ref<any>()
// 从缓存获取所属生产商
const getManufacturerName = computed(() => {
  const userData = localStorage.getItem('userData')
  if (!userData) return
  const userDataObj = JSON.parse(userData)
  return userDataObj.company
})
// 资质证书表格数据
const CertificateData = ref<CertificateRow[]>([
  // 默认一行
  {
    certificate_license_type: null,
    manufacturer_name: getManufacturerName.value || '',
    validity_period: [null, null],
    fileList: [],
    fileNumber: 0,
    certificate_name: '',
  },
])
// 资质证书选项
const certificateOptions = ref<any[]>([])
// 表格引用
const CertificateTableRef = ref<VxeTableInstance<CertificateRow>>()
// 上传文件ref
const uploadFileRef = ref<any>()
// 有效期验证
const validityPeriodValidator = (_: any, value: any) => {
  if (!value || !Array.isArray(value) || !value[0] || !value[1]) {
    return Promise.reject('请选择有效期')
  }
  const today = dayjs().startOf('day')
  if (dayjs(value[1]).isBefore(today)) {
    return Promise.reject('有效期结束时间不能小于当前日期')
  }
  if (dayjs(value[0]).isAfter(today)) {
    return Promise.reject('有效期开始时间不能大于当前日期')
  }
  return Promise.resolve()
}
// 当前行索引
const currentRowIndex = ref<number | null>(null)
// 当前行文件列表
const currentFileList = ref<any[]>([])
// 显示抽屉
const showDrawer = () => {
  openDrawer.value = true
  // 如果 CertificateData 为空，自动补一条默认数据
  if (CertificateData.value.length === 0) {
    CertificateData.value.push({
      certificate_license_type: null,
      manufacturer_name: getManufacturerName.value || '',
      validity_period: [null, null],
      fileList: [],
      fileNumber: 0,
      certificate_name: '',
    })
  }
}
// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
  CertificateData.value = []
}
// // 未勾选数据
// const isSubmitDisabled = computed(() => {
//   const $table = CertificateTableRef.value
//   if (!$table) return true
//   const checkedRows = $table.getCheckboxRecords()
//   return checkedRows.length === 0
// })

// 提交
const handleSubmit = () => {
  // 校验所有 CertificateData 行
  const fields = CertificateData.value
    .map((row, idx) => {
      const arr = [
        ['CertificateData', idx, 'certificate_license_type'],
        ['CertificateData', idx, 'manufacturer_name'],
        ['CertificateData', idx, 'validity_period'],
        ['CertificateData', idx, 'fileList'],
      ]
      if (row.certificate_license_type === 99) {
        arr.push(['CertificateData', idx, 'certificate_name'])
      }
      return arr
    })
    .flat()

  formRef.value
    .validateFields(fields)
    .then(() => {
      const params = CertificateData.value.map((row) => {
        let certificateName = ''
        if (row.certificate_license_type === 99) {
          certificateName = row.certificate_name
        } else if (row.certificate_license_type) {
          const certificateOption = certificateOptions.value.find((option: any) => option.value === row.certificate_license_type)
          certificateName = certificateOption ? certificateOption.label : ''
        }
        return {
          certificate_license_type: row.certificate_license_type,
          certificate_name: certificateName,
          manufacturer_name: row.manufacturer_name,
          validity_period_start_time: row.validity_period[0],
          validity_period_end_time: row.validity_period[1],
          file_ids: row.fileList.map((file) => file.id).join(','),
        }
      })
      AddCertificate(params).then(() => {
        message.success('提交成功')
        emit('search', CertificateData.value)
        handleClose()
      })
    })
    .catch((err) => {
      // 校验未通过
      console.log(err)
    })
}

// // 表格单勾选事件
// const selectChangeEvent: VxeTableEvents.CheckboxChange<CertificateRow> = ({ checked }) => {
//   const $table = CertificateTableRef.value
//   if ($table) {
//     const records = $table.getCheckboxRecords()
//     console.log(checked ? '勾选事件' : '取消事件', records)
//   }
// }
// 获取资质证书下拉筛选项
const getCertificateType = async () => {
  const [certificateOption] = await getCommonOption([16])
  certificateOptions.value = certificateOption
  console.log('certificateOptions', certificateOptions.value)
}
// 上传文件
const handleUploadFile = (row, index) => {
  currentRowIndex.value = index
  // 深拷贝，避免弹窗内操作影响原数据
  currentFileList.value = row.fileList ? JSON.parse(JSON.stringify(row.fileList)) : []
  uploadFileRef.value.showModal(row, index)
}
// 删除当前行
const handleDeleteRow = (rowIndex) => {
  CertificateData.value.splice(rowIndex, 1)
}
// 弹窗“确定”时调用
const handleUploadModalOk = () => {
  if (currentRowIndex.value !== null) {
    CertificateData.value[currentRowIndex.value].fileList = JSON.parse(JSON.stringify(currentFileList.value))
    // 更新 fileNumber
    CertificateData.value[currentRowIndex.value].fileNumber = currentFileList.value.length
    // 手动触发当前行的 fileList 校验，避免校验不消失
    formRef.value.validateFields([['CertificateData', currentRowIndex.value, 'fileList']])
  }
  console.log(
    'CertificateData',
    CertificateData.value.map((item) => item.fileList.map((file) => file.id)),
  )
}
// 添加行
const handleAddRow = () => {
  CertificateData.value.push({
    certificate_license_type: null, // 默认选中第一个选项
    manufacturer_name: getManufacturerName.value || '',
    validity_period: [null, null],
    fileList: [],
    certificate_name: '',
    fileNumber: 0,
  })
}
onMounted(() => {
  getCertificateType()
})
defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
.drawer-title {
  display: flex;
  flex-direction: column;
  height: 145px;
  font-size: 13px;
  background-color: rgb(255 248 221 / 100%);
  .text1 {
    color: #666;
  }
  .text2 {
    color: #666;
  }
  .text3 {
    color: #666;
  }
  .text4 {
    color: #666;
  }
}

.add-row {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border: 1px solid #666;
}
:deep(.ant-form-item) {
  margin-bottom: 0;
}
.redText1 {
  color: #f87171;
  margin-right: 4px;
}
.redText2 {
  color: #f87171;
  margin-right: 4px;
}
.redText3 {
  color: #f87171;
  margin-right: 4px;
}
.redText4 {
  color: #f87171;
  margin-right: 4px;
}
</style>
