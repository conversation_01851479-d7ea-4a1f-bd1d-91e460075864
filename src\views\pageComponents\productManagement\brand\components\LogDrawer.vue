<template>
  <div class="logBox">
    <div class="logTitle">操作日志</div>
    <div class="logContent">
      <LoadingOutlined v-show="loading" class="loadingIcon" />
      <div v-if="!loading && logList.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <p>暂无授权书记录</p>
      </div>
      <div v-else-if="!loading" class="log-list">
        <div v-for="(item, index) in logList" :key="index" class="log-item">
          <div class="log-header">
            <span class="log-time">{{ item.modified_at }}</span>
            <span class="log-operator">版本 {{ item.auth_version }}</span>
          </div>
          <div class="log-action">
            <a-tag color="blue">授权书文件</a-tag>
          </div>
          <div class="log-content">
            <div class="auth-info">
              <div class="info-item">
                <span class="info-label">文件名：</span>
                <span class="info-value">{{ item.original_name || item.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">授权期限：</span>
                <span class="info-value">{{ item.auth_start_at }} 至 {{ item.auth_end_at }}</span>
              </div>
              <div class="info-item">
                <a-button type="link" size="small" @click="downloadAuthFile(item.file_id)">下载文件</a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { GetAuthFiles } from '@/servers/Brand'

interface AuthFileLog {
  file_id: number
  modified_at: string // 时间戳或日期字符串
  auth_version: string | number // 版本号
  original_name: string
  name: string
  auth_start_at: string // 授权开始时间
  auth_end_at: string // 授权结束时间
  // 其他可能的属性...
}

const loading = ref(false)
const logList = ref<AuthFileLog[]>([])

// 打开日志
const open = async (brandData: any) => {
  if (!brandData?.id) return

  loading.value = true
  try {
    const res = await GetAuthFiles({ id: brandData.id })
    logList.value = res.data || []
  } catch (error) {
    console.error('获取授权书记录失败:', error)
    logList.value = []
  } finally {
    loading.value = false
  }
}

// 下载授权书文件
const downloadAuthFile = async (fileId: number) => {
  if (!fileId) {
    console.warn('缺少文件ID')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建下载URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/DownloadFile' : '/api/Files/DownloadFile'

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        logintoken: loginToken,
      },
      body: JSON.stringify({
        file_id: fileId,
        data_source: 'SRS',
      }),
    })

    if (!response.ok) {
      console.error('下载请求失败:', response.status)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取文件blob
    const blob = await response.blob()

    // 尝试从响应头获取文件名
    const contentDisposition = response.headers.get('content-disposition')
    let fileName = `auth_file_${fileId}`
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (fileNameMatch) {
        fileName = fileNameMatch[1].replace(/['"]/g, '')
      }
    }

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = fileName
    link.style.display = 'none'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000)
  } catch (error) {
    console.error('文件下载失败:', error)
  }
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
.logBox {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #f0f0f0;
  background: #fafafa;
}

.logTitle {
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.logContent {
  flex: 1;
  padding: 16px 20px;
  overflow-y: auto;
  position: relative;
}

.loadingIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #1890ff;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
}

.log-list {
  .log-item {
    margin-bottom: 16px;
    padding: 12px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #f0f0f0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .log-time {
      font-size: 12px;
      color: #8c8c8c;
    }

    .log-operator {
      font-size: 12px;
      color: #595959;
      font-weight: 500;
    }
  }

  .log-action {
    margin-bottom: 8px;
  }

  .log-content {
    font-size: 14px;
    color: #262626;
    line-height: 1.5;
    margin-bottom: 8px;
  }

  .auth-info {
    .info-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 500;
        color: #595959;
      }

      .info-value {
        color: #262626;
      }
    }
  }
}
</style>
