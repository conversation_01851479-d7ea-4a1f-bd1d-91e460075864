<template>
  <AttrForm />
  <div class="drawer-title">证书信息</div>
  <div class="px-20 mb-24">
    <a-space class="mb-16">
      <div class="text-16px font-bold c-#000">商品所需许可证</div>
    </a-space>
    <div class="c-#999 mb-24">
      <div>温馨提示：</div>
      <div>1:涉及护肤品商品生产，如：洗护用品，膏霜乳液制品，唇膏、唇彩、眉笔、唇线笔、发蜡，牙膏类商品，清洁类商品等，需上传《化妆品生产许可证》；</div>
      <div>2. 涉及食品生产，如：粮食加工品、油脂及其制品，调味品，肉制品，乳制品，饮料方便食品，冷冻饮品，速冻食品等，需上传《食品生产许可证》；</div>
      <div>3. 涉及药品生产，如：化学药剂，中药制剂，生物制品，原材料药及药用辅料，特殊管理药品等，需上传《药品生产许可证》；</div>
      <div>4. 涉及工业品生产，如：建筑用钢筋，水泥，广播电视传输设备，电线电缆，危险化学品，化肥等，需上传《工业产品生产许可证》；</div>
      <div>5. 涉及医疗器械相关生产，如：基础耗材，护理用品，诊断监护类，治疗康复类，手术耗材类，医用卫生材料等，需上传《医疗器械生产许可证》；</div>
    </div>
    <vxe-table :data="[{}]" size="small" border class="mb-24">
      <vxe-column v-for="item in permitCloumns" :key="item.field" :field="item.title" :title="item.title">
        <template #default>
          <a-flex>
            <a-button type="link" class="!px-8" @click="handleShowPermitModal(item.field)">选择文件</a-button>
            <div>
              <a-badge :count="form.productCertificates.filter((i) => i.certificate_license_type === item.field).length" />
            </div>
          </a-flex>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
  <ProductPermitModal v-if="permitModalVisible" v-model:visible="permitModalVisible" :selected-permit-ids="selectedPermitIds" @select="handleSelectPermit" />
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form'
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import ProductPermitModal from './ProductPermitModal.vue'

const props = defineProps<{
  rules: Record<string, Rule[]>
}>()

const formConfig = defineModel<BaseFormItem[]>('formConfig', { required: true })

const form = defineModel<any>('form')
// 商品所需许可证弹窗是否显示
const permitModalVisible = ref(false)
// 选择商品所需许可证的id
const selectedPermitIds = ref<number[]>([])

const selectField = ref<number>()

// 商品所需许可证
const permitCloumns = ref([
  {
    field: 1,
    title: '化妆品生产许可证',
  },
  {
    field: 4,
    title: '食品生产许可证',
  },
  {
    field: 5,
    title: '药品生产许可证',
  },
  {
    field: 6,
    title: '医疗器械生产许可证',
  },
  {
    field: 8,
    title: '工业产品生产许可证',
  },
])

// 显示商品所需许可证弹窗
const handleShowPermitModal = (field: number) => {
  selectField.value = field
  permitModalVisible.value = true
  const selectPermit = form.value.productCertificates.filter((i) => i.certificate_license_type === field)
  selectedPermitIds.value = selectPermit.map((i) => i.certificate_id)
}

// 选择商品所需许可证
const handleSelectPermit = (selectRecors: any[]) => {
  const newSelectRecors = selectRecors.map((i) => ({
    id: 0,
    certificate_id: i.id,
    certificate_license_type: selectField.value,
    file_ids: i.file_ids,
  }))
  form.value.productCertificates = form.value.productCertificates.filter((i) => i.certificate_license_type !== selectField.value)
  form.value.productCertificates.push(...newSelectRecors)
}

const [AttrForm, attrFormRef] = useBaseForm({
  modelValue: form,
  formConfig,
  rules: props.rules,
})

defineExpose({
  validate: () => attrFormRef.value?.validate(),
  clearValidate: () => attrFormRef.value?.clearValidate(),
  validateFields: (key: string) => attrFormRef.value?.validateFields([key]),
})
</script>

<style scoped lang="scss">
:deep(.vxe-table--body-wrapper) {
  min-height: 0px !important;
}
</style>
