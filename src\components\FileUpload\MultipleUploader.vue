<template>
  <a-modal :closable="!loading" :maskClosable="!loading" :keyboard="!loading" :open="modelVisible" title="文件上传" width="600px" @cancel="modelVisible = false">
    <div>
      <a-radio-group @change="setListType" class="listTypeRadio" v-model:value="listType">
        <a-radio-button :value="1"><appstore-outlined class="icon" /></a-radio-button>
        <a-radio-button :value="2"><BarsOutlined class="icon" /></a-radio-button>
      </a-radio-group>
      <a-upload-dragger
        class="draggerUploader"
        v-model:fileList="fileList"
        name="file"
        :multiple="true"
        :before-upload="beforeUpload"
        @mouseenter="imgMouseEnter"
        :customRequest="null"
        action="#"
        :showUploadList="false"
        :disabled="loading"
      >
        <div class="filePreviewMainBox" style="user-select: none">
          <a-row v-show="listType == 1" :gutter="16">
            <a-col v-for="(item, index) in fileList" :key="index" :span="8">
              <div @click.stop="preview(item)" class="filePreviewCard">
                <div class="preview" :style="item.url ? '' : 'display: flex;align-items: center;justify-content: center;'">
                  <img style="width: 100%" v-if="item.url" :src="item.url" alt="" />
                  <LoadingOutlined v-show="item.loading" class="loadingIcon" />
                  <div class="previewMasker">
                    <eye-outlined class="icon" />
                    <div class="tip">预览</div>
                    <div v-show="!loading" class="delBtn" @click.stop="fileList.splice(index, 1)"><delete-outlined class="delBtnIcon" /></div>
                  </div>
                </div>
                <div class="title">
                  <FileTextFilled class="icon txtIcon" v-show="item.type.indexOf('text') != -1" />
                  <FileImageFilled class="icon imgIcon" v-show="item.type.indexOf('image') != -1" />
                  <FilePdfFilled class="icon pdfIcon" v-show="item.type.indexOf('pdf') != -1" />
                  <span v-if="item.file">{{ item?.file?.name }}</span>
                </div>
              </div>
            </a-col>
          </a-row>
          <div v-show="listType == 2" class="listItemBox" @click.stop="null">
            <div @click="preview(item)" class="listItem" v-for="(item, index) in fileList" :key="index">
              <div style="display: flex; align-items: center">
                <FileTextFilled class="icon txtIcon" v-show="item.type.indexOf('text') != -1" />
                <FileImageFilled class="icon imgIcon" v-show="item.type.indexOf('image') != -1" />
                <FilePdfFilled class="icon pdfIcon" v-show="item.type.indexOf('pdf') != -1" />
                <div class="title">{{ item?.file?.name }}</div>
              </div>
              <div v-show="!loading" @click.stop="fileList.splice(index, 1)">
                <delete-outlined class="delBtnIcon" />
              </div>
            </div>
          </div>
        </div>
        <div v-show="fileList.length === 0" class="uploaderTip">粘贴文件或将文件拖拽至此即可添加</div>
      </a-upload-dragger>
      <input class="pasteInput" id="pasteInput" />
      <!-- <a-upload class="btnUploader" v-model:file-list="fileList" name="file" :multiple="true" :before-upload="beforeUpload" :customRequest="null" action="#" :showUploadList="false">
        <a-button :disabled="loading" class="btnUploaderBtn" block>
          <upload-outlined></upload-outlined>
          <span>点击上传</span>
        </a-button>
      </a-upload> -->
    </div>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; align-items: center">
        <!-- <span class="uploaderDescription">同名的文件将会覆盖</span> -->
        <div>
          <a-button :disabled="loading" @click="modelVisible = false">取消</a-button>
          <a-button :loading="loading" @click="submit" type="primary">提交</a-button>
        </div>
      </div>
    </template>
  </a-modal>
  <a-image-preview-group
    :preview="{
      visible: pdfPreviewerVisible,
      onVisibleChange: (value) => {
        pdfPreviewerVisible = value
      },
    }"
  >
    <a-image v-for="(item, index) in pdfPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
  </a-image-preview-group>
  <a-image-preview-group
    :preview="{
      current: imgPreviewerCurrent,
      visible: imgPreviewerVisible,
      onVisibleChange: (value) => {
        imgPreviewerVisible = value
      },
    }"
  >
    <a-image v-for="(item, index) in imgPreviewerArray" :key="index" :width="0" :height="0" :src="item" />
  </a-image-preview-group>
</template>

<script lang="ts" setup>
import { FileImageFilled, FilePdfFilled, LoadingOutlined, FileTextFilled, EyeOutlined, DeleteOutlined, BarsOutlined, AppstoreOutlined } from '@ant-design/icons-vue'
// import * as pdfjsLib from 'pdfjs-dist/build/pdf'
import { message } from 'ant-design-vue'

const emits = defineEmits(['submit'])

// pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})
const target = ref(null)
const fileList = ref<any[]>([])
const modelVisible = ref(false)
const pdfPreviewerVisible = ref(false)
const pdfPreviewerArray = ref<any[]>([])
const imgPreviewerVisible = ref(false)
const imgPreviewerArray = ref<any[]>([])
const imgPreviewerCurrent = ref(0)
const listType = ref(1)
const imgMouseEnter = () => {
  const input = document.getElementById('pasteInput')
  if (input) {
    input.focus()
    input.addEventListener('paste', (e) => getClipboardFiles(e))
  }
}
const getClipboardFiles = (e: ClipboardEvent) => {
  e.preventDefault()
  const items = e.clipboardData?.items
  if (items) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) {
          beforeUpload(file)
        }
      }
    }
  }
}
const getBase64 = (file) => {
  return new Promise((resolve) => {
    const fileReader = new FileReader()
    fileReader.readAsDataURL(file)
    fileReader.onload = () => {
      resolve(fileReader.result)
    }
  })
}
const submit = () => {
  emits('submit', fileList.value, target.value)
}
const preview = (item) => {
  if (item.type.indexOf('pdf') != -1) {
    previewPdf(item)
  } else if (item.type.indexOf('image') != -1) {
    previewImg(item)
  } else if (item.type.indexOf('text') != -1) {
    previewText(item)
  }
}
const previewImg = (item) => {
  const arr = fileList.value.filter((e) => e.type.indexOf('image') != -1).map((a) => a.url)
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [...arr]
  imgPreviewerCurrent.value = arr.findIndex((e) => e === item.url)
}
const previewText = (item) => {
  imgPreviewerVisible.value = true
  imgPreviewerArray.value = [item.url]
  imgPreviewerCurrent.value = 0
}
const beforeUpload = (file) => {
  const promise = new Promise((resolve, reject) => {
    if (file.size / 1024 / 1024 > 5) {
      message.error({ content: '文件大小不能超过5M', key: 'msg' })
      reject()
      return
    }
    const isJpgOrPng =
      file.type === 'text/plain' ||
      file.type === 'application/pdf' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/gif' ||
      file.type === 'image/webp' ||
      file.type === 'image/bmp'
    if (!isJpgOrPng) {
      message.error({ content: '请上传jpg、png、pdf、txt文件', key: 'msg' })
      reject()
      return
    }
    const isDuplicate = fileList.value.some((e) => e.file.name === file.name)
    if (isDuplicate) {
      // message.error({ content: '存在相同文件', key: 'msg' })
      reject()
      return
    }
    const fileId = Math.floor(Math.random() * 9000000000) + 1000000000
    const fileObj = { id: fileId, file, type: file.type, doneImgLength: 1, doneImgArr: [], url: null, pdfUrl: null, loading: true, lastModified: file.lastModified, size: file.size }
    fileList.value.push(fileObj)
    const targetIndex = fileList.value.findIndex((item) => item.id === fileId)
    getBase64(file).then((url) => {
      if (file.type === 'application/pdf') {
        getPdfMinPreviewUrl(url).then((res) => {
          fileList.value[targetIndex].pdfUrl = url
          fileList.value[targetIndex].url = res
          fileList.value[targetIndex].loading = false
        })
      } else if (file.type === 'text/plain') {
        getTxtAsImage(file).then((res) => {
          fileList.value[targetIndex].url = res
          fileList.value[targetIndex].loading = false
        })
      } else {
        fileList.value[targetIndex].url = url
        fileList.value[targetIndex].loading = false
      }
    })
  })
  return promise
}
const getPdfMinPreviewUrl = async (fileUrl) => {
  return new Promise((resolve) => {
    ;(async () => {
      const loadingTask = pdfjsLib.getDocument(fileUrl)
      const pdf = await loadingTask.promise
      const page = await pdf.getPage(1)
      const viewport = page.getViewport({ scale: 0.2 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d')
      const renderContext = {
        canvasContext: context,
        viewport,
      }
      await page.render(renderContext).promise
      resolve(canvas.toDataURL('image/png'))
    })()
  })
}
const getTxtAsImage = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsText(file)
    reader.onload = () => {
      const textContent = reader.result as string
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      if (!context) {
        reject('无法创建2D上下文')
        return
      }
      const fontSize = 14
      const lineHeight = 24
      const padding = 20
      const lines = textContent.split('\n')
      canvas.width = 800
      canvas.height = 500
      context.fillStyle = '#fff'
      context.fillRect(0, 0, canvas.width, canvas.height)
      context.font = `${fontSize}px Arial`
      context.fillStyle = '#000'
      context.textBaseline = 'top'
      lines.forEach((line, index) => {
        context.fillText(line, padding, padding + index * lineHeight)
      })
      const imageUrl = canvas.toDataURL('image/png')
      resolve(imageUrl)
    }
    reader.onerror = () => {
      reject('文件读取失败')
    }
  })
}
const previewPdf = async (item) => {
  pdfPreviewerArray.value = [...item.doneImgArr]
  pdfPreviewerVisible.value = true
  const loadingTask = pdfjsLib.getDocument(item.pdfUrl)
  const pdf = await loadingTask.promise
  for (let currentPage = item.doneImgLength; currentPage <= pdf.numPages; currentPage++) {
    if (pdfPreviewerVisible.value) {
      const page = await pdf.getPage(currentPage)
      const viewport = page.getViewport({ scale: 3 })
      const canvas = document.createElement('canvas')
      canvas.height = viewport.height
      canvas.width = viewport.width
      const context = canvas.getContext('2d')
      const renderContext = {
        canvasContext: context,
        viewport,
      }
      await page.render(renderContext).promise
      item.doneImgArr.push(canvas.toDataURL('image/png'))
      item.doneImgLength = currentPage + 1
      pdfPreviewerArray.value.push(canvas.toDataURL('image/png'))
    }
  }
}
const setListType = () => {
  localStorage.setItem('credentialsUploadListType', String(listType.value))
}
const open = (obj) => {
  fileList.value = []
  target.value = obj
  if (localStorage.getItem('credentialsUploadListType')) {
    listType.value = Number(localStorage.getItem('credentialsUploadListType'))
  } else {
    listType.value = 1
  }
  modelVisible.value = true
}
const close = () => {
  modelVisible.value = false
  fileList.value = []
}
defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.uploaderDescription {
  color: rgba(0, 0, 0, 0.5);
}
.uploaderTip {
  height: 120px;
  line-height: 120px;
  text-align: center;
  color: rgba(0, 0, 0, 0.5);
  margin: -16px 0;
  user-select: none;
}
.filePreviewMainBox {
  margin: 0 1vw;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  .listItemBox {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .listItem {
      cursor: pointer;
      background-color: #fff;
      padding: 0 12px;
      border: 1px solid #d9d9d9;
      border-radius: 3px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      height: 30px;
      transition: all 0.3s;
      .title {
        max-width: 450px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &:hover {
        border-color: #40a9ff;
      }
      .icon {
        font-size: 16px;
        margin-right: 4px;
      }
      .delBtnIcon {
        font-size: 15px;
        color: rgba(255, 77, 79, 0.8);
        &:hover {
          color: rgba(255, 77, 79, 1);
        }
      }
    }
  }
  .filePreviewCard {
    width: 150px;
    cursor: pointer;
    .preview {
      width: 100%;
      height: 96px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fff;
      overflow: hidden;
      position: relative;
      .previewMasker {
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.5);
        top: 0;
        transition: opacity 0.3s;
        opacity: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .delBtn {
          width: 25px;
          height: 25px;
          border-radius: 4px;
          position: absolute;
          right: 5px;
          top: 5px;
          background-color: rgba(255, 77, 79, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;
          .delBtnIcon {
            color: #fff;
            font-size: 14px;
          }
          &:hover {
            background-color: rgba(255, 77, 79, 1);
          }
        }
        .icon {
          font-size: 16px;
          color: #fff;
        }
        .tip {
          color: #fff;
          font-size: 14px;
          margin-left: 8px;
        }
      }
    }
    &:hover {
      .preview {
        border-color: #40a9ff;
      }
      .previewMasker {
        opacity: 1;
      }
      .title {
        color: #40a9ff;
        text-decoration: underline;
      }
    }
    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
      line-height: 36px;
      font-size: 13px;
      text-align: left;
      .icon {
        font-size: 15px;
        margin-right: 4px;
      }
    }
  }
}
.loadingIcon {
  color: #1890ff;
  font-size: 30px;
}
.btnUploaderBtn {
  margin-top: 12px;
}
.pasteInput {
  width: 100%;
  height: 0;
  background-color: red;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
}
.listTypeRadio {
  margin-bottom: 12px;
  .icon {
    font-size: 17px;
  }
}
.pdfIcon {
  color: rgb(254, 83, 89);
}
.imgIcon {
  color: rgb(37, 179, 158);
}
.txtIcon {
  color: rgb(74, 144, 255);
}
// .draggerUploader {
//   display: inline-block;
//   background-color: red;
//   width: 100%;
// }
</style>
