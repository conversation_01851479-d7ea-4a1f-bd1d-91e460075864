import { Mo<PERSON><PERSON>eth<PERSON> } from 'vite-plugin-mock'

export default [
  {
    url: '/product/product/detail',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          content_json: [
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '规格信息',
                  attr_tips: null,
                },
              ],
              id: 15,
              name: '规格信息',
              x: 0,
              y: 0,
              w: 4,
              h: 29,
              i: 8256065903,
              type: 99,
              attr_ids: [119, 157, 123, 149, 133, 134, 135, 136, 137, 159, 162, 158, 124, 172, 121],
              children: [
                {
                  id: 119,
                  attr_group_id: 15,
                  name: '规格型号',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: true,
                  x: 0,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 3578366437,
                  type: 1,
                  type_json: '{"word_limit_count":100,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: 'ceshi-ce011',
                      language_id: 1,
                      attr_name: '规格型号',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 157,
                  attr_group_id: 15,
                  name: '重量',
                  template_display_unit: 3,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 5,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 6983895169,
                  type: 4,
                  type_json: '{"unit_type":1,"metering_unit_ids":[3],"currency_unit_ids":null,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '10',
                      language_id: 1,
                      attr_name: '重量',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 123,
                  attr_group_id: 15,
                  name: '标准装箱数',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 9515665496,
                  type: 3,
                  type_json: '{"precision":0,"rounding_type":1,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '10',
                      language_id: 1,
                      attr_name: '标准装箱数',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 149,
                  attr_group_id: 15,
                  name: '箱规',
                  template_display_unit: null,
                  multi_attr_value_options: [
                    {
                      language_id: 1,
                      options: [
                        {
                          id: 2,
                          mult_attr_value_name: '小号 cm：28x36x21',
                          is_enabled: true,
                        },
                        {
                          id: 3,
                          mult_attr_value_name: '旧中 cm：56x36x21.5',
                          is_enabled: true,
                        },
                        {
                          id: 4,
                          mult_attr_value_name: '大号 cm：56x36x42',
                          is_enabled: true,
                        },
                        {
                          id: 5,
                          mult_attr_value_name: '其它1 cm：27x11x25.5',
                          is_enabled: true,
                        },
                        {
                          id: 6,
                          mult_attr_value_name: '新中 cm：55x35x21.5',
                          is_enabled: true,
                        },
                        {
                          id: 7,
                          mult_attr_value_name: '其它2 cm：33.5x32.5x12.7',
                          is_enabled: true,
                        },
                        {
                          id: 8,
                          mult_attr_value_name: '其它3 cm：34x28x20',
                          is_enabled: true,
                        },
                        {
                          id: 9,
                          mult_attr_value_name: '其它4 cm：37x30x28',
                          is_enabled: true,
                        },
                        {
                          id: 10,
                          mult_attr_value_name: '其它5 cm：38.5x21.5x24',
                          is_enabled: true,
                        },
                        {
                          id: 11,
                          mult_attr_value_name: '其它6 cm：41x21x25.5',
                          is_enabled: true,
                        },
                        {
                          id: 12,
                          mult_attr_value_name: '其它7 cm：41x33x22',
                          is_enabled: true,
                        },
                        {
                          id: 13,
                          mult_attr_value_name: '其它8 cm：42x28x16',
                          is_enabled: true,
                        },
                        {
                          id: 14,
                          mult_attr_value_name: '其它9 cm：43.5x36.5x20.5',
                          is_enabled: true,
                        },
                        {
                          id: 15,
                          mult_attr_value_name: '其它10 cm：44.5x32x40',
                          is_enabled: true,
                        },
                        {
                          id: 16,
                          mult_attr_value_name: '其它11 cm：44x28x25',
                          is_enabled: true,
                        },
                        {
                          id: 17,
                          mult_attr_value_name: '其它12 cm：44x32x40',
                          is_enabled: true,
                        },
                        {
                          id: 18,
                          mult_attr_value_name: '其它13 cm：44x44x32',
                          is_enabled: true,
                        },
                        {
                          id: 19,
                          mult_attr_value_name: '其它14 cm：45.8x37.8x26.8',
                          is_enabled: true,
                        },
                        {
                          id: 20,
                          mult_attr_value_name: '其它15 cm：45x23x29.2',
                          is_enabled: true,
                        },
                        {
                          id: 21,
                          mult_attr_value_name: '其它16 cm：45x32x30',
                          is_enabled: true,
                        },
                        {
                          id: 22,
                          mult_attr_value_name: '其它17 cm：47x47x175',
                          is_enabled: true,
                        },
                        {
                          id: 23,
                          mult_attr_value_name: '其它18 cm：50.5x32x32.5',
                          is_enabled: true,
                        },
                        {
                          id: 24,
                          mult_attr_value_name: '其它19 cm：50x29x34',
                          is_enabled: true,
                        },
                        {
                          id: 25,
                          mult_attr_value_name: '其它20 cm：50x65x58',
                          is_enabled: true,
                        },
                        {
                          id: 26,
                          mult_attr_value_name: '其它21 cm：51x25x26',
                          is_enabled: true,
                        },
                        {
                          id: 27,
                          mult_attr_value_name: '其它22 cm：51x30x22',
                          is_enabled: true,
                        },
                        {
                          id: 28,
                          mult_attr_value_name: '其它24 cm：52x32x32.5',
                          is_enabled: true,
                        },
                        {
                          id: 29,
                          mult_attr_value_name: '其它25 cm：52x36x28',
                          is_enabled: true,
                        },
                        {
                          id: 30,
                          mult_attr_value_name: '其它26 cm：53x36x33',
                          is_enabled: true,
                        },
                        {
                          id: 31,
                          mult_attr_value_name: '其它27 cm：53x42x45.5',
                          is_enabled: true,
                        },
                        {
                          id: 32,
                          mult_attr_value_name: '其它28 cm：54x38x38',
                          is_enabled: true,
                        },
                        {
                          id: 33,
                          mult_attr_value_name: '其它29 cm：55x34.6x22.5',
                          is_enabled: true,
                        },
                        {
                          id: 34,
                          mult_attr_value_name: '其它30 cm：56x36x21',
                          is_enabled: true,
                        },
                        {
                          id: 35,
                          mult_attr_value_name: '其它31 cm：56x36x23.5',
                          is_enabled: true,
                        },
                        {
                          id: 36,
                          mult_attr_value_name: '其它32 cm：57x42x43',
                          is_enabled: true,
                        },
                        {
                          id: 37,
                          mult_attr_value_name: '其它33 cm：58.5x34.5x38.5',
                          is_enabled: true,
                        },
                        {
                          id: 38,
                          mult_attr_value_name: '其它35 cm：60x42x42',
                          is_enabled: true,
                        },
                        {
                          id: 39,
                          mult_attr_value_name: '其它36 cm：61.5x31x31',
                          is_enabled: true,
                        },
                        {
                          id: 40,
                          mult_attr_value_name: '其它37 cm：61.5x34.5x32',
                          is_enabled: true,
                        },
                      ],
                    },
                  ],
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 3,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 9328752254,
                  type: 12,
                  type_json:
                    '{"available_reference_type":1,"data_source":null,"option_type":1,"multi_attr_id":2,"multi_attr_id_name":"箱规","options":[{"language_id":1,"options":[{"id":2,"mult_attr_value_name":"小号 cm：28x36x21","is_enabled":true,"label":"小号 cm：28x36x21","value":2},{"id":3,"mult_attr_value_name":"旧中 cm：56x36x21.5","is_enabled":true,"label":"旧中 cm：56x36x21.5","value":3},{"id":4,"mult_attr_value_name":"大号 cm：56x36x42","is_enabled":true,"label":"大号 cm：56x36x42","value":4},{"id":5,"mult_attr_value_name":"其它1 cm：27x11x25.5","is_enabled":true,"label":"其它1 cm：27x11x25.5","value":5},{"id":6,"mult_attr_value_name":"新中 cm：55x35x21.5","is_enabled":true,"label":"新中 cm：55x35x21.5","value":6},{"id":7,"mult_attr_value_name":"其它2 cm：33.5x32.5x12.7","is_enabled":true,"label":"其它2 cm：33.5x32.5x12.7","value":7},{"id":8,"mult_attr_value_name":"其它3 cm：34x28x20","is_enabled":true,"label":"其它3 cm：34x28x20","value":8},{"id":9,"mult_attr_value_name":"其它4 cm：37x30x28","is_enabled":true,"label":"其它4 cm：37x30x28","value":9},{"id":10,"mult_attr_value_name":"其它5 cm：38.5x21.5x24","is_enabled":true,"label":"其它5 cm：38.5x21.5x24","value":10},{"id":11,"mult_attr_value_name":"其它6 cm：41x21x25.5","is_enabled":true,"label":"其它6 cm：41x21x25.5","value":11},{"id":12,"mult_attr_value_name":"其它7 cm：41x33x22","is_enabled":true,"label":"其它7 cm：41x33x22","value":12},{"id":13,"mult_attr_value_name":"其它8 cm：42x28x16","is_enabled":true,"label":"其它8 cm：42x28x16","value":13},{"id":14,"mult_attr_value_name":"其它9 cm：43.5x36.5x20.5","is_enabled":true,"label":"其它9 cm：43.5x36.5x20.5","value":14},{"id":15,"mult_attr_value_name":"其它10 cm：44.5x32x40","is_enabled":true,"label":"其它10 cm：44.5x32x40","value":15},{"id":16,"mult_attr_value_name":"其它11 cm：44x28x25","is_enabled":true,"label":"其它11 cm：44x28x25","value":16},{"id":17,"mult_attr_value_name":"其它12 cm：44x32x40","is_enabled":true,"label":"其它12 cm：44x32x40","value":17},{"id":18,"mult_attr_value_name":"其它13 cm：44x44x32","is_enabled":true,"label":"其它13 cm：44x44x32","value":18},{"id":19,"mult_attr_value_name":"其它14 cm：45.8x37.8x26.8","is_enabled":true,"label":"其它14 cm：45.8x37.8x26.8","value":19},{"id":20,"mult_attr_value_name":"其它15 cm：45x23x29.2","is_enabled":true,"label":"其它15 cm：45x23x29.2","value":20},{"id":21,"mult_attr_value_name":"其它16 cm：45x32x30","is_enabled":true,"label":"其它16 cm：45x32x30","value":21},{"id":22,"mult_attr_value_name":"其它17 cm：47x47x175","is_enabled":true,"label":"其它17 cm：47x47x175","value":22},{"id":23,"mult_attr_value_name":"其它18 cm：50.5x32x32.5","is_enabled":true,"label":"其它18 cm：50.5x32x32.5","value":23},{"id":24,"mult_attr_value_name":"其它19 cm：50x29x34","is_enabled":true,"label":"其它19 cm：50x29x34","value":24},{"id":25,"mult_attr_value_name":"其它20 cm：50x65x58","is_enabled":true,"label":"其它20 cm：50x65x58","value":25},{"id":26,"mult_attr_value_name":"其它21 cm：51x25x26","is_enabled":true,"label":"其它21 cm：51x25x26","value":26},{"id":27,"mult_attr_value_name":"其它22 cm：51x30x22","is_enabled":true,"label":"其它22 cm：51x30x22","value":27},{"id":28,"mult_attr_value_name":"其它24 cm：52x32x32.5","is_enabled":true,"label":"其它24 cm：52x32x32.5","value":28},{"id":29,"mult_attr_value_name":"其它25 cm：52x36x28","is_enabled":true,"label":"其它25 cm：52x36x28","value":29},{"id":30,"mult_attr_value_name":"其它26 cm：53x36x33","is_enabled":true,"label":"其它26 cm：53x36x33","value":30},{"id":31,"mult_attr_value_name":"其它27 cm：53x42x45.5","is_enabled":true,"label":"其它27 cm：53x42x45.5","value":31},{"id":32,"mult_attr_value_name":"其它28 cm：54x38x38","is_enabled":true,"label":"其它28 cm：54x38x38","value":32},{"id":33,"mult_attr_value_name":"其它29 cm：55x34.6x22.5","is_enabled":true,"label":"其它29 cm：55x34.6x22.5","value":33},{"id":34,"mult_attr_value_name":"其它30 cm：56x36x21","is_enabled":true,"label":"其它30 cm：56x36x21","value":34},{"id":35,"mult_attr_value_name":"其它31 cm：56x36x23.5","is_enabled":true,"label":"其它31 cm：56x36x23.5","value":35},{"id":36,"mult_attr_value_name":"其它32 cm：57x42x43","is_enabled":true,"label":"其它32 cm：57x42x43","value":36},{"id":37,"mult_attr_value_name":"其它33 cm：58.5x34.5x38.5","is_enabled":true,"label":"其它33 cm：58.5x34.5x38.5","value":37},{"id":38,"mult_attr_value_name":"其它35 cm：60x42x42","is_enabled":true,"label":"其它35 cm：60x42x42","value":38},{"id":39,"mult_attr_value_name":"其它36 cm：61.5x31x31","is_enabled":true,"label":"其它36 cm：61.5x31x31","value":39},{"id":40,"mult_attr_value_name":"其它37 cm：61.5x34.5x32","is_enabled":true,"label":"其它37 cm：61.5x34.5x32","value":40}]}]}',
                  language_config: [
                    {
                      value: [],
                      language_id: 1,
                      attr_name: '箱规',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 133,
                  attr_group_id: 15,
                  name: '长',
                  template_display_unit: 6,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 5,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 1337354984,
                  type: 4,
                  type_json: '{"unit_type":1,"metering_unit_ids":[5,6],"currency_unit_ids":null,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '10',
                      language_id: 1,
                      attr_name: '长',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 134,
                  attr_group_id: 15,
                  name: '宽',
                  template_display_unit: 6,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 5,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 8711390473,
                  type: 4,
                  type_json: '{"unit_type":1,"metering_unit_ids":[5,6],"currency_unit_ids":null,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '12',
                      language_id: 1,
                      attr_name: '宽',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 135,
                  attr_group_id: 15,
                  name: '高',
                  template_display_unit: 6,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 5,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 4748339591,
                  type: 4,
                  type_json: '{"unit_type":1,"metering_unit_ids":[5,6],"currency_unit_ids":null,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '13',
                      language_id: 1,
                      attr_name: '高',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 136,
                  attr_group_id: 15,
                  name: '体积',
                  template_display_unit: 8,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 5,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 70,
                  w: 1,
                  h: 35,
                  i: 8331935694,
                  type: 4,
                  type_json: '{"unit_type":1,"metering_unit_ids":[7,8],"currency_unit_ids":null,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '1560',
                      language_id: 1,
                      attr_name: '体积',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 137,
                  attr_group_id: 15,
                  name: '面积',
                  template_display_unit: 10,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 5,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 70,
                  w: 1,
                  h: 35,
                  i: 4952387108,
                  type: 4,
                  type_json: '{"unit_type":1,"metering_unit_ids":[9,10],"currency_unit_ids":null,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '120',
                      language_id: 1,
                      attr_name: '面积',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 159,
                  attr_group_id: 15,
                  name: '中文成分名',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 105,
                  w: 1,
                  h: 35,
                  i: 9380486162,
                  type: 1,
                  type_json: '{"word_limit_count":100,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: '硅胶',
                      language_id: 1,
                      attr_name: '中文成分名',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 162,
                  attr_group_id: 15,
                  name: '中文INCI',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 105,
                  w: 1,
                  h: 35,
                  i: 3555184175,
                  type: 1,
                  type_json: '{"word_limit_count":100,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: 'guijiao',
                      language_id: 1,
                      attr_name: '中文INCI',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 158,
                  attr_group_id: 15,
                  name: '印刷品所用产品编码',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 140,
                  w: 1,
                  h: 35,
                  i: 2055850695,
                  type: 1,
                  type_json: '{"word_limit_count":100,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: null,
                      language_id: 1,
                      attr_name: '印刷品所用产品编码',
                      attr_tips: '',
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 124,
                  attr_group_id: 15,
                  name: '辅助码',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 140,
                  w: 1,
                  h: 35,
                  i: 1212770856,
                  type: 1,
                  type_json: '{"word_limit_count":500,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: '22',
                      language_id: 1,
                      attr_name: '辅助码',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 172,
                  attr_group_id: 15,
                  name: '开发人员',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 140,
                  w: 1,
                  h: 35,
                  i: 2165033435,
                  type: 12,
                  type_json:
                    '{"available_reference_type":2,"data_source":3,"option_type":1,"multi_attr_id":null,"options":[{"language_id":1,"options":[{"value":1563,"label":"黄佳霓"},{"value":1424,"label":"陆雪婉"},{"value":1285,"label":"李晓琪"},{"value":1034,"label":"佘晓丹"},{"value":1028,"label":"魏建辉"},{"value":989,"label":"陈涌宜"},{"value":885,"label":"李育如"},{"value":848,"label":"洪泽光"},{"value":843,"label":"陈晓萍"},{"value":773,"label":"纪铿铭"},{"value":52,"label":"李晓仪"},{"value":51,"label":"翁璇银"},{"value":50,"label":"蔡雁侨"},{"value":49,"label":"翁嘉敏"},{"value":48,"label":"黄嘉慧"},{"value":28,"label":"吴丽洪"},{"value":27,"label":"林杰妹"},{"value":26,"label":"杨妍"},{"value":25,"label":"蔡丹妮"},{"value":24,"label":"黄佳贤"}]}],"page":1,"searchStr":null,"searching":false,"total":33}',
                  language_config: [
                    {
                      value: [],
                      language_id: 1,
                      attr_name: '开发人员',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 121,
                  attr_group_id: 15,
                  name: '颜色',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: false,
                  x: 3,
                  y: 140,
                  w: 1,
                  h: 35,
                  i: 7608344341,
                  type: 6,
                  type_json:
                    '{"option_list":[{"value":1,"is_def":false,"option_label_list":[{"language_id":1,"label":"白色"}]},{"value":2,"is_def":false,"option_label_list":[{"language_id":1,"label":"黑色"}]},{"value":3,"is_def":null,"option_label_list":[{"language_id":1,"label":"黄色"}]},{"value":4,"is_def":null,"option_label_list":[{"language_id":1,"label":"透明"}]},{"value":5,"is_def":null,"option_label_list":[{"language_id":1,"label":"浅肤色"}]},{"value":6,"is_def":null,"option_label_list":[{"language_id":1,"label":"灰色"}]},{"value":7,"is_def":null,"option_label_list":[{"language_id":1,"label":"深绿色"}]},{"value":8,"is_def":null,"option_label_list":[{"language_id":1,"label":"蓝色"}]},{"value":9,"is_def":null,"option_label_list":[{"language_id":1,"label":"红色"}]},{"value":10,"is_def":null,"option_label_list":[{"language_id":1,"label":"紫色"}]},{"value":11,"is_def":null,"option_label_list":[{"language_id":1,"label":"粉色"}]},{"value":12,"is_def":null,"option_label_list":[{"language_id":1,"label":"蓝绿色"}]},{"value":13,"is_def":null,"option_label_list":[{"language_id":1,"label":"半透红色"}]},{"value":14,"is_def":null,"option_label_list":[{"language_id":1,"label":"棕色"}]},{"value":15,"is_def":null,"option_label_list":[{"language_id":1,"label":"橄榄绿"}]},{"value":16,"is_def":null,"option_label_list":[{"language_id":1,"label":"金色"}]},{"value":17,"is_def":null,"option_label_list":[{"language_id":1,"label":"荧光黄"}]},{"value":18,"is_def":null,"option_label_list":[{"language_id":1,"label":"实绿色"}]},{"value":19,"is_def":null,"option_label_list":[{"language_id":1,"label":"淡绿色"}]},{"value":20,"is_def":null,"option_label_list":[{"language_id":1,"label":"茶色"}]}]}',
                  language_config: [
                    {
                      value: null,
                      language_id: 1,
                      attr_name: '颜色',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '价格信息',
                  attr_tips: null,
                },
              ],
              id: 19,
              name: '价格信息',
              x: 0,
              y: 29,
              w: 4,
              h: 29,
              i: 6282587004,
              type: 99,
              attr_ids: [152, 153, 173, 154, 155, 156, 170],
              children: [
                {
                  id: 152,
                  attr_group_id: 19,
                  name: '采购价',
                  template_display_unit: 1,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 6947281475,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[1],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '1',
                      language_id: 1,
                      attr_name: '采购价',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 153,
                  attr_group_id: 19,
                  name: '成本价',
                  template_display_unit: 1,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 3087345498,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[1],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '2',
                      language_id: 1,
                      attr_name: '成本价',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 173,
                  attr_group_id: 19,
                  name: '基本售价',
                  template_display_unit: 1,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 3813719936,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[1],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '3',
                      language_id: 1,
                      attr_name: '基本售价',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 154,
                  attr_group_id: 19,
                  name: '国内一档价格',
                  template_display_unit: 1,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 3973505257,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[1],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '4',
                      language_id: 1,
                      attr_name: '国内一档价格',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 155,
                  attr_group_id: 19,
                  name: '国内二档价格',
                  template_display_unit: 1,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 9153362367,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[1],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '5',
                      language_id: 1,
                      attr_name: '国内二档价格',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 156,
                  attr_group_id: 19,
                  name: '国内三档价格',
                  template_display_unit: 1,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 3174134852,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[1],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '6',
                      language_id: 1,
                      attr_name: '国内三档价格',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 170,
                  attr_group_id: 19,
                  name: '美国指导价',
                  template_display_unit: 2,
                  multi_attr_value_options: null,
                  template_display_unit_precision: 0,
                  display_type: null,
                  is_must: false,
                  x: 3,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 4686716352,
                  type: 4,
                  type_json: '{"unit_type":2,"metering_unit_ids":null,"currency_unit_ids":[2],"min_value":1,"max_value":10000,"display_format":1}',
                  language_config: [
                    {
                      value: '7',
                      language_id: 1,
                      attr_name: '美国指导价',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '生产信息',
                  attr_tips: null,
                },
              ],
              id: 14,
              name: '生产信息',
              x: 0,
              y: 58,
              w: 4,
              h: 29,
              i: 3016228837,
              type: 99,
              attr_ids: [118, 128, 131, 151, 130, 129],
              children: [
                {
                  id: 118,
                  attr_group_id: 14,
                  name: '加工方式',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: false,
                  x: 0,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 8209418342,
                  type: 6,
                  type_json:
                    '{"option_list":[{"value":1,"is_def":false,"option_label_list":[{"language_id":1,"label":"直采"}]},{"value":2,"is_def":false,"option_label_list":[{"language_id":1,"label":"包材半包"}]},{"value":3,"is_def":null,"option_label_list":[{"language_id":1,"label":"包材全包"}]},{"value":4,"is_def":null,"option_label_list":[{"language_id":1,"label":"仅加工"}]},{"value":5,"is_def":null,"option_label_list":[{"language_id":1,"label":"返工"}]}]}',
                  language_config: [
                    {
                      value: null,
                      language_id: 1,
                      attr_name: '加工方式',
                      attr_tips: '请输入加工方式',
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 128,
                  attr_group_id: 14,
                  name: '所需许可证',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: false,
                  x: 1,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 2379625133,
                  type: 7,
                  type_json:
                    '{"option_list":[{"value":1,"is_def":false,"option_label_list":[{"language_id":1,"label":"化妆品生产许可证"}]},{"value":2,"is_def":false,"option_label_list":[{"language_id":1,"label":"食品生产许可证"}]},{"value":3,"is_def":null,"option_label_list":[{"language_id":1,"label":"药品生产许可证"}]},{"value":4,"is_def":null,"option_label_list":[{"language_id":1,"label":"医疗器械生产许可证"}]},{"value":5,"is_def":null,"option_label_list":[{"language_id":1,"label":"工业产品生产许可证"}]}]}',
                  language_config: [
                    {
                      value: [],
                      language_id: 1,
                      attr_name: '所需许可证',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 131,
                  attr_group_id: 14,
                  name: '产品产线',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: false,
                  x: 2,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 6582411330,
                  type: 6,
                  type_json:
                    '{"option_list":[{"value":1,"is_def":false,"option_label_list":[{"language_id":1,"label":"软管"}]},{"value":2,"is_def":false,"option_label_list":[{"language_id":1,"label":"针管"}]},{"value":3,"is_def":null,"option_label_list":[{"language_id":1,"label":"牙胶"}]},{"value":4,"is_def":null,"option_label_list":[{"language_id":1,"label":"膏霜灌装"}]},{"value":5,"is_def":null,"option_label_list":[{"language_id":1,"label":"水类灌装"}]},{"value":6,"is_def":null,"option_label_list":[{"language_id":1,"label":"粉末类"}]},{"value":7,"is_def":null,"option_label_list":[{"language_id":1,"label":"蜡基类"}]},{"value":8,"is_def":null,"option_label_list":[{"language_id":1,"label":"其他手工"}]}]}',
                  language_config: [
                    {
                      value: null,
                      language_id: 1,
                      attr_name: '产品产线',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 151,
                  attr_group_id: 14,
                  name: '制造商',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 5312294173,
                  type: 12,
                  type_json:
                    '{"available_reference_type":2,"data_source":2,"option_type":1,"multi_attr_id":null,"options":[{"language_id":1,"options":[{"label":"海口维木康跨境电商有限公司","value":54},{"label":"香港格嵐昕供應鏈有限公司","value":53},{"label":"香港沃斐塔科技有限公司","value":52},{"label":"香港拓克斯科技有限公司","value":51},{"label":"汕头市中之月生物科技有限公司","value":50},{"label":"香港欧好生物科技有限公司","value":49},{"label":"香港提穆跨境供应链有限公司","value":48},{"label":"香港索瑞塔品牌管理有限公司","value":47},{"label":"香港泽璐娜品牌咨询公司","value":46},{"label":"香港坤环盛供应链有限公司","value":45},{"label":"香港蓝布芙品牌管理有限公司","value":44},{"label":"香港斐慕珂品牌管理有限公司","value":43},{"label":"⾹港露瑪維貿易有限公司","value":42},{"label":"香港瑞喵克品牌管理有限公司","value":41},{"label":"香港布薩汀品牌管理有限公司","value":40},{"label":"香港依歐好生物科技有限公司","value":39},{"label":"香港芙洛皙品牌管理有限公司","value":38},{"label":"香港薇陸可品牌管理有限公司","value":37},{"label":"香港諾圖力品牌管理有限公司","value":36},{"label":"香港赫彌索品牌管理有限公司","value":35}]}],"page":1,"searchStr":null,"searching":false,"total":54}',
                  language_config: [
                    {
                      value: [],
                      language_id: 1,
                      attr_name: '制造商',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 130,
                  attr_group_id: 14,
                  name: '生产过的加工厂',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 1245252350,
                  type: 1,
                  type_json: '{"word_limit_count":500,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: 'XL',
                      language_id: 1,
                      attr_name: '生产过的加工厂',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 129,
                  attr_group_id: 14,
                  name: '建议加工厂',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 35,
                  w: 1,
                  h: 35,
                  i: 8999842922,
                  type: 1,
                  type_json: '{"word_limit_count":500,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: 'WL',
                      language_id: 1,
                      attr_name: '建议加工厂',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '采购信息',
                  attr_tips: null,
                },
              ],
              id: 16,
              name: '采购信息',
              x: 0,
              y: 87,
              w: 4,
              h: 29,
              i: 3447902985,
              type: 99,
              attr_ids: [147, 171, 127],
              children: [
                {
                  id: 147,
                  attr_group_id: 16,
                  name: '默认供应商',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 1432763380,
                  type: 12,
                  type_json:
                    '{"available_reference_type":3,"data_source":1002,"option_type":1,"multi_attr_id":null,"options":[{"language_id":1,"options":[{"value":80005177,"label":"河南环孚新材料科技有限公司"},{"value":80005176,"label":"义乌市香澜日用品厂（个体工商户）"},{"value":80005175,"label":"衡水悦江塑料制品有限公司"},{"value":80005174,"label":"绍兴市上虞日昌塑料制品厂"},{"value":80005173,"label":"汕头市汇诚印务有限公司"},{"value":80005172,"label":"广州达济堂医药"},{"value":80005171,"label":"东莞市和晟复合材料有限公司-成品"},{"value":80005170,"label":"义乌市哲胜磁业有限公司"},{"value":80005169,"label":"东莞市泰弘纸制品有限公司"},{"value":80005168,"label":"亿发玻璃瓶子泵头"},{"value":80005167,"label":"沧县东驰塑料制品厂"},{"value":80005166,"label":"广州市易顺联体育用品有限公司"},{"value":80005165,"label":"东莞市金晟体育用品有限公司"},{"value":80005164,"label":"安徽同物堂药业有限公司"},{"value":80005163,"label":"深圳市光明新区公明百佳兴日用品商行"},{"value":80005162,"label":"东莞长安欧诺曼新材料厂-配件"},{"value":80005161,"label":"浙江诗诺化妆品有限公司"},{"value":80005160,"label":"汕头金派文创科技有限公司"},{"value":80005159,"label":"上虞红喜塑业有限公司 "},{"value":80005158,"label":"义乌市寻鑫日用品厂"}]}],"page":1,"searchStr":null,"searching":false,"total":5078}',
                  language_config: [
                    {
                      value: [],
                      language_id: 1,
                      attr_name: '默认供应商',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 171,
                  attr_group_id: 16,
                  name: '供应商商品编码',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 7388765285,
                  type: 1,
                  type_json: '{"word_limit_count":100,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: '12334456',
                      language_id: 1,
                      attr_name: '供应商商品编码',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 127,
                  attr_group_id: 16,
                  name: '采购平均回货周期',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 2,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 1923596778,
                  type: 3,
                  type_json: '{"precision":2,"rounding_type":1,"min_value":1,"max_value":10000,"display_format":null}',
                  language_config: [
                    {
                      value: '10.00',
                      language_id: 1,
                      attr_name: '采购平均回货周期',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '销售信息',
                  attr_tips: null,
                },
              ],
              id: 18,
              name: '销售信息',
              x: 0,
              y: 116,
              w: 4,
              h: 29,
              i: 6609951825,
              type: 99,
              attr_ids: [125, 150],
              children: [
                {
                  id: 125,
                  attr_group_id: 18,
                  name: '店铺名称',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 6354815614,
                  type: 1,
                  type_json: '{"word_limit_count":500,"default_value_config":[{"language_id":1,"default_value":null}]}',
                  language_config: [
                    {
                      value: null,
                      language_id: 1,
                      attr_name: '店铺名称',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 150,
                  attr_group_id: 18,
                  name: '客户',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 1,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 6168650128,
                  type: 12,
                  type_json:
                    '{"available_reference_type":3,"data_source":1001,"option_type":1,"multi_attr_id":null,"options":[{"language_id":1,"options":[{"value":1053273,"label":"趁风将起"},{"value":1053011,"label":"唐河县聚达坤商贸有限公司"},{"value":1052494,"label":"扬州雪之瑞百货商行(个人独资)"},{"value":1052286,"label":"Tiktok全托管-汕头拓洋行跨境供应链有限公司-TopOcean"},{"value":1052248,"label":"Tiktok全托管-汕头市浩赫特汽车用品有限公司-Hectory"},{"value":1052084,"label":"香港赫彌索品牌管理有限公司"},{"value":1051522,"label":"TikTok欧盟跨境-汕头市谷吉尔健康食品有限公司-德国"},{"value":1051521,"label":"TikTok欧盟跨境-广东依欧好生物科技有限公司-EELHOE-德国"},{"value":1051519,"label":"TikTok美国跨境-汕头市绝菲士家清日用品有限公司-JueFeiShi"},{"value":1051518,"label":"TikTok美国跨境店-汕头易环汇跨境供应链有限公司"},{"value":1051517,"label":"TikTok美国跨境-汕头捷跨客跨境供应链有限公司"},{"value":1051516,"label":"Tiktok美国跨境-广州兰尼丝卡生物科技有限公司-VelvetBloom"},{"value":1051515,"label":"Tiktok美国跨境店-广州奥禾拉生物科技有限公司—VelvetSkinLab"},{"value":1051514,"label":"Tiktok美国跨境-汕头万品汇跨境供应链有限公司-VerveLux"},{"value":1051511,"label":"TikTok美国跨境-汕头市逗某玩具有限公司-Domentoy"},{"value":1051509,"label":"Tiktok美国跨境-香港賽未斯科技有限公司-xseaaauu"},{"value":1051508,"label":"亚马逊国际（美国）-株洲痞崇商贸有限公司-us"},{"value":1051507,"label":"亚马逊（美国）-河津市泽赢贸易有限公司-us"},{"value":1051506,"label":"速卖通海外托管-深圳西之月选品跨境供应链有限公司"},{"value":1051504,"label":"速卖通海外托管-汕头市翼浩日化科技有限公司"}]}],"page":1,"searchStr":null,"searching":false,"total":5896}',
                  language_config: [
                    {
                      value: [],
                      language_id: 1,
                      attr_name: '客户',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '组织信息',
                  attr_tips: null,
                },
              ],
              id: 21,
              name: '组织信息',
              x: 0,
              y: 145,
              w: 4,
              h: 29,
              i: 4318627700,
              type: 99,
              attr_ids: [163, 164, 165],
              children: [
                {
                  id: 163,
                  attr_group_id: 21,
                  name: '创建组织',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: true,
                  x: 0,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 9165884232,
                  type: 6,
                  type_json: '{"option_list":[{"value":1,"is_def":true,"option_label_list":[{"language_id":1,"label":"西月集团"}]}]}',
                  language_config: [
                    {
                      value: 1,
                      language_id: 1,
                      attr_name: '创建组织',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 164,
                  attr_group_id: 21,
                  name: '使用组织',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: true,
                  x: 1,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 4092025749,
                  type: 6,
                  type_json: '{"option_list":[{"value":1,"is_def":true,"option_label_list":[{"language_id":1,"label":"西月集团"}]}]}',
                  language_config: [
                    {
                      value: 1,
                      language_id: 1,
                      attr_name: '使用组织',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
                {
                  id: 165,
                  attr_group_id: 21,
                  name: '采购组织',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: 2,
                  is_must: true,
                  x: 2,
                  y: 0,
                  w: 1,
                  h: 35,
                  i: 7082862813,
                  type: 6,
                  type_json: '{"option_list":[{"value":1,"is_def":true,"option_label_list":[{"language_id":1,"label":"西月总部"}]}]}',
                  language_config: [
                    {
                      value: 1,
                      language_id: 1,
                      attr_name: '采购组织',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
            {
              is_customize: false,
              is_display_border: false,
              is_display_serial_number: false,
              is_support_adding_rows: false,
              table_rows: 0,
              language_config: [
                {
                  language_id: 1,
                  attr_name: '文档信息',
                  attr_tips: null,
                },
              ],
              id: 17,
              name: '文档信息',
              x: 0,
              y: 174,
              w: 4,
              h: 29,
              i: 7508846275,
              type: 99,
              attr_ids: [148],
              children: [
                {
                  id: 148,
                  attr_group_id: 17,
                  name: '图片',
                  template_display_unit: null,
                  multi_attr_value_options: null,
                  template_display_unit_precision: null,
                  display_type: null,
                  is_must: false,
                  x: 0,
                  y: 0,
                  w: 4,
                  h: 122,
                  i: 6983070055,
                  type: 9,
                  type_json: '{"image_count":3,"image_types":[1,2,3],"image_size":5,"image_size_unit":2}',
                  language_config: [
                    {
                      value: null,
                      language_id: 1,
                      attr_name: '图片',
                      attr_tips: null,
                    },
                  ],
                  single_line_text_type: null,
                  multi_line_text_type: null,
                  number_input_type: null,
                  unit_input_type: null,
                  date_time_picker_type: null,
                  radio_type: null,
                  check_box_type: null,
                  image_type: null,
                  video_type: null,
                  file_type: null,
                  reference_type: null,
                },
              ],
            },
          ],
        },
      }
    },
  },
] as MockMethod[]
