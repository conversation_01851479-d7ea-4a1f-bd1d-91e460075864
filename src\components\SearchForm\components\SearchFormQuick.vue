<template>
  <a-space :size="4">
    <a-button @click="handleShowQuick">另存为快捷查询</a-button>
    <a-tag color="#f2f2f2" closable @click="setQuickSearch(item)" @close.prevent="handleDelQuick(i)" v-for="(item, i) in quickQueryList" :key="item.label" class="cursor-pointer quick-tag">
      {{ item.label || '快捷查询' + (i + 1) }}
    </a-tag>
  </a-space>
  <a-modal :width="350" @afterOpenChange="formRef.clearValidate()" v-model:open="quickVisible" title="另存为快捷查询" okText="确定" cancelText="取消" @ok="handleSaveQuick">
    <a-form style="margin-top: 20px" :colon="false" :label-col="{ style: { width: '130px', marginRight: '20px' } }" ref="formRef" :model="formData">
      <a-form-item
        label=""
        name="label"
        :rules="[
          { required: true, message: '请输入快捷查询命名' },
          { max: 20, message: '输入内容不可超过20字符' },
        ]"
      >
        <a-input auto-focus v-model:value="formData.label" placeholder="请输入快捷查询命名" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { GetQuickQuery, SaveQuickQuery } from '@/servers/Common'
import { FormItem } from '../type'

const props = defineProps<{
  pageType: number
}>()

const emits = defineEmits<{
  (e: 'search'): void
}>()

const formArr = defineModel<FormItem[]>('formArr', { required: true })

const { proxy }: any = getCurrentInstance()

// 快捷查询显示
const quickVisible = ref(false)

// 快捷查询表单
const formData = ref({
  label: '',
})
const formRef = ref()

const quickQueryList = ref<any[]>([])

const setQuickSearch = (item) => {
  formArr.value.forEach((x) => {
    x.value = null
    for (const key in item.value) {
      if (x.key === key) {
        x.value = item.value[key]
      }
    }
  })
  nextTick(() => emits('search'))
}

const handleShowQuick = async () => {
  await formRef.value?.clearValidate()
  if (!formArr.value.find((e) => e.value || e.value === 0)) {
    message.warning('未进行筛选')
  } else {
    quickVisible.value = true
    formData.value.label = ''
  }
}

// 保存快捷查询
const handleSaveQuick = async () => {
  try {
    await formRef.value.validateFields()
    if (quickQueryList.value.find((e) => e.label === formData.value.label)) {
      message.warning('存在相同命名的快捷查询')
      return
    }

    const obj = {}
    formArr.value.forEach((x) => {
      if (x.value || x.value === 0) {
        obj[x.key] = x.value
      }
    })
    quickQueryList.value.push({
      label: formData.value.label,
      value: obj,
    })

    SaveQuickQuery({ page_type: props.pageType, quick_query_data: JSON.stringify(quickQueryList.value) })
    quickVisible.value = false
    formData.value.label = ''
    await formRef.value?.clearValidate()
  } catch (e) {
    console.log(e)
  }
}

// 删除快捷记录
const handleDelQuick = (i) => {
  proxy.$confirm.show({
    title: '删除快捷方式',
    type: 'del',
    width: 440,
    content: h('div', null, '此操作不可恢复，确定要删除该快捷方式吗？'),
    onOk: () => {
      quickQueryList.value.splice(i, 1)
      SaveQuickQuery({ page_type: props.pageType, quick_query_data: JSON.stringify(quickQueryList.value) })
    },
  })
}

const getInitQuick = async () => {
  try {
    const res = await GetQuickQuery({ page_type: props.pageType })
    if (res && res.data && res.data.quick_query_data) {
      quickQueryList.value = JSON.parse(res.data.quick_query_data)
    } else {
      quickQueryList.value = []
    }
  } catch (error) {
    console.warn('获取快捷查询失败:', error)
    quickQueryList.value = []
  }
}

onMounted(() => {
  getInitQuick()
})
</script>

<style scoped lang="scss">
.quick-tag {
  height: 28px !important;
  padding: 4px 12px !important;
  cursor: pointer;
  user-select: none;
  font-size: 12px;
  align-items: center;
  display: inline-block;
  color: #666 !important;
  :deep(.ant-tag-close-icon) {
    color: silver !important;
    font-size: 10px;
    margin-left: 8px;
  }
}
</style>
