<template>
  <a-modal v-model:open="openModal" title="上传文件" @ok="showModal" centered @cancel="handleCancel" :maskClosable="false">
    <BaseFileUploadMode v-model:file-list="fileList" :multiple="true" :before-upload="beforeUpload" @delete="handleDelete" :max-count="3" />
    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import BaseFileUploadMode from '@/components/BaseFileUploadMode/index.vue'
import { UploadCommonFile } from '@/servers/Common'

const emit = defineEmits(['update'])
// 文件列表
const fileList = defineModel<any[]>('fileList', { required: true })
// 是否显示弹窗
const openModal = ref(false)
// 显示弹窗
const showModal = () => {
  openModal.value = true
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}
// 确定
const handleSubmit = () => {
  // 检查是否有文件还在上传
  const uploading = fileList.value.some((file) => file.status !== 'done' || !file.id)
  if (uploading) {
    message.warning('有文件正在上传，请稍后')
    return
  }
  openModal.value = false
  emit('update')
}
// 上传
const beforeUpload = async (file) => {
  if (fileList.value.length >= 3) {
    message.error('最多只能上传3个文件')
    return false
  }

  const fileExt = file.name.split('.').pop().toLowerCase()

  if (!['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
    message.error('文件格式错误')
    return false
  }
  // 新增：文件类型限制
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    message.error('文件格式错误')
    return false
  }
  // 新增：文件大小限制为5MB
  const maxSize = 5 * 1024 * 1024 // 5MB
  if (file.size > maxSize) {
    message.error('单个文件不能超过5MB')
    return false
  }

  file.status = 'uploading'
  fileList.value.push(file)
  const formData = new FormData()
  formData.append('files', file)
  const res = await UploadCommonFile('Default', formData)
  fileList.value.splice(fileList.value.indexOf(file), 1)
  file.status = 'done'
  file.id = res.data[0].id
  fileList.value.push(file)
  return false
}
// 删除文件
const handleDelete = (file) => {
  fileList.value.splice(fileList.value.indexOf(file), 1)
}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss"></style>
