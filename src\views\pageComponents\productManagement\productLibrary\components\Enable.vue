<template>
  <a-modal v-model:open="openModal" title="提示" @ok="showModal" centered @cancel="handleCancel">
    <div class="flex flex-col gap-20" v-if="enableType">
      <span>{{ texts.enable.title }}</span>
      <span class="text1">{{ texts.enable.tip1 }}</span>
      <span>{{ texts.enable.confirm }}</span>
    </div>
    <div class="flex flex-col gap-20" v-else>
      <span>{{ texts.disable.title }}</span>
      <div class="flex flex-col">
        <span class="text2">{{ texts.disable.tip1 }}</span>
        <span class="text3">{{ texts.disable.tip2 }}</span>
        <span class="text4">{{ texts.disable.tip3 }}</span>
      </div>
      <span>{{ texts.disable.confirm }}</span>
    </div>
    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleEnable">{{ enableType ? texts.enable.button : texts.disable.button }}</a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { EnableProduct } from '@/servers/ProductLibrary'

const emit = defineEmits(['search'])
// 是否显示弹窗
const openModal = ref(false)
// 启用禁用
const enableType = ref(false)
// 商品id
const productId = ref(0)

const texts = {
  enable: {
    title: '即将启用该商品，启用后：',
    tip1: '● 启用的商品，将重新推送平台进行 选品/议价/售卖。',
    confirm: '确定启用该商品吗？',
    button: '启用',
  },
  disable: {
    title: '即将停用该商品，停用后：',
    tip1: '● 将通知平台安排该商品下架。',
    tip2: '● 商品下架后，商品页会显示“已下架”或“商品不存在”，用户无法购买。',
    tip3: '● 停用后重新启用，商品将重新进入“待选品”状态，无法直接上架。',
    confirm: '确定停用该商品吗？',
    button: '停用',
  },
}
// 显示弹窗
const showModal = (id: number, type: string) => {
  openModal.value = true
  productId.value = id
  if (type === 'enable') {
    enableType.value = true
  } else {
    enableType.value = false
  }
}
const handleCancel = () => {
  openModal.value = false
}
const handleEnable = () => {
  openModal.value = false
  const params = {
    id: productId.value,
  }
  EnableProduct(params)
    .then(() => {
      message.success('操作成功')
      emit('search')
    })
    .catch(() => {
      message.error('操作失败')
    })
}
defineExpose({
  showModal,
})
</script>
<style scoped lang="scss">
.text1 {
  margin-left: 20px;
}

.text2 {
  margin-left: 20px;
}

.text3 {
  margin-left: 20px;
}

.text4 {
  margin-left: 20px;
}
</style>
