<template>
  <teleport to="body">
    <div class="export-notification-container">
      <transition-group name="notification" tag="div">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="export-notification"
          :class="[`notification-${notification.type}`]"
          @mouseenter="pauseTimer(notification.id)"
          @mouseleave="resumeTimer(notification.id)"
        >
          <div class="notification-content">
            <div class="notification-icon">
              <loading-outlined v-if="notification.type === 'progress'" spin />
              <check-circle-outlined v-else-if="notification.type === 'complete'" />
              <close-circle-outlined v-else-if="notification.type === 'failed'" />
            </div>
            <div class="notification-text">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
            </div>
            <div class="notification-actions">
              <a-button v-if="notification.type === 'complete'" type="primary" size="small" @click="handleDownload(notification)">立即下载</a-button>
              <a-button size="small" @click="closeNotification(notification.id)" class="close-btn">
                <close-outlined />
              </a-button>
            </div>
          </div>
          <div v-if="notification.type !== 'progress'" class="notification-progress" :style="{ width: `${notification.progress}%` }"></div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { LoadingOutlined, CheckCircleOutlined, CloseCircleOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { DownloadFile } from '@/servers/DownloadCenter'

// 组件卸载时清理
import { onUnmounted } from 'vue'

interface NotificationData {
  id: string
  type: 'progress' | 'complete' | 'failed'
  title: string
  message: string
  taskId?: string
  fileId?: string
  fileName?: string
  downloadUrl?: string
  progress: number
  timer?: number
  isPaused: boolean
}

const notifications = ref<NotificationData[]>([])
const timers = reactive<Record<string, number>>({})

// 显示通知
const showNotification = (data: Omit<NotificationData, 'id' | 'progress' | 'isPaused'>) => {
  const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  const notification: NotificationData = {
    id,
    progress: 100,
    isPaused: false,
    ...data,
  }

  // 如果是进度通知，检查是否已存在相同任务的通知
  if (notification.type === 'progress' && notification.taskId) {
    const existingIndex = notifications.value.findIndex((n) => n.taskId === notification.taskId && n.type === 'progress')
    if (existingIndex !== -1) {
      // 更新现有通知
      notifications.value[existingIndex] = notification
      return
    }
  }

  // 如果是完成通知，移除对应的进度通知
  if (notification.type === 'complete' && notification.taskId) {
    const progressIndex = notifications.value.findIndex((n) => n.taskId === notification.taskId && n.type === 'progress')
    if (progressIndex !== -1) {
      closeNotification(notifications.value[progressIndex].id)
    }
  }

  notifications.value.push(notification)

  // 对于非进度通知，设置自动关闭定时器
  if (notification.type !== 'progress') {
    startTimer(id)
  }
}

// 开始定时器
const startTimer = (id: string) => {
  const notification = notifications.value.find((n) => n.id === id)
  if (!notification || notification.isPaused) return

  const duration = 5000 // 5秒
  const interval = 50 // 50ms更新一次
  const step = (interval / duration) * 100

  timers[id] = window.setInterval(() => {
    const notif = notifications.value.find((n) => n.id === id)
    if (!notif || notif.isPaused) return

    notif.progress -= step
    if (notif.progress <= 0) {
      closeNotification(id)
    }
  }, interval)
}

// 暂停定时器
const pauseTimer = (id: string) => {
  const notification = notifications.value.find((n) => n.id === id)
  if (notification) {
    notification.isPaused = true
  }
  if (timers[id]) {
    clearInterval(timers[id])
    delete timers[id]
  }
}

// 恢复定时器
const resumeTimer = (id: string) => {
  const notification = notifications.value.find((n) => n.id === id)
  if (notification && notification.type !== 'progress') {
    notification.isPaused = false
    startTimer(id)
  }
}

// 关闭通知
const closeNotification = (id: string) => {
  const index = notifications.value.findIndex((n) => n.id === id)
  if (index !== -1) {
    notifications.value.splice(index, 1)
  }
  if (timers[id]) {
    clearInterval(timers[id])
    delete timers[id]
  }
}

// 处理下载
const handleDownload = async (notification: NotificationData) => {
  try {
    if (notification.fileId && notification.fileName) {
      await DownloadFile(Number(notification.fileId), notification.fileName)
      message.success('文件下载成功')
    } else if (notification.downloadUrl) {
      // 使用下载链接
      const link = document.createElement('a')
      link.href = notification.downloadUrl
      link.download = notification.fileName || 'download'
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      message.success('文件下载成功')
    } else {
      message.error('下载信息不完整')
    }
  } catch (error) {
    console.error('下载失败:', error)
    message.error('下载失败，请重试')
  }
}

// 导出函数供外部调用
defineExpose({
  showNotification,
})

// 清理定时器
const cleanup = () => {
  Object.values(timers).forEach((timer) => clearInterval(timer))
}
onUnmounted(cleanup)
</script>

<style scoped>
.export-notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.export-notification {
  width: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 12px;
  overflow: hidden;
  pointer-events: auto;
  position: relative;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.notification-icon {
  font-size: 20px;
  margin-top: 2px;
}

.notification-progress .notification-icon {
  color: #1890ff;
}

.notification-complete .notification-icon {
  color: #52c41a;
}

.notification-failed .notification-icon {
  color: #ff4d4f;
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  color: #595959;
  line-height: 1.4;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  border: none;
  box-shadow: none;
  padding: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-progress {
  height: 3px;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  transition: width 0.05s linear;
  position: absolute;
  bottom: 0;
  left: 0;
}

.notification-complete .notification-progress {
  background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
}

.notification-failed .notification-progress {
  background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
}

/* 动画效果 */
.notification-enter-active {
  transition: all 0.3s ease-out;
}

.notification-leave-active {
  transition: all 0.3s ease-in;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
