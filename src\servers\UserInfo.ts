// 用户信息接口 - 新的 /XY/User/ 路径
import { requestXY } from './request'

// 获取当前用户菜单权限
export const GetUserPermissions = () => {
  return requestXY({ url: '/User/GetUserPermissions' }, 'GET')
}

// 获取当前用户信息
export const GetUserInfo = () => {
  return requestXY({ url: '/User/GetUserInfo' }, 'GET')
}

// 获取当前用户账号列表
export const GetUserAccountList = () => {
  return requestXY({ url: '/User/GetUserAccountList' }, 'GET')
}

// 获取当前用户角色信息
export const GetUserRoleInfo = () => {
  return requestXY({ url: '/User/GetUserRoleInfo' }, 'GET')
}

// 判断当前用户是否拥有指定角色
export const CheckUserIfHasRole = (role_code: string) => {
  return requestXY({ url: `/User/CheckUserIfHasRole?role_code=${role_code}` }, 'GET')
}

// 获取当前登陆用户管理的关联公司列表
export const GetContactCompanies = () => {
  return requestXY({ url: '/User/GetContactCompanies' }, 'GET')
}
