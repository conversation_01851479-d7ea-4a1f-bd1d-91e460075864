// 用户管理接口 - 合并版本
import { request, requestXY } from './request'

// ==================== 新接口 (使用 /XY/UserManager/ 路径) ====================

// 获取用户列表
export const GetList = (data) => {
  return requestXY({ url: '/UserManager/GetList', data }, 'POST')
}

// 更新账号角色（内部账号）
export const UpdateAccountRole = (data) => {
  return requestXY({ url: '/UserManager/UpdateAccountRole', data }, 'POST')
}

// 更新外部账号
export const UpdateOuterAccount = (data) => {
  return requestXY({ url: '/UserManager/UpdateOuterAccount', data }, 'POST')
}

// 批量停用/启用外部用户
export const UpdateOuterStatus = (data) => {
  return requestXY({ url: '/UserManager/UpdateOuterStatus', data }, 'POST')
}

// 获取本地账号日志
export const GetOpLogInfos = (data: { page: number; id: string; pageSize?: number; sortField?: string; sortType?: string; user_id?: any; account_id?: any }) => {
  return requestXY({ url: '/Common/GetOpLogInfos', data }, 'POST')
}

// 获取UMC账号日志
export const GetUmcOpLogInfos = (data: { page: number; pageSize: number; sortField?: string; sortType?: string; id?: string; user_id?: any; account_id?: any }) => {
  return requestXY({ url: '/UserManager/GetUmcOpLogInfos', data }, 'POST')
}

// 获取我的下属列表
export const GetAllSubordinates = (data) => {
  return requestXY({ url: '/UserManager/GetAllSubordinates', data }, 'POST')
}

// 批量设置用户的关联关系单位
export const UpdateUserContacts = (data) => {
  return requestXY({ url: '/UserManager/UpdateUserContacts', data }, 'POST')
}

// 获取部门树状下拉框, 带有是否设置关联关系标识
export const GetDepartmentContactTreeList = (data) => {
  return requestXY({ url: '/UserManager/GetDepartmentContactTreeList', data }, 'POST')
}

// 批量设置组织架构关联管理单位
export const UpdateDepartmentContacts = (data) => {
  return requestXY({ url: '/UserManager/UpdateDepartmentContacts', data }, 'POST')
}

// 获取[关联常用用户]列表
export const ContactUserGetList = (data) => {
  return requestXY({ url: '/UserManager/ContactUserGetList', data }, 'POST')
}

// 获取[关联常用用户分组及用户]树形列表
export const ContactUserGetTree = (data) => {
  return requestXY({ url: '/UserManager/ContactUserGetTree', data }, 'POST')
}

// 更新[关联常用用户]
export const UpdateContactUser = (data) => {
  return requestXY({ url: '/UserManager/UpdateContactUser', data }, 'POST')
}

// 删除[关联常用用户]
export const DeleteContactUser = (data) => {
  return requestXY({ url: '/UserManager/DeleteContactUser', data }, 'POST')
}

// 获取[关联常用用户分组]列表
export const ContactUserGroupGetList = (data) => {
  return requestXY({ url: '/UserManager/ContactUserGroupGetList', data }, 'POST')
}

// 更新[关联常用用户分组]
export const UpdateContactUserGroup = (data) => {
  return requestXY({ url: '/UserManager/UpdateContactUserGroup', data }, 'POST')
}

// 删除[关联常用用户分组]
export const DeleteContactUserGroup = (data) => {
  return requestXY({ url: '/UserManager/DeleteContactUserGroup', data }, 'POST')
}

// 新增外部用户账号
export const AddUserAccount = (data) => {
  return requestXY({ url: '/UserManager/AddUserAccount', data }, 'POST')
}

// 新增账号 - 新的API接口
export const AddAccount = (data) => {
  return requestXY({ url: '/UserManager/AddAccount', data }, 'POST')
}

// 获取账号下拉选项
export const GetAccountSelectOption = (data) => {
  return requestXY({ url: '/UserManager/GetAccountSelectOption', data }, 'POST')
}

// 获取当前用户信息
export const GetUserInfo = (data) => {
  return requestXY({ url: '/User/GetUserInfo', data }, 'GET')
}

// 同步公司信息
export const SyncCompanyInfo = () => {
  return requestXY({ url: '/User/SyncCompanyInfo' }, 'GET')
}

// ==================== 旧接口 (仍在使用，保持兼容性) ====================

// 新增用户 - 仍在使用
export const Add = (data) => {
  return request({ url: '/api/UserManager/Add', data })
}

// 删除外部用户 - 仍在使用
export const Delete = (data) => {
  return request({ url: '/api/UserManager/Delete', data })
}

// 重置密码 - 仍在使用
export const ResetPwd = (data) => {
  return request({ url: '/api/UserManager/ResetPwd', data })
}

// 获取外部用户详情 - 仍在使用
export const DetailsExtraByEdit = (data) => {
  return request({ url: '/api/User/GetUserById', data }, 'GET')
}

// 获取部门下拉框 - 仍在使用
export const GetDepartmentSelectOption = (data) => {
  return request({ url: '/api/UserManager/GetDepartmentTreeList', data })
}

// 获取用户下拉列表 - 仍在使用
export const GetUserOptions = (data) => {
  return request({ url: '/api/UserManager/getUserScreen', data })
}

// 获取部门树状下拉框 - 仍在使用
export const GetDepartmentTreeList = (data?: any) => {
  return request({ url: '/api/UserManager/GetDepartmentTreeList', data })
}

// 获取公司列表 - 仍在使用
export const GetCompanyList = (data?: any) => {
  return request({ url: '/api/UserManager/GetDepartmentTreeList', data })
}

// ==================== 兼容性别名接口 ====================

// 兼容旧接口名
export const GetUserList = GetList
export const UpdateInner = UpdateAccountRole
export const UpdateExtra = UpdateOuterAccount
export const UpdateExtraStatus = UpdateOuterStatus
export const Log = GetOpLogInfos
