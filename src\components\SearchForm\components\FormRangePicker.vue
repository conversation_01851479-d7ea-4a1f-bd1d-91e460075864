<template>
  <a-range-picker
    :placeholder="item.placeholder"
    v-model:value="item.value"
    :picker="item.picker"
    :valueFormat="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
    :show-time="item.showTime"
    @change="
      () =>
        (item.value = [
          dayjs(item.value[0])
            .startOf('day')
            .format(item.valueFormat || 'YYYY-MM-DD HH:mm:ss'),
          dayjs(item.value[1])
            .endOf('day')
            .format(item.valueFormat || 'YYYY-MM-DD HH:mm:ss'),
        ])
    "
    class="w-284px"
    v-bind="item"
  />
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { FormItemType } from '../type'

defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'range-picker'>>('item', { required: true })
</script>

<style scoped></style>
