<template>
  <div class="loginMainBox">
    <div class="loginBox">
      <div class="titleBox">
        <img src="../assets/image/logo-o.png" alt="" />
        <div>
          <div>欢迎登录</div>
          <div>SRS供应商门户</div>
        </div>
      </div>

      <!-- 加载动画和状态 -->
      <div class="loading-container">
        <div class="loading-icon">
          <div class="spinner"></div>
        </div>
        <div class="loading-text">
          <div class="main-text">{{ loadingText }}</div>
          <div class="sub-text">{{ subText }}</div>
        </div>
      </div>

      <!-- 手动登录按钮 -->
      <a-button @click="umcLogin" :loading="redirecting" class="umc-login">
        {{ redirecting ? '跳转中...' : '点击登录' }}
      </a-button>
    </div>
    <div class="beian">
      <span>© 广东云跨易网络科技有限公司 版权所有</span>
      <div class="line"></div>
      <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2024317142号-1</a>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { LoginRedirect } from '@/servers/UmcAuth'
import { message } from 'ant-design-vue'

const router = useRouter()
const redirecting = ref(false)
const loadingText = ref('正在跳转到统一登录平台...')
const subText = ref('请稍候')

onMounted(() => {
  // 检查是否已经登录且Token未过期
  const userData = localStorage.getItem('userData')
  if (userData) {
    try {
      const userDataObj = JSON.parse(userData)

      // 检查Token是否过期（10分钟）
      const loginTime = userDataObj.login_time || 0
      const currentTime = Math.floor(Date.now() / 1000)
      const expireTime = 10 * 60 // 10分钟
      const isTokenExpired = currentTime - loginTime > expireTime

      if (isTokenExpired) {
        if (import.meta.env.VITE_APP_ENV === 'development') {
          console.log('Token已过期，清除用户数据')
        }
        localStorage.removeItem('userData')
      } else {
        // Token未过期，重定向到主页面
        const menuList = userDataObj.permissions_infos
        if (menuList && menuList.length > 0) {
          const firstPage = menuList[0]?.children?.length > 0 ? menuList[0].children[0].path : menuList[0].path
          if (import.meta.env.VITE_APP_ENV === 'development') {
            console.log('用户已登录且Token有效，重定向到:', firstPage)
          }
          router.push(firstPage)
          return
        }
      }
    } catch (error) {
      console.error('解析用户数据失败:', error)
      localStorage.removeItem('userData')
    }
  }

  // 检查是否是UMC回调
  const urlParams = new URLSearchParams(window.location.search)
  const codeFromUrl = urlParams.get('code')
  const codeFromStorage = localStorage.getItem('code')

  if (codeFromUrl || codeFromStorage) {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('检测到UMC回调参数，跳过自动UMC登录')
    }
    return
  }

  // 检查退出登录Cookie
  if (checkLogoutCookie()) {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('检测到退出登录Cookie，跳过自动UMC登录')
    }
    return
  }

  // 自动跳转到UMC登录（延迟1秒）
  setTimeout(() => {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log('自动跳转到UMC登录')
    }
    umcLogin()
  }, 1500)
})

// 检查退出登录Cookie
const checkLogoutCookie = () => {
  // 检查是否存在退出登录标识Cookie
  // Cookie名称：isLogout，值：true
  const cookies = document.cookie.split(';')
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'isLogout' && value === 'true') {
      if (import.meta.env.VITE_APP_ENV === 'development') {
        console.log('检测到退出登录Cookie:', { name, value })
      }
      return true
    }
  }

  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('未检测到退出登录Cookie，允许自动跳转UMC登录')
  }
  return false
}

// 清除退出登录Cookie
const clearLogoutCookie = () => {
  // 清除退出登录标识Cookie
  document.cookie = 'isLogout=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/'
  if (import.meta.env.VITE_APP_ENV === 'development') {
    console.log('已清除退出登录Cookie')
  }
}

// 动态获取当前页面的IP和端口
const getCurrentHostInfo = () => {
  const protocol = window.location.protocol // http: 或 https:
  const hostname = window.location.hostname // IP地址或域名
  const port = window.location.port // 端口号

  // 构建完整的主机信息
  let hostInfo = `${protocol}//${hostname}`
  if (port) {
    hostInfo += `:${port}`
  }

  return hostInfo
}

// UMC统一登录
const umcLogin = async () => {
  if (redirecting.value) return

  redirecting.value = true
  loadingText.value = '正在获取登录地址...'
  subText.value = '请稍候'

  // 清除退出登录Cookie（用户主动登录时清除）
  clearLogoutCookie()

  try {
    // 准备请求参数
    let ipParam: string | undefined

    if (import.meta.env.VITE_APP_ENV === 'development') {
      // 开发环境：传递当前页面的IP和端口
      ipParam = getCurrentHostInfo()
      console.log('开发环境，传递IP参数:', ipParam)
    }

    // 调用LoginRedirect接口获取登录地址
    const response = await LoginRedirect(ipParam)

    if (response.success && response.data) {
      // 有登录地址，直接跳转
      loadingText.value = '正在跳转到UMC登录...'
      console.log('获取到UMC登录地址:', response.data)
      window.location.href = response.data
    } else {
      // 没有登录地址或接口失败，显示错误信息
      const errorMessage = response.message || '获取登录地址失败'
      message.error(errorMessage)
      console.error('LoginRedirect接口失败:', response)

      // 重置状态
      redirecting.value = false
      loadingText.value = '正在跳转到统一登录平台...'
      subText.value = '请稍候'
    }
  } catch (error) {
    // 接口调用异常
    console.error('调用LoginRedirect接口异常:', error)
    message.error('网络异常，请稍后重试')

    // 重置状态
    redirecting.value = false
    loadingText.value = '正在跳转到统一登录平台...'
    subText.value = '请稍候'
  }
}
</script>
<style lang="scss" scoped>
.loginMainBox {
  position: relative;
  width: 100%;
  min-width: 1180px;
  height: 100vh;
  background-color: #fff;
  background-image: url('../assets/image/login-bg.webp');
  background-size: cover;

  .loginBox {
    position: absolute;
    top: 0;
    right: 360px;
    bottom: 0;
    width: 480px;
    height: 490px;
    padding: 60px;
    margin: auto;
    background: rgb(255 255 255 / 90%);
    border-radius: 10px;
    box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%);

    .titleBox {
      display: flex;
      gap: 16px;
      align-items: center;
      margin-bottom: 40px;
      font-size: 18px;
      font-weight: 400;
      line-height: 30px;
      color: #1a1a1a;

      img {
        width: 60px;
        height: 60px;
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30px;

      .loading-icon {
        margin-bottom: 16px;

        .spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #409eff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .loading-text {
        text-align: center;

        .main-text {
          margin-bottom: 4px;
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }

        .sub-text {
          font-size: 14px;
          color: #666;
        }
      }
    }

    .umc-login {
      width: 100%;
      height: 44px;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
      background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
      border: none;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover:not(.loading) {
        background: linear-gradient(135deg, #36a3f7 0%, #409eff 100%);
        box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
        transform: translateY(-1px);
      }

      &.loading {
        opacity: 0.8;
      }
    }
  }

  .beian {
    position: absolute;
    right: 0;
    bottom: 30px;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    font-size: 12px;
    color: #999;

    .line {
      position: relative;
      top: 1px;
      width: 1px;
      height: 10px;
      margin: 0 12px;
      background: #999;
    }

    a {
      color: #999;
      text-decoration: none;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
