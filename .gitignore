# ================================
# 依赖包目录 (Dependencies)
# ================================
node_modules/
vendor/
.pnpm/
.yarn/
.npm/
sw.js
workbox-*.js

# ================================
# 构建输出目录 (Build Output)
# ================================
dist/
dist-ssr/
build/
out/
.output/
.nuxt/
.next/
coverage/

# ================================
# 日志文件 (Logs)
# ================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# ================================
# 临时文件和缓存 (Temp & Cache)
# ================================
*.tmp
*.temp
.cache/
.parcel-cache/
.vite/
.turbo/
*.local
.env.local
.env.*.local

# ================================
# 系统生成文件 (System Files)
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ================================
# 开发环境配置 (Development Config)
# ================================
.vscode/
!.vscode/extensions.json
!.vscode/settings.json
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# ================================
# IDE和编辑器文件 (IDE & Editor)
# ================================
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.vscode-test/

# ================================
# 敏感信息文件 (Sensitive Files)
# ================================

*.key
*.pem
*.p12
*.pfx
config/local.js
config/production.js
**/secrets.json
**/credentials.json

# ================================
# 文档文件 (Documentation)
# ================================
*.md
!README.md
!CHANGELOG.md
!CONTRIBUTING.md
!LICENSE.md
*.doc
*.docx
*.pdf
*.ppt
*.pptx
*.xls
*.xlsx
.txt
docs/
documentation/

# ================================
# Vue.js 特定文件 (Vue.js Specific)
# ================================
auto-imports.d.ts
components.d.ts
*.tsbuildinfo

# ================================
# 服务器配置文件 (Server Config)
# ================================


# ================================
# 测试覆盖率 (Test Coverage)
# ================================
coverage/
.nyc_output/
*.lcov

# ================================
# 打包和部署文件 (Package & Deploy)
# ================================
*.zip
*.tar.gz
*.rar
*.7z
deploy/
release/

# ================================
# 数据库文件 (Database)
# ================================
*.db
*.sqlite
*.sqlite3

# ================================
# 备份文件 (Backup Files)
# ================================
*.bak
*.backup
*.old
*~

# ================================
# 运行时文件 (Runtime)
# ================================
*.pid
*.seed
*.pid.lock

# ================================
# 其他临时文件 (Other Temp Files)
# ================================
.sass-cache/
.stylelintcache
.eslintcache
.prettiercache
