<template>
  <a-modal v-model:open="visible" title="选择文件" :footer="false" :width="500">
    <BaseFileUploadMode v-model:file-list="fileList" :before-upload="beforeUpload" @delete="handleDeleteFile" :disabled="disabled" />
  </a-modal>
</template>

<script setup lang="ts">
import BaseFileUploadMode from '@/components/BaseFileUploadMode/index.vue'
import { UploadCommonFile } from '@/servers/Common'
import { message } from 'ant-design-vue'

const emits = defineEmits(['change'])

const props = defineProps<{
  selectFileList: any[]
  disabled?: boolean
}>()

const fileList = ref<any[]>([])
const visible = defineModel<boolean>('visible')

// 上传文件
const beforeUpload = async (file: any) => {
  if (file.type !== 'application/pdf') {
    message.error('只允许上传 PDF 文件')
    return false
  }
  // 判断文件大小不能超过5M
  if (file.size > 5 * 1024 * 1024) {
    message.error('文件大小不能超过5M')
    return false
  }
  if (fileList.value.length >= 3) {
    message.error('最多上传3个文件')
    return false
  }
  file.status = 'uploading'
  file.fileId = new Date().getTime()
  fileList.value.push(file)
  const formData = new FormData()
  formData.append('files', file)
  try {
    const res = await UploadCommonFile('Product', formData)
    file.status = 'done'
    file.id = res.data[0].id
    file.fileId = res.data[0].file_id
    fileList.value = fileList.value.filter((i) => i.id !== file.id)
    fileList.value.push(file)
  } catch (error) {
    fileList.value = fileList.value.filter((i) => i.id !== file.id)
  } finally {
    emits('change', fileList.value)
  }
  return false
}

const handleDeleteFile = (file: any) => {
  fileList.value = fileList.value.filter((i) => i.id !== file.id)
  emits('change', fileList.value)
}

onMounted(() => {
  fileList.value = props.selectFileList
})
</script>

<style scoped lang="scss"></style>
