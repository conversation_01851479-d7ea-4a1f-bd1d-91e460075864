<template>
  <a-drawer :open="visible" :width="1200" title="编辑商品" @close="handleClose" :maskClosable="false" :keyboard="false" :destroyOnClose="true">
    <a-spin :spinning="loading">
      <BaseForm />
      <div class="drawer-title">证书信息</div>
      <div class="px-20 mb-24">
        <a-space class="mb-16">
          <div class="text-16px font-bold c-#000">商品所需许可证</div>
        </a-space>
        <div class="c-#999 mb-24">
          <div>温馨提示：</div>
          <div>1:涉及护肤品商品生产，如：洗护用品，膏霜乳液制品，唇膏、唇彩、眉笔、唇线笔、发蜡，牙膏类商品，清洁类商品等，需上传《化妆品生产许可证》；</div>
          <div>2. 涉及食品生产，如：粮食加工品、油脂及其制品，调味品，肉制品，乳制品，饮料方便食品，冷冻饮品，速冻食品等，需上传《食品生产许可证》；</div>
          <div>3. 涉及药品生产，如：化学药剂，中药制剂，生物制品，原材料药及药用辅料，特殊管理药品等，需上传《药品生产许可证》；</div>
          <div>4. 涉及工业品生产，如：建筑用钢筋，水泥，广播电视传输设备，电线电缆，危险化学品，化肥等，需上传《工业产品生产许可证》；</div>
          <div>5. 涉及医疗器械相关生产，如：基础耗材，护理用品，诊断监护类，治疗康复类，手术耗材类，医用卫生材料等，需上传《医疗器械生产许可证》；</div>
        </div>
        <vxe-table :data="[{}]" size="small" border class="mb-24">
          <vxe-column v-for="item in permitCloumns" :key="item.field" :field="item.title" :title="item.title">
            <template #default>
              <a-flex>
                <a-button type="link" class="!px-8" @click="handleShowPermitModal(item.field)">点击上传</a-button>
                <div>
                  <a-badge :count="getCount(item.field)" />
                </div>
              </a-flex>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div>
        <div class="drawer-title">商品图片/视频</div>
        <a-form layout="vertical">
          <a-form-item label="商品主图" :rules="{ required: true }">
            <div class="flex">
              <div>
                <BaseFileUpload
                  class="uploader main-image"
                  multiple
                  v-model:file-list="fileObj.shopFileList"
                  :before-upload="(file) => beforeUpload(file, 'shopFileList')"
                  @delete="(f) => handleDelete(f, 'shopFileList')"
                  accept="image/png, image/jpeg, image/bmp"
                  :max-count="6"
                >
                  <div v-if="fileObj.shopFileList.length < 6">
                    <plus-outlined />
                  </div>
                </BaseFileUpload>
              </div>
              <div class="c-#999">
                <div>1. ≥800×800像素，单图≤3MB，支持 JPEG/JPG/PNG/BMP 格式。</div>
                <div>2. 支持同时上传多张图片，最多不超过6张。</div>
                <div>3. 图片包含商品整体款式和商品细节。第一张图片将作为商品首张主图。</div>
              </div>
            </div>
          </a-form-item>
          <a-form-item label="商品SKU图" :rules="{ required: true }">
            <div class="flex">
              <div>
                <BaseFileUpload
                  class="uploader"
                  multiple
                  v-model:file-list="fileObj.skuFileList"
                  accept="image/png, image/jpeg, image/bmp"
                  :before-upload="(file) => beforeUpload(file, 'skuFileList')"
                  @delete="(f) => handleDelete(f, 'skuFileList')"
                  :max-count="6"
                >
                  <div v-if="fileObj.skuFileList.length < 6">
                    <plus-outlined />
                  </div>
                </BaseFileUpload>
              </div>
              <div class="c-#999">
                <div>1. 用于首页、列表页、活动 等特定场景的展示</div>
                <div>2. 比例 1:1，≥800×800像素，单图&lt; =3MB，支持 JPEG/JPG/PNG/BMP 格式，&lt;=6张。</div>
                <div>3. 背景纯白底，主体清晰，展示完整，不能有LOGO、文字。</div>
              </div>
            </div>
          </a-form-item>
          <a-form-item label="商品视频">
            <div class="flex">
              <div>
                <BaseFileUpload
                  class="uploader"
                  v-model:file-list="fileObj.videoFileList"
                  list-type="picture-card"
                  :before-upload="(file) => beforeUpload(file, 'videoFileList')"
                  @delete="(f) => handleDelete(f, 'videoFileList')"
                  accept="video/mp4"
                  :max-count="1"
                >
                  <div v-if="fileObj.videoFileList.length < 1">
                    <SvgIcon name="video" class="w-20px! h-20px!" />
                  </div>
                </BaseFileUpload>
              </div>
              <div class="c-#999">
                <div>1. 商品支持 MP4 格式，≤300MB。仅支持上传1个视频。</div>
                <div>2. 视频时长 15秒~60秒，分辨率 ≥720（推荐1080P）， 宽高比 1:1（正方形）或 16:9（横屏）。</div>
                <div>3. 建议上传视频，可帮助用户更好了解商品。</div>
              </div>
            </div>
          </a-form-item>
          <div class="drawer-title">商品详情</div>
          <a-radio-group v-model:value="language" button-style="solid" class="mb-8">
            <a-radio-button value="cn">中文</a-radio-button>
            <a-radio-button value="en">英文</a-radio-button>
          </a-radio-group>
          <a-form-item :rules="{ required: true }">
            <template #label>
              <a-space :size="16">
                <span>商品详情页</span>
                <div class="flex items-center c-#999">
                  <InfoCircleOutlined />
                  支持 JPEG/JPG/PNG/BMP 格式；单张≤5MB；宽度>=750px，建议 750px~1600px（高度自适应）；最多不超过15张。
                </div>
              </a-space>
            </template>
            <div class="c-#D33333">我们将按照提交时的图片顺序展示详情页图片，请严格按照详情页顺序上传或调整，以确保详情页正确展示。</div>
            <BaseFileUploadMode
              class="mt-16"
              v-model:file-list="fileObj.cnFileList"
              :before-upload="(file) => beforeUpload(file, 'cnFileList')"
              @delete="(f) => handleDelete(f, 'cnFileList')"
              @sortable="(f) => handleSortable(f, 'cnFileList')"
              v-show="language === 'cn'"
              :max-count="15"
            />
            <BaseFileUploadMode
              class="mt-16"
              v-model:file-list="fileObj.enFileList"
              :before-upload="(file) => beforeUpload(file, 'enFileList')"
              @delete="(f) => handleDelete(f, 'enFileList')"
              @sortable="(f) => handleSortable(f, 'enFileList')"
              v-show="language === 'en'"
              :max-count="15"
            />
          </a-form-item>
        </a-form>
      </div>
      <ProductFileSelectModal v-if="fileSelectModalVisible" v-model:visible="fileSelectModalVisible" :select-file-list="showFileList" />
      <ProductPermitModal v-if="permitModalVisible" v-model:visible="permitModalVisible" :selected-permit-ids="selectedPermitIds" @select="handleSelectPermit" />
    </a-spin>
    <template #footer>
      <a-space>
        <a-button @click="handleClose">关闭</a-button>
        <a-button type="primary" @click="handleSave">修改</a-button>
        <a-button @click="handleSaveDraft">保存草稿</a-button>
      </a-space>
    </template>
    <BrandAddDrawer ref="brandRef" />
  </a-drawer>
</template>

<script setup lang="ts">
import { GetProduct, GetCategoryOption, UpdateProduct } from '@/servers/ProductLibrary'
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { useTemplate } from '@/hook/useTemplate'
import { Tooltip, message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { QuestionCircleOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { getCommonOption, createId, cloneDeep } from '@/utils'
import { ViewByFileIdCommon, UploadCommonFile, UploadVideoFile } from '@/servers/Common'
import { GetFileList } from '@/servers/companyInfo'
// import Decimal from 'decimal.js'
import BrandAddDrawer from '@/views/pageComponents/productManagement/brand/components/EditDrawer.vue'
import SvgIcon from '@/components/SvgIcon/index.vue'
import ProductAddStepSecondTable from './ProductAddStepSecondTable.vue'
import ProductFileSelectModal from './ProductFileSelectModal.vue'
import ProductPermitModal from './ProductPermitModal.vue'

const props = defineProps<{
  id: number
  isEdit: boolean
  isNew?: boolean
}>()

const brandRef = useTemplateRef<any>('brandRef')
const { proxy }: any = getCurrentInstance()

const rules = ref<Record<string, Rule[]>>({
  product_name: [
    {
      required: true,
      validator(_rule, value) {
        if (!value?.length) {
          return Promise.reject('请输入商品名称')
        }
        const reg = /^[\u4e00-\u9fa5a-zA-Z0-9 \-_/()·]*$/
        if (value && !reg.test(value)) {
          return Promise.reject('商品名称格式有误')
        }
        return Promise.resolve()
      },
    },
  ],
  product_name_en: [
    {
      required: true,
      validator(_rule, value) {
        if (!value?.length) {
          return Promise.reject('请输入商品名称')
        }
        const reg = /^[a-zA-Z0-9 \-_/()·]*$/
        if (value && !reg.test(value)) {
          return Promise.reject('商品名称格式有误')
        }
        return Promise.resolve()
      },
    },
  ],
  supplier_product_number: [{ required: true, message: '请输入供应商商品编码' }],
  product_type: [{ required: true, message: '请选择商品类型' }],
  brand_id: [{ required: true, message: '请选择商品品牌' }],
  category_id: [{ required: true, message: '请选择商品类目' }],
  delivery_time: [{ required: true, message: '请输入发货时效' }],
  delivery_location: [{ required: true, message: '请输入发货地' }],
  selling_points: [{ required: true, message: '请输入商品卖点' }],
  declared_purchase_price: [{ required: true, message: '请输入不含税单价' }],
  declared_purchase_tax_rate: [{ required: true, message: '请输入税率' }],
  declared_purchase_tax_price: [{ required: true, message: '清输入采购单价(含税)' }],
})

const copyRule = ref({})

// 抽屉是否显示
const visible = defineModel<boolean>('visible', { required: true })
// 加载状态
const loading = ref(false)
// 商品信息
const productInfo = ref<any>({})

const copyInfo = ref<any>({})
// 模板配置
const initTemplate = ref<any>([])
// 选择商品所需许可证的field
const selectField = ref<number>()

const showFileList = ref<any[]>([])
// 商品所需许可证弹窗是否显示
const permitModalVisible = ref(false)
// 选择商品所需许可证的id
const selectedPermitIds = ref<number[]>([])

// 商品所需许可证
const permitCloumns = ref([
  {
    field: 1,
    title: '化妆品生产许可证',
  },
  {
    field: 4,
    title: '食品生产许可证',
  },
  {
    field: 5,
    title: '药品生产许可证',
  },
  {
    field: 6,
    title: '医疗器械生产许可证',
  },
  {
    field: 8,
    title: '工业产品生产许可证',
  },
])
// 商品主图
const fileObj = ref<any>({
  shopFileList: [],
  skuFileList: [],
  videoFileList: [],
  cnFileList: [],
  enFileList: [],
})

const language = ref('cn')
const fileSelectModalVisible = ref(false)
// 行内提示
const rowTip = (label: string, title: VNode[]) => {
  return h('div', { class: 'flex items-center' }, [
    h('div', {}, label),
    h(
      Tooltip,
      { placement: 'topRight' },
      {
        default: () => h(QuestionCircleOutlined, { class: 'c-#666 ml-8px mr-17px' }),
        title: () => title,
      },
    ),
  ])
}

const handleSortable = (file: any, type: string) => {
  if (type === 'cnFileList') {
    productInfo.value.product_detail_images_ids = file.map((i: any) => i.id)
  } else if (type === 'enFileList') {
    productInfo.value.product_detail_en_images_ids = file.map((i: any) => i.id)
  }
}

// const setSupplyPrice = () => {
//   basicDrawerForm.value.forEach((item) => {
//     if (item.type === 'input-number' && item.key === 'declared_purchase_price') {
//       item.hidden = !productInfo.value.price_type
//       item.props!.disabled = productInfo.value.price_type === 2
//       item.props!.placeholder = productInfo.value.price_type === 2 ? '不含税单价，自动计算' : '请输入不含税单价'
//       item.props!.showCount = productInfo.value.price_type === 1
//     } else if (item.type === 'select' && item.key === 'declared_purchase_declared_purchase_tax_rate') {
//       item.hidden = !productInfo.value.price_type
//     } else if (item.type === 'input-number' && item.key === 'declared_purchase_tax_price') {
//       item.hidden = !productInfo.value.price_type
//       item.props!.disabled = productInfo.value.price_type === 1
//       item.props!.placeholder = productInfo.value.price_type === 1 ? '含税单价，自动计算' : '请输入含税单价'
//       item.props!.showCount = productInfo.value.price_type === 2
//     }
//   })
// }

// 基础信息表单配置
const basicDrawerForm = ref<BaseFormItem[]>([
  {
    type: 'input',
    label: '商品名称(中)',
    key: 'product_name',
    span: 6,
    props: {
      maxlength: 50,
      placeholder: '请输入中文商品名称',
    },
  },
  {
    type: 'input',
    label: '商品名称(英)',
    key: 'product_name_en',
    span: 6,
    props: {
      maxlength: 100,
      placeholder: '请输入英文商品名称',
    },
  },
  {
    type: 'input',
    label: () => rowTip('供应商商品编码', [h('div', null, '用于您内部商品管理，与您的系统保持一致。')]),
    key: 'supplier_product_number',
    span: 6,
    props: {
      maxlength: 50,
      placeholder: '供应商内部商品编码',
      disabled: !props.isNew,
    },
  },
  {
    type: 'select',
    label: '商品类型',
    span: 6,
    key: 'product_type',
    props: {
      disabled: true,
      options: [
        { label: '新品', value: 1 },
        { label: '定制品', value: 2 },
        { label: '物料', value: 3 },
      ],
    },
  },
  {
    type: 'select',
    label: () => rowTip('商品品牌', [h('div', null, '商品已通过平台选品，不可修改品牌。若需要修改，请重新发布商品')]),
    key: 'brand_id',
    span: 6,
    props: {
      placeholder: '商品品牌',
      options: [],
      allowClear: true,
    },
    slots: {
      notFoundContent: () => h('div', { class: 'flex items-center' }, [h('span', {}, '无品牌数据，请先'), h('span', { class: ' c-primary cursor-pointer ', onClick: showBrandAdd }, '添加品牌')]),
    },
  },
  {
    type: 'cascader',
    label: () => rowTip('商品类目', [h('div', null, '商品已通过平台选品，不可修改类目。若需要修改，请重新发布商品')]),
    span: 6,
    key: 'category_id',
    props: {
      options: [],
      fieldNames: { label: 'name', value: 'id' },
      allowClear: true,
      onChange: async () => {
        loading.value = true
        copyList(copyBasicDrawerForm, basicDrawerForm)
        if (productInfo.value.category_id) {
          const category_id = cloneDeep(productInfo.value.category_id)
          const ls = category_id[category_id.length - 1].split('_')
          productInfo.value.category_template_id = Number(ls[1])
          await getNewTempalte()
        }
        loading.value = false
      },
    },
  },
  {
    type: 'input-number',
    label: () => rowTip('发货时效', [h('div', null, '请填写承诺的发货时间，单位“小时”（如：24），确保准确以免影响订单履约')]),
    span: 6,
    key: 'delivery_time',
    props: {
      maxlength: 9,
      placeholder: '从下单到发货的时间',
      showCount: true,
      onChange: () => {
        let val = productInfo.value.delivery_time
        // 只允许数字
        val = String(val).replace(/[^0-9]/g, '')
        productInfo.value.delivery_time = val
      },
      addonAfter: 'h(小时)',
    },
  },
  {
    type: 'input',
    label: () => rowTip('发货地', [h('div', null, '填写实际发货仓库所在省市（如：广东省深圳市），若多仓发货请用分号“;”分隔 （如：广东省深圳市；浙江省杭州市）')]),
    span: 6,
    key: 'delivery_location',
    props: {
      placeholder: '请输入发货地',
      maxlength: 50,
    },
  },
  {
    type: 'textarea',
    label: () => rowTip('商品卖点', [h('div', null, '请用3-5条短句描述核心优势，用分号“;”分隔。示例：新疆长绒棉|A类婴幼儿标准|无荧光剂')]),
    span: 6,
    key: 'selling_points',
    props: {
      class: 'w-full',
      placeholder: '请提炼3-5个商品核心卖点，吸引买家快速决策',
      maxlength: 50,
    },
  },
  {
    type: 'title',
    label: '申报采购价',
  },
  // {
  //   label: '选择填写价格类型',
  //   type: 'select',
  //   key: 'price_type',
  //   span: 6,
  //   props: {
  //     options: [
  //       { label: '含税单价', value: 2 },
  //       { label: '不含税单价', value: 1 },
  //     ],
  //     onChange: () => {
  //       if (productInfo.value.price_type === 2) {
  //         baseFormRef.value?.clearValidate('declared_purchase_price')
  //       } else {
  //         baseFormRef.value?.clearValidate('declared_purchase_tax_price')
  //       }
  //       setSupplyPrice()
  //     },
  //   },
  // },
  // {
  //   type: 'input-number',
  //   label: () => rowTip('不含税单价', [h('div', null, '商品不含税费的价格。税率为0时，不含税单价=含税单价。')]),
  //   key: 'declared_purchase_price',
  //   hidden: false,
  //   span: 6,
  //   props: {
  //     placeholder: '请输入不含税单价',
  //     showCount: true,
  //     maxlength: 10,
  //     onChange: () => {
  //       if (productInfo.value.price_type !== 1) return
  //       let val = productInfo.value.declared_purchase_price
  //       // 只允许数字和小数点
  //       val = productInfo.value.declared_purchase_price.replace(/[^\d.]/g, '')
  //       // 只允许出现一个小数点
  //       val = val.replace(/^(\d+)\.(\d*)\./, '$1.$2')
  //       // 小数点不能在首位
  //       if (val.startsWith('.')) val = ''
  //       // 保留两位小数
  //       val = val.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2')
  //       productInfo.value.declared_purchase_price = val
  //       productInfo.value.declared_purchase_tax_price = new Decimal(Number(val) || 0)
  //         .mul(new Decimal(1 + Number(productInfo.value.declared_purchase_tax_rate || 0) / 100))
  //         .toDecimalPlaces(2)
  //         .toNumber()
  //     },
  //     addonAfter: 'CNY',
  //   },
  // },
  // {
  //   type: 'select',
  //   label: () => rowTip('税率', [h('div', null, '支持填写 0，零税率则 不含税单价=含税单价。')]),
  //   key: 'declared_purchase_tax_rate',
  //   hidden: false,
  //   span: 6,
  //   props: {
  //     options: [
  //       { label: '0%', value: 0 },
  //       { label: '3%', value: 3 },
  //       { label: '6%', value: 6 },
  //       { label: '9%', value: 9 },
  //       { label: '13%', value: 13 },
  //       { label: '16%', value: 16 },
  //       { label: '17%', value: 17 },
  //     ],
  //     placeholder: '零税率的增值税',
  //     onChange: () => {
  //       let val = productInfo.value.declared_purchase_tax_rate.toString()
  //       // 只允许数字和小数点
  //       val = val.replace(/[^\d.]/g, '')
  //       // 只允许出现一个小数点
  //       val = val.replace(/\.(?=.*\.)/g, '')
  //       // 如果以小数点开头，在前面加0
  //       if (val.startsWith('.')) {
  //         val = `0${val}`
  //       }
  //       // 保留两位小数
  //       val = val.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2')
  //       // 限制范围0-1000
  //       if (Number(val) > 1000) val = '1000'
  //       if (Number(val) < 0) val = '0'
  //       productInfo.value.declared_purchase_tax_rate = val
  //       if (productInfo.value.price_type === 1) {
  //         // 重新计算含税单价
  //         productInfo.value.declared_purchase_tax_price = new Decimal(Number(productInfo.value.declared_purchase_price) || 0)
  //           .mul(new Decimal(1 + (Number(productInfo.value.declared_purchase_tax_rate) || 0) / 100))
  //           .toDecimalPlaces(2)
  //           .toNumber()
  //       } else {
  //         productInfo.value.declared_purchase_price = new Decimal(Number(productInfo.value.declared_purchase_tax_price) || 0)
  //           .div(new Decimal(1 + (Number(productInfo.value.declared_purchase_tax_rate) || 0) / 100))
  //           .toDecimalPlaces(2)
  //           .toNumber()
  //       }
  //     },
  //   },
  // },
  {
    type: 'a-input-number',
    label: '采购单价(含税)',
    key: 'declared_purchase_tax_price',
    span: 6,
    props: {
      addonAfter: 'CNY',
      controls: false,
      max: *********.99,
      precision: 2,
    },
  },
])

// 基础表单备份
const copyBasicDrawerForm = ref<BaseFormItem[]>([])

// 获取商品信息
const getInfo = async (id: number) => {
  const res = await GetProduct({ id, isEdit: props.isEdit })
  await getPermitFile(res.data.productCertificates)

  if (res.data.brand_id) {
    res.data.brand_id = String(res.data.brand_id)
  }
  if (res.data.category_id) {
    res.data.category_id += `_${res.data.category_template_id}`
  }
  res.data.productAttrValues.forEach((item: any) => {
    res.data[item.attr_id] = item.value
  })
  await Promise.all([
    getFileObj(res.data.main_images_ids, 'shopFileList', res.data.main_images),
    getFileObj(res.data.sku_images_ids, 'skuFileList', res.data.sku_images),
    getFileObj(res.data.video_file_id ? [res.data.video_file_id] : [], 'videoFileList'),
    getFileObj(res.data.product_detail_images_ids, 'cnFileList', res.data.product_detail_images),
    getFileObj(res.data.product_detail_en_images_ids, 'enFileList', res.data.product_detail_en_images),
  ])

  // res.data.declared_purchase_tax_price = new Decimal(Number(res.data.declared_purchase_price) || 0)
  //   .mul(new Decimal(1 + (Number(res.data.declared_purchase_tax_rate) || 0) / 100))
  //   .toDecimalPlaces(2)
  //   .toNumber()
  copyInfo.value = cloneDeep(res.data)
  productInfo.value = res.data

  basicDrawerForm.value.forEach((i) => {
    if ((i.type === 'select' || i.type === 'cascader') && ['brand_id', 'category_id'].includes(i.key)) {
      i.props!.disabled = productInfo.value.selection_status === 40
    }
  })
  await getBrandOption()
}

// 获取许可证
const getPermitFile = async (permitList: any[]) => {
  if (permitList.length) {
    await Promise.all(
      permitList.map(async (cert: any, index: number) => {
        const fileIdArr = cert.file_ids.split(',').filter(Boolean)
        const files = await Promise.all(
          fileIdArr.map(async (fid: string) => {
            const res = await ViewByFileIdCommon(fid)
            return {
              id: fid,
              status: 'done',
              url: URL.createObjectURL(res.data),
              file_id: fid,
            }
          }),
        )
        const fileRes = await GetFileList({ file_id_list: fileIdArr })
        files.forEach((file: any, idx: number) => {
          const name = fileRes.data[idx].original_name
          file.type = ['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(name.split('.').pop()) ? 'image' : name.split('.').pop()
          file.name = name
        })
        permitList[index].fileList = files
      }),
    )
  }
}

const getFileObj = async (ids: number[], key: string, list?: any) => {
  if (!ids?.length) return
  if (list) {
    // 用 map 返回 Promise 数组，Promise.all 保证顺序
    const resArr = await Promise.all(
      list.map(async (element) => {
        const res = await ViewByFileIdCommon(element.file_id)
        return {
          id: element.file_id,
          fileId: element.file_id,
          status: 'done',
          url: URL.createObjectURL(res.data),
          name: element.file_name,
          type: ['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(element.file_name.split('.').pop()) ? 'image' : element.file_name.split('.').pop(),
        }
      }),
    )
    fileObj.value[key].push(...resArr)
  } else {
    const fileDataList = await Promise.all(ids.map((id) => ViewByFileIdCommon(id)))
    const fileAttrRes = await GetFileList({ file_id_list: ids })
    // 用 Map 以 id 为 key 存储 fileAttrRes.data
    const attrMap = new Map(fileAttrRes.data.map((i) => [i.id, i]))
    fileObj.value[key] = await Promise.all(
      ids.map(async (id, idx) => {
        const i: any = attrMap.get(id)
        const url = URL.createObjectURL(fileDataList[idx].data)
        const cover = await getVideoCoverByStream(fileDataList[idx].data)
        return {
          id: i.id,
          fileId: i.id,
          status: 'done',
          url: cover,
          name: i.original_name,
          videoUrl: url,
          type: ['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(i.original_name.split('.').pop()) ? 'image' : i.original_name.split('.').pop(),
        }
      }),
    )
  }
}

// 视频文件流获取封面
const getVideoCoverByStream = (file: Blob): Promise<string> => {
  return new Promise((resolve) => {
    const url = URL.createObjectURL(file)
    const video = document.createElement('video')
    video.src = url
    video.crossOrigin = 'anonymous'
    video.currentTime = 0.1 // 避免黑帧
    video.addEventListener(
      'canplay',
      () => {
        const canvas = document.createElement('canvas')
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext('2d')
        ctx?.drawImage(video, 0, 0, canvas.width, canvas.height)
        resolve(canvas.toDataURL('image/png'))
        URL.revokeObjectURL(url)
      },
      { once: true },
    )
  })
}

const categoryOption = ref<any[]>([])

// 获取商品类目选项
const getCategoryOption = async () => {
  const res: any = await GetCategoryOption()
  const recursiveModifyId = (data: any[]) => {
    data.forEach((item) => {
      if (item.id && item.category_template_id) {
        item.id = `${item.id}_${item.category_template_id}`
      }
      if (item.children?.length) {
        recursiveModifyId(item.children)
      }
    })
  }
  recursiveModifyId(res.data)
  categoryOption.value = res.data
  setOption('category_id', res.data)
}

// 获取商品品牌选项
const getBrandOption = async () => {
  const [brandOption] = await getCommonOption([15])
  const item = brandOption.find((i) => i.value === productInfo.value.brand_id)
  if (!item) {
    const newBrandOption = cloneDeep(brandOption)
    newBrandOption.unshift({
      label: productInfo.value.brand_name,
      value: productInfo.value.brand_id,
    })
    setOption('brand_id', newBrandOption)
  } else {
    setOption('brand_id', brandOption)
  }
}

const handleShowPermitModal = (field: number) => {
  selectField.value = field
  permitModalVisible.value = true
  const selectPermit = productInfo.value.productCertificates.filter((i) => i.certificate_license_type === field)
  selectedPermitIds.value = selectPermit.map((i) => i.certificate_id)
}

// 递归查找树形数据中的label
const findIdsByValue = (treeData: any[], targetValue: any, parentLabel = ''): string => {
  for (const item of treeData) {
    const currentLabel = parentLabel ? `${parentLabel},${item.id}` : item.id
    if (item.id === targetValue) return currentLabel
    if (item.children?.length) {
      const result = findIdsByValue(item.children, targetValue, currentLabel)
      if (result) return result
    }
  }
  return ''
}

const setCategoryId = () => {
  const res = findIdsByValue(categoryOption.value, productInfo.value.category_id)
  productInfo.value.category_id = res.split(',')
}

// 打开抽屉
const handleOpen = async (id: number) => {
  try {
    visible.value = true
    loading.value = true
    await Promise.all([getInfo(id), getCategoryOption()])
    if (productInfo.value.category_template_id) {
      await getNewTempalte(true)
    }
    loading.value = false
  } finally {
    loading.value = false
  }
}

// 获取新的模板
const getNewTempalte = async (init = false) => {
  const [transformTemplateConfig, templateConfig, editRules] = await getTemplate(productInfo.value.category_template_id, productInfo)
  rules.value = { ...copyRule.value, ...editRules }
  if (init) {
    setCategoryId()
  }
  initTemplate.value = templateConfig || []
  templateConfig.forEach(async (item: any) => {
    if (item.children?.length) {
      item.children.forEach((i: any) => {
        if ((i.type_json.options?.length || i.type_json.option_list?.length) && productInfo.value[i.id]) {
          productInfo.value[i.id] = Number(productInfo.value[i.id])
        }
        if (i.type === 7 && i.display_type === 2) {
          productInfo.value[i.id] = copyInfo.value[i.id] ? copyInfo.value[i.id].split(',').map((i) => Number(i)) : []
        } else if (i.type === 12 && i.type_json.option_type === 2) {
          productInfo.value[i.id] = copyInfo.value[i.id] ? copyInfo.value[i.id].split(',').map((i) => Number(i)) : []
        }
      })
    }
    if (item.type === 100) {
      const rows: any[] = []
      const ids: number[] = []
      item.children.forEach((i) => {
        const fileId = productInfo.value[i.id]
        if (!fileId) return
        ids.push(...fileId.split(',').map(Number))
        rows.push({
          id: i.id,
          fileId: fileId.split(',').map(Number),
        })
      })
      const fileAttrRes = await GetFileList({ file_id_list: ids.filter((i) => i) })
      rows.forEach(async (i) => {
        const res = await Promise.all(
          i.fileId.map(async (id) => {
            const res = await ViewByFileIdCommon(id)
            return { id, url: URL.createObjectURL(res.data), name: fileAttrRes.data.find((j) => j.id === id)?.original_name }
          }),
        )
        productInfo.value[i.id] = res.map((i) => ({
          id: i.id,
          url: i.url,
          name: i.name,
          status: 'done',
          type: ['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(i.name.split('.').pop()) ? 'image' : i.name.split('.').pop(),
        }))
      })
    }
  })
  basicDrawerForm.value.push(...transformTemplateConfig)
}

// 获取附件的数量
const getCount = (field: number) => {
  const list = productInfo.value?.productCertificates?.filter((i) => i.certificate_license_type === field)
  return list?.length || 0
}

// 关闭抽屉
const handleClose = () => {
  proxy.$confirm.show({
    title: '取消',
    width: 450,
    content: '当前商品上新信息未保存，是否确认取消配置？',
    onConfirm: () => {
      visible.value = false
    },
  })
}

// 保存
const handleSave = async () => {
  await baseFormRef.value?.validate()

  const form = cloneDeep(productInfo.value)
  const ls = form.category_id[form.category_id.length - 1].split('_')

  form.category_id = Number(ls[0])
  form.category_template_id = Number(ls[1])
  form.brand_id = Number(form.brand_id)
  form.productAttrValues = transformSubmitForm(form)
  delete form.brand_name
  // delete form.declared_purchase_tax_price

  form.publish_status = 1
  await UpdateProduct(form)
  message.success('成功修改')
  visible.value = false
}

// 保存草稿
const handleSaveDraft = async () => {
  await baseFormRef.value?.validate(['product_name', 'supplier_product_number'])
  const form = cloneDeep(productInfo.value)
  if (form.category_id) {
    const ls = form.category_id[form.category_id.length - 1].split('_')
    form.category_id = Number(ls[0])
    form.category_template_id = Number(ls[1])
  } else {
    form.category_id = null
    form.category_template_id = null
  }
  form.brand_id = Number(form.brand_id)
  form.productAttrValues = transformSubmitForm(form)
  delete form.brand_name

  form.publish_status = 0
  await UpdateProduct(form)
  message.success('保存草稿成功')
  visible.value = false
}

// 转换提交表单
const transformSubmitForm = (form: any) => {
  const arr: any[] = []
  console.log('initTem', initTemplate.value)

  initTemplate?.value?.forEach((i) => {
    if (i.children) {
      i.children.forEach((j: any) => {
        if (form[j.id]) {
          const item: any = {
            id: 0,
            attr_id: j.id,
            attr_group_id: j.attr_group_id,
            attr_name: j.name,
          }
          if (i.type === 100) {
            item.value = form[j.id]?.map((i: any) => i.id)?.join(',')
          } else if (j.type === 7 && j.display_type === 2) {
            item.value = form[j.id].toString()
          } else if (j.type === 12 && j.type_json.option_type === 2) {
            item.value = form[j.id].toString()
          } else {
            item.value = form[j.id]
          }
          arr.push(item)
          delete form[j.id]
        }
      })
    }
  })
  return arr
}

// 自定义插槽回调
const slotCallback = (item: any): BaseFormItem => ({
  type: 'slot',
  label: '',
  key: item.name,
  slots: () => h(ProductAddStepSecondTable, { item, form: productInfo.value }),
})

// 选择商品所需许可证
const handleSelectPermit = (selectRecors: any[]) => {
  const newSelectRecors = selectRecors.map((i) => ({
    id: 0,
    certificate_id: i.id,
    certificate_license_type: selectField.value,
    file_ids: i.file_ids,
  }))
  productInfo.value.productCertificates = productInfo.value.productCertificates.filter((i) => i.certificate_license_type !== selectField.value)
  productInfo.value.productCertificates.push(...newSelectRecors)
}

// 上传文件
const beforeUpload = async (file: any, type: string) => {
  file.status = 'uploading'
  file.fileId = createId()

  if (['shopFileList', 'skuFileList'].includes(type)) {
    if (file.size > 3 * 1024 * 1024) {
      message.error('图片大小不能超过3M')
      return false
    }
    if (type === 'shopFileList') {
      if (fileObj.value.shopFileList.length >= 6) {
        message.error('最多上传6张图片')
        return false
      }
    } else if (type === 'skuFileList') {
      if (fileObj.value.skuFileList.length >= 6) {
        message.error('最多上传6张图片')
        return false
      }
    }
    if (!['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(file.name.split('.').pop()!)) {
      message.error('请上传png、jpg、jpeg、bmp格式图片')
      return false
    }
  }

  if (type === 'videoFileList') {
    if (file.size > 300 * 1024 * 1024) {
      message.error('视频大小不能超过300M')
      return false
    }
    if (file.type.indexOf('mp4') === -1) {
      message.error('请上传mp4格式视频')
      return false
    }
  }
  if (type === 'cnFileList' || type === 'enFileList') {
    if (file.size > 5 * 1024 * 1024) {
      message.error('图片大小不能超过5M')
      return false
    }
    if (type === 'cnFileList') {
      if (fileObj.value.cnFileList.length >= 15) {
        message.error('最多上传15张图片')
        return false
      }
      if (!['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(file.name.split('.').pop()!)) {
        message.error('请上传png、jpg、jpeg、bmp格式图片')
        return false
      }
    } else if (type === 'enFileList') {
      if (fileObj.value.enFileList.length >= 15) {
        message.error('最多上传15张图片')
        return false
      }
      if (!['png', 'jpg', 'jpeg', 'bmp', 'PNG', 'JPG', 'JPEG', 'BMP'].includes(file.name.split('.').pop()!)) {
        message.error('请上传png、jpg、jpeg、bmp格式图片')
        return false
      }
    }
  }

  fileObj.value[type].push(file)
  const formData = new FormData()
  formData.append('files', file)
  try {
    const fn = type === 'videoFileList' ? UploadVideoFile : UploadCommonFile
    const res = await fn('ProductDetail', formData)
    file.status = 'done'
    file.url = URL.createObjectURL(file)
    file.id = res.data[0].id
    file.fileId = res.data[0].id
    fileObj.value[type] = fileObj.value[type].filter((i: any) => i.fileId !== file.fileId)
    fileObj.value[type].push(file)
    switch (type) {
      case 'shopFileList':
        productInfo.value.main_images_ids = fileObj.value.shopFileList.map((i: any) => i.id).slice(0, 6)
        break
      case 'skuFileList':
        productInfo.value.sku_images_ids = fileObj.value.skuFileList.map((i: any) => i.id).slice(0, 6)
        break
      case 'videoFileList':
        file.url = await getVideoCoverByStream(file)
        file.videoUrl = URL.createObjectURL(file)
        productInfo.value.video_file_id = res.data[0]!.id
        break
      case 'cnFileList':
        productInfo.value.product_detail_images_ids = fileObj.value.cnFileList.map((i: any) => i.id).slice(0, 15)
        break
      case 'enFileList':
        productInfo.value.product_detail_en_images_ids = fileObj.value.enFileList.map((i: any) => i.id).slice(0, 15)
        break
      default:
        break
    }
  } catch (error) {
    fileObj.value[type] = fileObj.value[type].filter((i: any) => i.fileId !== file.fileId)
  }

  return false
}

// 删除文件
const handleDelete = (file: any, type: string) => {
  fileObj.value[type].splice(fileObj.value[type].indexOf(file), 1)
  switch (type) {
    case 'shopFileList':
      productInfo.value.main_images_ids = productInfo.value.main_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.shopFileList = fileObj.value.shopFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'skuFileList':
      productInfo.value.sku_images_ids = productInfo.value.sku_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.skuFileList = fileObj.value.skuFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'videoFileList':
      productInfo.value.video_file_id = null
      fileObj.value.videoFileList = fileObj.value.videoFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'cnFileList':
      productInfo.value.product_detail_images_ids = productInfo.value.product_detail_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.cnFileList = fileObj.value.cnFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    case 'enFileList':
      productInfo.value.product_detail_en_images_ids = productInfo.value.product_detail_en_images_ids.filter((i: any) => i !== file.id)
      fileObj.value.enFileList = fileObj.value.enFileList.filter((i: any) => i.fileId !== file.fileId)
      break
    default:
      break
  }
}

// 添加品牌
const showBrandAdd = () => {
  brandRef.value.open('add')
}

const [BaseForm, baseFormRef, setOption] = useBaseForm({
  modelValue: productInfo,
  formConfig: basicDrawerForm,
  rules,
})

// 获取模板
const { getTemplate } = useTemplate(slotCallback, baseFormRef)

// 拷贝数组
const copyList = (startList: Ref<BaseFormItem[]>, needCopyList: Ref<BaseFormItem[]>) => {
  const list: BaseFormItem[] = []
  startList.value.forEach((i) => {
    list.push(i)
  })
  needCopyList.value = list
}

const copyObj = (obj, copyObj) => {
  for (const key in obj) {
    copyObj[key] = obj[key]
  }
}

onMounted(() => {
  copyList(basicDrawerForm, copyBasicDrawerForm)
  copyObj(rules.value, copyRule.value)
  handleOpen(props.id)
})
</script>

<style scoped lang="scss">
:deep(.base-file-upload) {
  .ant-upload.ant-upload-select {
    width: 58px !important;
    height: 58px !important;
  }

  .ant-upload-list-item-container {
    width: 58px !important;
    height: 58px !important;
    overflow: hidden !important;
  }

  .ant-upload-list-item {
    padding: 0 !important;

    &::before {
      width: 58px !important;
      height: 58px !important;
    }
  }
}

:deep(.main-image) {
  .ant-upload-list {
    .ant-upload-list-item-container {
      &:first-child {
        position: relative;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          width: 100%;
          height: 20px;
          font-size: 10px;
          line-height: 18px;
          color: #fff;
          text-align: center;
          content: '首张主图';
          background-color: #73a2f3;
        }
      }
    }
  }
}

:deep(.vxe-table--body-wrapper) {
  min-height: 0 !important;
}
</style>
