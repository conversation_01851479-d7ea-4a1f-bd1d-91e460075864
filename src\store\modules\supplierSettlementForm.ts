import { defineStore } from 'pinia'
import { Modal } from 'ant-design-vue'

export const useSupplierSettleFormStore = defineStore('supplierSettlementForm', {
  state: () => ({
    originForm: {},
    form: {},
    isChanged: false,
  }),
  actions: {
    setOriginForm(val: any) {
      this.originForm = val
      this.isChanged = JSON.stringify(this.originForm) != JSON.stringify(this.form)
    },
    setForm(val: any) {
      this.form = val
      this.isChanged = JSON.stringify(this.originForm) != JSON.stringify(this.form)
    },
    setFormChange(val: boolean) {
      this.isChanged = val
    },
    async checkFormChangedAndConfirm(): Promise<boolean> {
      if (window.location.pathname !== '/supplierSettlement' || !this.isChanged) {
        return true
      }
      return new Promise((resolve, reject) => {
        Modal.confirm({
          title: '取消',
          content: '当前供应商入驻信息未保存，是否确认取消配置？',
          onOk: () => {
            resolve(true)
          },
          onCancel: () => {
            reject(false)
          },
        })
      })
    },
  },
})
