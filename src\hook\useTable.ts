type Fn = (params: any) => Promise<any>

interface TableProps {
  /** 初始查询 */
  initSearch?: boolean
  formArr?: Ref<any[]>
}

/**
 * 表格hook
 * @param fetchListFn 获取列表的函数
 * @returns 总条数、查询参数、获取列表函数
 */
export const useTable = (queryFn: Fn, props: TableProps) => {
  const { initSearch = true } = props
  // 查询参数
  const queryParams = ref({
    page: 1,
    pageSize: 20,
  })
  // 加载状态
  const loading = ref(false)
  // 总条数
  const total = ref(0)
  // 数据
  const list = ref<any[]>([])

  // 获取列表
  const getList = async () => {
    try {
      loading.value = true
      const params = getQueryParams()
      const res = await queryFn(params)
      list.value = res.data?.data || res.data?.list || []
      total.value = res.data.total
    } catch (e) {
      total.value = 0
      list.value = []
    } finally {
      loading.value = false
    }
  }

  // 获取查询参数
  const getQueryParams = () => {
    const params: any = {
      page: queryParams.value.page,
      pageSize: queryParams.value.pageSize,
    }
    props.formArr?.value.forEach((i) => {})
  }

  // 初始化
  onMounted(() => {
    initSearch && getList()
  })

  return {
    total,
    queryParams,
    list,
  }
}
