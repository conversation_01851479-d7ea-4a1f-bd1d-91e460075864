import axios from 'axios'
// import md5 from 'js-md5';
import router from '@/router'
import { message } from 'ant-design-vue'
import { beforLogout } from '@/utils'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'

// 创建axios实例 - 旧接口使用 /api 前缀
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // 锐旭 10.1.19.82:90
  // 胜祖 10.1.19.91:16760
  baseURL: import.meta.env.VITE_APP_ENV === 'development' ? '/api' : (window as any).serverConfig.VITE_APP_BASE_API,
  withCredentials: true,
  // 超时
  timeout: 60000,
})

// 创建axios实例 - 新接口使用 /XY 前缀
const serviceXY = axios.create({
  baseURL: import.meta.env.VITE_APP_ENV === 'development' ? '/XY' : `${(window as any).serverConfig.VITE_APP_BASE_API}/XY`,
  withCredentials: true,
  timeout: 60000,
})

class HttpRequest {
  // 旧接口请求方法（使用 /api 前缀）
  baseOptions = (params?: any, method?: string, header?: any, isShowErr: boolean = true): Promise<any> => {
    return this.makeRequest(service, params, method, header, isShowErr)
  }

  // 新接口请求方法（使用 /XY 前缀）
  xyOptions = (params?: any, method?: string, header?: any): Promise<any> => {
    return this.makeRequest(serviceXY, params, method, header)
  }

  // 通用请求方法
  private makeRequest = (axiosInstance: any, params?: any, method?: string, header?: any, isShowErr?: boolean): Promise<any> => {
    if (!params) params = {}
    if (!method) method = 'POST'
    // 获取LoginToken
    let userData = localStorage.getItem('userData') || ('' as any)
    let LoginToken = ''
    if (userData) {
      userData = JSON.parse(userData) as any
      LoginToken = userData.login_token || ''
    }

    const { url, data, responseType = '', isFormData = false } = params
    const headers = { LoginToken, ...header }

    if (isFormData) {
      headers['Content-Type'] = 'multipart/form-data'
    }

    const option = {
      url,
      data: method === 'POST' ? data : {},
      params: method === 'GET' ? data : {},
      method,
      responseType,
      headers,
    }

    return new Promise((resolve, reject) => {
      axiosInstance(option)
        .then((res) => {
          if (responseType === 'blob') {
            return resolve(res)
          }

          const result = res.data || {}

          // 检查success字段，如果为false则显示错误信息
          if (result.success === false) {
            // 对于特定的错误代码（1000和3000），不显示错误消息，直接reject
            if (result.code === 1000 || result.code === 3000) {
              return reject(result)
            }

            // 检查是否在退出登录状态，避免显示不必要的错误消息
            const userData = localStorage.getItem('userData')
            const isLoggedOut = !userData

            // 如果用户已退出登录且当前在登录相关页面，不显示错误消息
            if (isLoggedOut && (window.location.pathname === '/login' || window.location.pathname.includes('/logout'))) {
              if (import.meta.env.VITE_APP_ENV === 'development') {
                console.log('用户已退出登录，跳过错误消息显示:', result.message || '请求失败')
              }
              return reject(result)
            }

            if (isShowErr) {
              message.error({ content: result.message || '请求失败', key: 'msg' })
            }
            return reject(result)
          }

          // 成功响应：检查code和success字段
          if ((result.code === 0 || result.code === 1) && result.success) {
            return resolve(result)
          }

          // 兼容旧接口：如果没有success字段但code为0或1，也认为成功
          if ((result.code === 0 || result.code === 1) && result.success === undefined) {
            return resolve(result)
          }

          // 其他情况视为失败
          if (isShowErr) {
            message.error({ content: result.message || '请求失败', key: 'msg' })
          }
          return reject(result)
        })
        .catch((err) => {
          // 处理登录过期
          if (err?.response?.data?.code === 1001) {
            message.error({ content: err.response.data.message || '登录过期，请重新登录~', key: 'msg' })
            beforLogout()
            setTimeout(() => {
              router.replace('/')
            }, 1000)
            return
          }

          // 检查是否在退出登录状态，避免显示不必要的错误消息
          const userData = localStorage.getItem('userData')
          const isLoggedOut = !userData

          // 如果用户已退出登录且当前在登录相关页面，不显示错误消息
          if (isLoggedOut && (window.location.pathname === '/login' || window.location.pathname.includes('/logout'))) {
            if (import.meta.env.VITE_APP_ENV === 'development') {
              console.log('用户已退出登录，跳过错误消息显示:', err?.response?.data?.message || err.message)
            }
            return reject(err)
          }

          message.error({ content: err?.response?.data?.message || '请求异常', key: 'msg' })
          return reject(err)
        })
    })
  }
}

const httpRequest = new HttpRequest()

// 导出旧接口请求方法（/api 前缀）
export const request = httpRequest.baseOptions

// 导出新接口请求方法（/XY 前缀）
export const requestXY = httpRequest.xyOptions
