/// <reference types="vite/client" />

declare module 'virtual:pwa-register' {
  export interface RegisterSWOptions {
    immediate?: boolean
    onNeedRefresh?: () => void
    onOfflineReady?: () => void
    // eslint-disable-next-line no-unused-vars
    onRegisteredSW?: (swUrl: string, registration: ServiceWorkerRegistration | undefined) => void | Promise<void>
    // eslint-disable-next-line no-unused-vars
    onRegisterError?: (error: any) => void
    // eslint-disable-next-line no-unused-vars
    onRegistered?: (arg: any) => void
  }

  // eslint-disable-next-line no-unused-vars
  export function registerSW(options?: RegisterSWOptions): (reloadPage?: boolean) => Promise<void>
}

declare module 'virtual:uno.css' {
  const content: any
  export default content
}
