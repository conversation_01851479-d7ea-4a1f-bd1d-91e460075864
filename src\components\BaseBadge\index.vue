<template>
  <div class="flex items-center">
    <span class="base-badge" :style="{ backgroundColor: color || colorMap[type] }"></span>
    <span :style="{ color: color || colorMap[type] }">{{ label }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  type: 'success' | 'warning' | 'error' | 'info'
  label: string
  color?: string
}>()

const colorMap = {
  success: '#00B42A',
  warning: '#FF8D1A',
  error: '#EB1237',
  info: '#1890FF',
  default: '#999999',
}
</script>

<style scoped lang="scss">
.base-badge {
  display: inline-block;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  color: #fff;
  margin-right: 4px;
}
</style>
