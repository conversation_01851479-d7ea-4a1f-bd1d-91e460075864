// 角色接口 - 已废弃，请使用 RoleNew.ts 中的新接口
// ⚠️ 警告：此文件中的所有接口已迁移到 RoleNew.ts，使用新的 /XY/Role/ 路径
// 请不要再使用此文件中的接口，它们可能返回404错误

// 重新导出新的角色接口，保持兼容性
export {
  Add,
  Update,
  UpdateRoleStatus,
  Delete,
  GetList,
  GetListByRole,
  SaveAuthRole,
  GetRoleSelectOption,
  GetOpLogInfos,
  Detail,
  DetailsByEdit,
  // 新增的接口
  CopyRole,
  GetRoleById,
  UpdateRoleContacts,
  GetRoleMenuCount,
  AddRoleGroup,
  UpdateRoleGroup,
  DeleteRoleGroup,
  GetRoleGroupList,
} from './RoleNew'

// === 以下是已废弃的老接口，仅作为参考保留 ===
/*
import { request } from './request'

// 新增角色 - 已废弃，请使用 RoleNew.Add
export const Add = (data) => {
  return request({ url: '/api/Role/Add', data })
}

// 编辑角色 - 已废弃，请使用 RoleNew.Update
export const Update = (data) => {
  return request({ url: '/api/Role/Update', data })
}

// 停用/启用角色 - 已废弃，请使用 RoleNew.UpdateRoleStatus
export const UpdateRoleStatus = (data) => {
  return request({ url: '/api/Role/UpdateRoleStatus', data })
}

// 删除角色 - 已废弃，请使用 RoleNew.Delete
export const Delete = (data) => {
  return request({ url: '/api/Role/Delete', data })
}

// 查询角色列表 - 已废弃，请使用 RoleNew.GetList
export const GetList = (data) => {
  return request({ url: '/api/Role/GetList', data })
}

// 根据角色获取权限列表 - 已废弃，请使用 RoleNew.GetListByRole
export const GetListByRole = (data) => {
  return request({ url: '/api/Role/GetListByRole', data })
}

// 选择角色授权权限 - 已废弃，请使用 RoleNew.SaveAuthRole
export const SaveAuthRole = (data) => {
  return request({ url: '/api/Role/SaveAuthRole', data })
}

// 获取角色下拉框 - 已废弃，请使用 RoleNew.GetRoleSelectOption
export const GetRoleSelectOption = (data) => {
  return request({ url: '/api/Role/GetRoleSelectOption', data })
}

// 获取角色日志 - 已废弃，请使用 RoleNew.GetOpLogInfos
export const GetOpLogInfos = (data) => {
  return request({ url: '/api/Role/GetOpLogInfos', data })
}

// 获取角色详情 - 已废弃，请使用 RoleNew.Detail 或 RoleNew.GetRoleById
export const Detail = (data) => {
  return request({ url: '/api/Role/Details', data })
}

// 获取角色详情 - 已废弃，请使用 RoleNew.DetailsByEdit 或 RoleNew.GetRoleById
export const DetailsByEdit = (data) => {
  return request({ url: '/api/Role/DetailsByEdit', data })
}
*/
