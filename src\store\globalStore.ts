import { defineStore } from 'pinia'
// import router from '@/router'

const globalStore = defineStore('page', {
  state: () => ({
    token: '',
    userInfo: {},
  }),
  actions: {
    setToken(value: string) {
      this.token = value
      localStorage.setItem('token', value)
      setTimeout(() => {
        // getUserInfo({ auth: value }).then((data: any) => {
        //     if (data.userHade && !data.userHade.includes('http')) {
        //         data.userHade = config.BT_userHade_baseURL + data.userHade
        //     }
        //     this.userInfo = data
        //     // this.GetExperienceInfo()
        // }).catch(() => {
        //     this.unLogin()
        // })
        // this.initExperience()
      }, 0)
    },
    unLogin(callBack?: () => void) {
      // 明确回调函数类型：无参数，返回值为 void
      this.userInfo = {}
      this.token = ''
      localStorage.removeItem('token')
      if (callBack) {
        callBack()
      }
      setTimeout(() => {
        // router.push('/')
      }, 2000)
    },
  },
})

export default globalStore
