<template>
  <div class="h-full flex-shrink-0">
    <a-image :src="src || ErrorIcon" :preview="!!src" height="100%" style="object-fit: cover; aspect-ratio: 1/1; flex-shrink: 0" v-bind="$attrs">
      <template #previewMask>
        <EyeOutlined />
      </template>
    </a-image>
  </div>
</template>

<script setup lang="ts">
import { EyeOutlined } from '@ant-design/icons-vue'
import ErrorIcon from '@/assets/icons/error-image.svg'

withDefaults(
  defineProps<{
    src: string
    height?: number | string
  }>(),
  {
    src: '',
    height: '100%',
  },
)
</script>

<style lang="scss" scoped></style>
