// 用户模块
import { request } from './request'
import { GetUserInfo as GetCurrentUserInfoNew } from './UserInfo'
// 登录接口
export const Login = (data) => {
  // return request({ url: '/api/Login/AdLogin', data }, 'GET');
  return request({ url: '/api/User/Login', data })
}

// 获取系统信息 - 注意：原接口已失效，暂时返回空数据避免404错误
export const Info = () => {
  console.warn('Info接口已失效，请联系后端提供新的系统信息接口')
  return Promise.resolve({ data: { version: '1.0.0' } })
}

// 获取用户详情 - 使用新的接口
export const UserInfo = () => {
  return GetCurrentUserInfoNew()
}
// 获取登录用户信息
export const GetUserInfo = (data) => {
  return request({ url: '/api/user/info', data }, 'GET')
}

// 获取用户偏好设置 - 已废弃，使用localStorage替代
export const GetUserPreference = () => {
  console.warn('GetUserPreference接口已废弃，请使用localStorage获取用户偏好设置')
  return Promise.resolve({ data: { isOpenLog: 0 } })
}

// 设置用户偏好设置 - 已废弃，使用localStorage替代
export const SetUserPreference = (data: any) => {
  console.warn('SetUserPreference接口已废弃，请使用localStorage保存用户偏好设置', data)
  return Promise.resolve({ success: true })
}

// 获取指定用户信息
export const GetUserInfoById = (data) => {
  return request({ url: '/api/User/getUserInfoById', data })
}

// === 以下接口已迁移到新的 /XY/User/ 路径，使用新的 UserInfo.ts 文件 ===

// 重新导出新的用户信息接口，避免命名冲突
export {
  GetUserPermissions as GetCurrentUserPermissions,
  GetUserInfo as GetCurrentUserInfo,
  GetUserAccountList as GetCurrentUserAccountList,
  GetUserRoleInfo,
  CheckUserIfHasRole,
  GetContactCompanies,
} from './UserInfo'
