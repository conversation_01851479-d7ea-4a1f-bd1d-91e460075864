import { request } from './request'

// 获取资质证书列表
export const GetList = (data) => request({ url: '/api/Certificate/GetList', data }, 'POST')

// 导出资质证书
export const ExportCertificate = (data) => request({ url: '/api/Certificate/Export', data, responseType: 'blob' })

// 新增资质证书
export const AddCertificate = (data) => request({ url: '/api/Certificate/BatchAdd', data }, 'POST')

// 获取文件列表
export const GetFileList = (data) => request({ url: '/api/Files/GetFileList', data }, 'POST')

// 重新上传资质证书
export const ReUploadCertificate = (data) => request({ url: '/api/Certificate/Update', data }, 'POST')

// 获取资质证书详情
export const GetCertificateDetail = (data) => request({ url: '/api/Certificate/GetDetail', data }, 'GET')
