import { useSignalR } from '@/composables/useSignalR'
import { GetUserPermissions, GetUserInfo } from '@/servers/UserInfo'
import { message as $message } from 'ant-design-vue'
import { showExportNotification } from '@/utils/exportNotification'

// 使用全局对象存状态，防止模块变量在热重载/页面跳转时失效
declare global {
  // eslint-disable-next-line no-unused-vars
  interface Window {
    __signalRState?: {
      started: boolean
    }
  }
}

if (!window.__signalRState) {
  window.__signalRState = {
    started: false,
  }
}

export async function initSignalRConnection() {
  if (window.__signalRState?.started) return

  const userDataStr = localStorage.getItem('userData')
  if (!userDataStr) {
    console.warn('尚未登录，跳过 SignalR 初始化')
    return
  }

  let userData
  try {
    userData = JSON.parse(userDataStr)
  } catch (e) {
    console.error('解析 userData 失败:', e)
    return
  }

  const login_token = userData.login_token
  const userId = userData.id

  if (!login_token || !userId) {
    console.warn('用户信息缺失，无法建立 SignalR 连接')
    return
  }

  const hubUrl = `${import.meta.env.VITE_APP_BASE_API}/backendhub?userId=${userId}&userToken=${login_token}`
  // 手动移除可能出现的 &id= 参数
  const cleanHubUrl = hubUrl.replace(/&id=[^&]+/, '')
  const { start, on, disableReconnect } = useSignalR(cleanHubUrl)

  try {
    await start()
    if (window.__signalRState) {
      window.__signalRState.started = true
    }
    console.log('✅ SignalR 已启动')

    // 权限更新处理函数
    const handlePermissionUpdate = (type: string) => {
      console.log(`📥 收到权限更新消息：${type}`)

      // 同时获取用户权限和用户信息
      Promise.all([GetUserPermissions(), GetUserInfo()])
        .then(([permissionsRes, userInfoRes]) => {
          const newPermissions = permissionsRes.data
          const newUserInfo = userInfoRes.data
          const currentStr = localStorage.getItem('userData')
          if (!currentStr) return

          try {
            const current = JSON.parse(currentStr)
            // 更新权限信息
            current.permissions_infos = newPermissions
            // 更新用户信息
            Object.assign(current, newUserInfo)
            localStorage.setItem('userData', JSON.stringify(current))
            console.log('✅ 用户权限和信息已更新')
          } catch (e) {
            console.error('更新用户权限和信息失败', e)
          }
        })
        .catch((error) => {
          console.error('获取用户权限和信息失败', error)
        })

      $message.warning('当前用户权限有修改，请刷新同步页面数据。', 5)
    }

    // 注册权限相关消息监听
    on('ReceiveRolePermission', () => setTimeout(() => handlePermissionUpdate('ReceiveRolePermission'), 800))
    on('ReceiveRoleChange', () => setTimeout(() => handlePermissionUpdate('ReceiveRoleChange'), 800))

    // 注册连接错误监听
    on('ReceiveConnectionError', (msg: string) => {
      console.warn('⚠️ SignalR 连接错误:', msg)
      if (window.__signalRState) {
        window.__signalRState.started = false // 防止死循环 false->true->但连接失败
      }
      disableReconnect() // 彻底停止重连
    })

    // 注册导出任务相关消息监听
    on('DownloadTaskCreated', (data: any) => {
      console.log('📥 收到导出任务进行中消息:', data)
      showExportNotification({
        type: 'progress',
        title: data.title || '导出文件准备中...',
        message: data.message || '文件已加入准备队列，您可以先进行其他操作，完成时将自动弹出下载框并存储在下载中心。',
        taskId: data.taskId,
        duration: 5, // 这里传入具体时长
      })
    })

    on('DownloadTaskComplete', (data: any) => {
      console.log('📥 收到导出任务完成消息:', data)
      console.log('📥 file_id:', data.file_id, 'file_name:', data.file_name, 'downloadUrl:', data.downloadUrl)
      showExportNotification({
        type: 'complete',
        title: data.title || '导出文件准备完成',
        message: data.message || '导出文件已经准备完成，文件下载链接将于24小时后失效，请尽快下载。',
        taskId: data.taskId || data.id,
        fileId: data.file_id, // 使用后端的字段名
        fileName: data.file_name, // 使用后端的字段名
        downloadUrl: data.downloadUrl,
        duration: 5, // 这里传入具体时长
      })
    })

    on('DownloadTaskFailed', (data: any) => {
      console.log('📥 收到导出任务失败消息:', data)
      showExportNotification({
        type: 'failed',
        title: data.title || '导出文件失败',
        message: data.message || '导出过程中发生错误，请重试',
        taskId: data.taskId,
        duration: 5, // 这里传入具体时长
      })
    })
  } catch (err) {
    console.error('❌ SignalR 启动失败:', err)
    if (window.__signalRState) {
      window.__signalRState.started = false // 防止死循环 false->true->但连接失败
    }
  }
}
