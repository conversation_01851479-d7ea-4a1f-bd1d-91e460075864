<template>
  <a-modal v-model:open="openModal" title="重新上传文件" @ok="showModal" centered @cancel="handleCancel" :width="1000">
    <a-form ref="formRef" :model="{ CertificateData }">
      <vxe-table :data="CertificateData" ref="CertificateTableRef">
        <vxe-column field="certificate_license_type" title="资质/证书" :width="200">
          <template #default="{ row }">
            <div class="flex">
              <a-form-item>
                <span>{{ row.certificate_name }}</span>
              </a-form-item>
            </div>
          </template>
          <template #header>
            <span class="redText1">*</span>
            <span>资质/证书</span>
          </template>
        </vxe-column>
        <vxe-column field="manufacturer_name" title="所属生产商">
          <template #default="{ row }">
            <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'manufacturer_name']" :rules="[{ required: true, message: '请填写资质/证书所属生产商' }]">
              <a-input v-model:value="row.manufacturer_name" placeholder="请填写资质/证书所属生产商" style="width: 220px" :maxlength="50" showCount />
            </a-form-item>
          </template>
          <template #header>
            <span class="redText2">*</span>
            <span>所属生产商</span>
            <a-tooltip>
              <template #title>
                <span>① 若您自己就是生产商，则所属生产商默认均是您自身；</span>
                <br />
                <span>② 若您有多个生产商，则需分别上传每家工厂的许可证。</span>
              </template>
              <QuestionCircleOutlined class="ml-8px" />
            </a-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="validity_period" title="有效期">
          <template #default="{ row }">
            <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'validity_period']" :rules="[{ validator: validityPeriodValidator }]">
              <a-range-picker :valueFormat="'YYYY-MM-DD'" v-model:value="row.validity_period" />
            </a-form-item>
          </template>
          <template #header>
            <span class="redText3">*</span>
            <span>有效期</span>
          </template>
        </vxe-column>
        <vxe-column field="fileList" title="上传文件" :width="180">
          <template #default="{ row, rowIndex }">
            <a-form-item :name="['CertificateData', CertificateData.indexOf(row), 'fileList']" :rules="[{ required: true, message: '请上传文件' }]">
              <span>共{{ row.fileNumber }}个</span>
              <span class="ml-12 c-#333">|</span>
              <a-button type="link" @click="handleUploadFile(row, rowIndex)">上传文件</a-button>
            </a-form-item>
          </template>
          <template #header>
            <span class="redText4">*</span>
            <span>上传文件</span>
          </template>
        </vxe-column>
      </vxe-table>
    </a-form>
    <template #footer>
      <a-button type="default" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit">确定</a-button>
    </template>
    <UploadFileModal ref="uploadFileRef" v-model:file-list="currentFileList" @update="handleUploadModalOk" :max-count="3" />
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VxeTableInstance } from 'vxe-table'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { GetFileList, ReUploadCertificate, GetCertificateDetail } from '@/servers/Certificate'
import { ViewByFileIdCommon } from '@/servers/Common'
import UploadFileModal from './UploadFileModal.vue'

const emit = defineEmits(['search'])
// 是否显示弹窗
const openModal = ref(false)
// 上传文件弹窗引用
const uploadFileRef = ref<any>(null)
// 当前行文件列表
const currentFileList = ref<any[]>([])
// 当前行索引
const currentRowIndex = ref<number | null>(null)
// // 资质证书表格数据
const CertificateData = ref<CertificateRow[]>([])
// 资质证书文件id
const fileIdList = ref<any>([])
// 资质证书id
const certificateId = ref<any>(null)
// 资质证书详情
const certificateDetail = ref<any>(null)
// 表单引用
const formRef = ref<any>()
// 表格引用
const CertificateTableRef = ref<VxeTableInstance<CertificateRow>>()
// 表格数据
interface CertificateRow {
  certificate_license_type: number // 资质/证书类型
  manufacturer_name: string // 所属生产商
  validity_period_start_time: dayjs.Dayjs | null // 有效期开始时间
  validity_period_end_time: dayjs.Dayjs | null // 有效期结束时间
  validity_period: [dayjs.Dayjs | null, dayjs.Dayjs | null] // 有效期
  fileList: any[] // 上传文件
  fileNumber: number // 上传文件数量
  certificate_name: string // 其他资质/证书名称(输入的值)
}
// 有效期验证
const validityPeriodValidator = (_: any, value: any) => {
  if (!value || !Array.isArray(value) || !value[0] || !value[1]) {
    return Promise.reject('请选择有效期')
  }
  const today = dayjs().startOf('day')
  if (dayjs(value[1]).isBefore(today)) {
    return Promise.reject('有效期结束时间不能小于当前日期')
  }
  if (dayjs(value[0]).isAfter(today)) {
    return Promise.reject('有效期开始时间不能大于当前日期')
  }
  return Promise.resolve()
}
// 显示弹窗
const showModal = (row: any) => {
  openModal.value = true
  certificateId.value = row.id
  CertificateData.value = [row]
  row.validity_period = [dayjs(row.validity_period_start_time), dayjs(row.validity_period_end_time)]
  fileIdList.value = row.file_ids.split(',')
  // 初始化 fileNumber 字段
  row.fileNumber = row.file_count
  getCertificateDetail()
  getFileList()
}
// 弹窗“确定”时调用
const handleUploadModalOk = () => {
  if (currentRowIndex.value !== null) {
    CertificateData.value[currentRowIndex.value].fileList = JSON.parse(JSON.stringify(currentFileList.value))
    // 更新 fileNumber
    CertificateData.value[currentRowIndex.value].fileNumber = currentFileList.value.length
    // 手动触发当前行的 fileList 校验，避免校验不消失
    formRef.value.validateFields([['CertificateData', currentRowIndex.value, 'fileList']])
  }
  console.log(
    'CertificateData',
    CertificateData.value.map((item) => item.fileList.map((file) => file.id)),
  )
}
// 关闭弹窗
const handleCancel = () => {
  openModal.value = false
}

// 确定
const handleSubmit = () => {
  // 正确生成校验字段；多行校验（现在只有单行）
  const fields = CertificateData.value
    .map((row) => {
      const idx = CertificateData.value.indexOf(row)
      const arr = [
        ['CertificateData', idx, 'certificate_license_type'],
        ['CertificateData', idx, 'manufacturer_name'],
        ['CertificateData', idx, 'certificate_name'],
        ['CertificateData', idx, 'validity_period'],
        ['CertificateData', idx, 'fileList'],
      ]
      return arr
    })
    .flat()
  // 校验表单
  formRef.value
    .validateFields(fields)
    .then(() => {
      const params = {
        id: certificateId.value,
        certificate_license_type: certificateDetail.value.certificate_license_type,
        certificate_name: CertificateData.value[0].certificate_name,
        manufacturer_name: CertificateData.value[0].manufacturer_name,
        validity_period_start_time: CertificateData.value[0].validity_period[0],
        validity_period_end_time: CertificateData.value[0].validity_period[1],
        // file_ids: certificateDetail.value.file_ids,
        file_ids: CertificateData.value.map((item) => item.fileList.map((file) => file.id)).join(','),
      }
      ReUploadCertificate(params).then(() => {
        message.success('提交成功')
        emit('search', CertificateData.value)
        handleCancel()
      })
    })
    .catch((err) => {
      // 校验未通过
      console.log(err)
    })
}
// 根据id获取文件列表
const getFileList = async () => {
  const params = {
    file_id_list: fileIdList.value,
  }

  const res = await GetFileList(params)
  // CertificateData.value[0].fileList =
  const list = await Promise.all(
    res.data.map(async (i) => {
      console.log('i', i)
      const result = await ViewByFileIdCommon(i.id)
      // 根据 original_name 判断文件类型
      let fileType = 'image'
      if (i.original_name) {
        const fileName = i.original_name.toLowerCase()
        if (fileName.endsWith('.pdf')) {
          fileType = 'pdf'
        }
      }
      return {
        ...i,
        name: i.original_name,
        url: URL.createObjectURL(result.data),
        status: 'done',
        type: fileType,
      }
    }),
  )
  list.forEach(async (item) => {
    if (item.type === 'pdf') {
      const { imageUrl } = await handlePdf(item)
      item.openUrl = item.url
      item.url = imageUrl
      item.windowOpten = true
    }
  })
  CertificateData.value[0].fileList = list
  console.log(CertificateData.value[0].fileList, 'CertificateData.value[0].fileList')
}

const handlePdf = async (item) => {
  if (item.type === 'pdf') {
    // 加载blob流
    const blob = new Blob([item.data], { type: 'application/pdf' })
    // 转为文件
    const file = new File([blob], item.original_name, { type: 'application/pdf' })

    const imageUrl = await getTxtAsImage(file)
    return { imageUrl }
  }
  return item.url
}

const getTxtAsImage = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsText(file)
    reader.onload = () => {
      const textContent = reader.result as string
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      if (!context) {
        reject('无法创建2D上下文')
        return
      }
      const fontSize = 14
      const lineHeight = 24
      const padding = 20
      const lines = textContent.split('\n')
      canvas.width = 800
      canvas.height = 500
      context.fillStyle = '#fff'
      context.fillRect(0, 0, canvas.width, canvas.height)
      context.font = `${fontSize}px Arial`
      context.fillStyle = '#000'
      context.textBaseline = 'top'
      lines.forEach((line, index) => {
        context.fillText(line, padding, padding + index * lineHeight)
      })
      const imageUrl = canvas.toDataURL('image/png')
      resolve(imageUrl)
    }
    reader.onerror = () => {
      reject('文件读取失败')
    }
  })
}

// 上传文件
const handleUploadFile = (row, index) => {
  console.log(row, index, 'row, index')

  currentRowIndex.value = index
  // 深拷贝，避免弹窗内操作影响原数据
  const data = toRaw(row)
  currentFileList.value = data.fileList
  console.log('currentFileList', currentFileList.value)

  uploadFileRef.value.showModal(row, index)
}
// 获取资质证书详情
const getCertificateDetail = async () => {
  const params = {
    id: certificateId.value,
  }
  const res = await GetCertificateDetail(params)
  certificateDetail.value = res.data
}
defineExpose({
  showModal,
})
</script>

<style scoped lang="scss">
.redText1 {
  color: #f87171;
  margin-right: 4px;
}
.redText2 {
  color: #f87171;
  margin-right: 4px;
}
.redText3 {
  color: #f87171;
  margin-right: 4px;
}
.redText4 {
  color: #f87171;
  margin-right: 4px;
}
</style>
