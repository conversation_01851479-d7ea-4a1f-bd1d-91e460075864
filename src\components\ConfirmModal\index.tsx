import { Modal } from 'ant-design-vue'
import type { ModalFuncProps } from 'ant-design-vue'
import { CloseCircleFilled } from '@ant-design/icons-vue'
import { defineComponent, h } from 'vue'
import type { PropType, VNode } from 'vue'

type ConfirmModalProps = Omit<ModalFuncProps, 'okText' | 'onOk' | 'type'> & {
  content: string | VNode | VNode[]
  confirmText?: string
  onConfirm: () => void
  type?: 'info' | 'success' | 'error' | 'warn' | 'warning' | 'confirm' | 'delete'
}

export default defineComponent({
  name: 'ConfirmModal',
  props: {
    content: {
      type: [String, Object, Array] as PropType<string | VNode | VNode[]>,
      required: true,
    },
    confirmText: {
      type: String,
      default: '确定',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    onConfirm: {
      type: Function as PropType<() => void>,
      required: true,
    },
    onCancel: {
      type: Function as PropType<() => void>,
      required: true,
    },
    title: {
      type: String,
      default: '提示',
    },
    width: {
      type: [String, Number],
      default: '520px',
    },
  },
  setup(props: ConfirmModalProps) {
    const show = () => {
      Modal.confirm({
        centered: true,
        title: props.title,
        content: props.content,
        okText: props.confirmText,
        cancelText: props.cancelText,
        onOk: props.onConfirm,
        onCancel: props.onCancel,
        width: props.width,
      })
    }

    return {
      show,
    }
  },
})

// 创建全局方法
export const confirm = {
  show: (options: ConfirmModalProps) => {
    const { title = '提示', confirmText = '确定', cancelText = '取消', onConfirm, width = '520px', type, ...ops } = options

    if (type === 'delete') {
      ops.okButtonProps = { ...ops.okButtonProps, danger: true }
      ops.icon = h(CloseCircleFilled, { style: { color: '#ff4d4f' } })
    }

    return Modal.confirm({
      centered: true,
      title,
      okText: confirmText,
      cancelText,
      onOk: onConfirm,
      width,
      ...ops,
    })
  },

  warn: (options: ConfirmModalProps) => {
    const { content, title = '提示', confirmText = '确定', onConfirm, width = '520px' } = options
    return Modal.warn({
      centered: true,
      width,
      title,
      content,
      okText: confirmText,
      onOk: onConfirm,
    })
  },
}
