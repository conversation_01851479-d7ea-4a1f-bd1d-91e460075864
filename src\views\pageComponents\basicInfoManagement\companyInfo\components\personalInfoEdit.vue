<template>
  <a-drawer v-if="open" v-model:open="open" width="85%" class="custom-class" root-class-name="drawerrootclass" :bodyStyle="{ padding: '0px' }" :title="title" placement="right" @close="closeDrawer">
    <div class="personalInfoEditBox">
      <a-form ref="fromref" :model="tableobj" name="basic" autocomplete="off">
        <div class="PI_Box" v-for="(item, index) in tableInfo" :key="index">
          <div class="PI_Box_Title">{{ item.title }}</div>
          <div class="PI_Box_Items">
            <div class="PI_Box_Item" v-for="(titem, tindex) in item.tableItem" :key="tindex" :style="{ width: titem.editwidth + '%' }">
              <div v-if="titem.edittype != 'table'" class="PI_Box_Item_label" :style="{ width: titem.labelwidth + 'px', 'min-width': titem.labelwidth + 'px' }">
                <span v-if="titem.ismust == true" style="color: red">*</span>
                {{ titem.label }}：
              </div>

              <div v-if="titem.edittype == 'text'" class="PI_Box_Item_content">
                {{ tableobj[titem.key] }}
                {{ titem.key == 'business_license_validity' && tableobj.is_long == true ? '（长期）' : '' }}
              </div>
              <div v-if="titem.edittype == 'textoptions'" class="PI_Box_Item_content">
                <template v-for="(otiem, oindex) in titem.options" :key="oindex">
                  <template v-if="otiem.value == tableobj[titem.key]">
                    {{ otiem.label }}
                  </template>
                </template>
              </div>
              <div v-if="titem.edittype == 'link'" class="PI_Box_Item_content">
                {{ tableobj[titem.key] }}
                <span class="PI_Box_Item_link" @click="ToPage(titem.link)">{{ titem.linkbtn }}</span>
              </div>
              <div v-if="titem.edittype == 'linkkey'" class="PI_Box_Item_content">
                <span class="PI_Box_Item_link" @click="ToPagebyId(tableobj[titem.key])">{{ titem.linkbtn }}</span>
              </div>
              <div v-if="titem.edittype == 'linkkeyarr'" class="PI_Box_Item_content">
                <span class="PI_Box_Item_link" @click="setVisible(true, titem)">{{ titem.linkbtn }}</span>
                <a-image-preview-group
                  v-if="titem.isshow"
                  :style="{ display: 'none' }"
                  :preview="{
                    visible: titem.isshow,
                    onVisibleChange: (e) => setVisible(e, titem),
                  }"
                >
                  <template v-for="(imgitem, imgindex) in titem.img" :key="imgindex">
                    <a-image :width="0" :src="imgitem" />
                  </template>
                </a-image-preview-group>
              </div>
              <a-form-item
                :style="{ width: titem.editwidth + '%', 'max-width': '600px' }"
                v-if="titem.edittype == 'textinput'"
                :label="''"
                :name="[titem.key]"
                :rules="[{ required: titem.ismust == true ? true : false, message: '请输入' + titem.label }]"
              >
                <a-input v-model:value="tableobj[titem.key]" :showCount="true" :maxlength="titem.maxlength" />
              </a-form-item>

              <a-form-item
                :style="{ width: titem.editwidth + '%' }"
                v-if="titem.edittype == 'checkbox'"
                :label="''"
                :name="[titem.key]"
                :rules="[{ required: titem.ismust == true ? true : false, message: '请选择' + titem.label }]"
              >
                <a-checkbox-group v-model:value="tableobj[titem.key]" :options="titem.options" @change="handleCheckBoxChange(titem.key)" />
              </a-form-item>
              <a-form-item
                :style="{ width: titem.editwidth + '%' }"
                v-if="titem.edittype == 'radio'"
                :label="''"
                :name="[titem.key]"
                :rules="[{ required: titem.ismust == true ? true : false, message: '请选择' + titem.label }]"
              >
                <a-radio-group v-model:value="tableobj[titem.key]" :options="titem.options" />
              </a-form-item>
              <a-form-item
                :style="{ width: titem.editwidth + '%' }"
                v-if="titem.edittype == 'textnumber'"
                :label="''"
                :name="[titem.key]"
                :rules="[{ required: titem.ismust == true ? true : false, message: '请选择' + titem.label }]"
              >
                <a-input-number v-model:value="tableobj[titem.key]" :addon-after="titem.suffix" :min="titem.min" :max="titem.max" :precision="titem.precision"></a-input-number>
              </a-form-item>
              <a-form-item
                :style="{ width: titem.editwidth + '%' }"
                v-if="titem.edittype == 'textnumberarr'"
                :label="''"
                :name="[titem.key, titem.key2]"
                :rules="[{ required: titem.ismust == true ? true : false, validator: () => getNumberRules(tableobj[titem.key], tableobj[titem.key2], titem.ismust) }]"
              >
                <div class="textnumberarr">
                  <a-input-number v-model:value="tableobj[titem.key]" :addon-after="titem.suffix" :min="titem.min" :precision="titem.precision" :max="titem.max"></a-input-number>
                  <span class="textnumberarrspan">~</span>
                  <a-form-item-rest>
                    <a-input-number v-model:value="tableobj[titem.key2]" :addon-after="titem.suffix2" :min="titem.min" :precision="titem.precision" :max="titem.max"></a-input-number>
                  </a-form-item-rest>
                </div>
              </a-form-item>
              <a-form-item
                v-if="titem.edittype == 'textselect'"
                :style="{ width: '100%' }"
                :label="''"
                :name="[titem.key]"
                :rules="[{ required: titem.ismust == true ? true : false, message: '请选择' + titem.label }]"
              >
                <a-select style="width: 100%" v-model:value="tableobj[titem.key]" :options="titem.options"></a-select>
              </a-form-item>
              <a-form-item
                :style="{ width: titem.editwidth + '%', 'max-width': '600px' }"
                v-if="titem.edittype == 'uploadfile'"
                :label="''"
                :name="[titem.key]"
                :rules="[{ required: titem.ismust == true ? true : false, message: '请完成' + titem.label }]"
              >
                <!-- action="https://www.mocky.io/v2/5cc8019d300000980a055e76" 
                 v-model:file-list="fileList" :beforeUpload="beforeUpload" list-type="picture" :max-count="1" accept=".xlsx"-->
                <!-- <a-upload v-model:file-list="tableobj[titem.key]" :headers="headers" @chenge="handleuploadChange">
                  <a-button>上传文件</a-button>
                </a-upload> -->

                <div class="uploadBox">
                  <a-upload
                    v-model:file-list="tableobj[titem.key]"
                    :multiple="true"
                    :beforeUpload="beforeUpload"
                    @change="handleupaloadChange"
                    @remove="removefile"
                    accept=".png,.jpg,.jpeg,.pdf,image/png,image/jpeg,application/pdf"
                    :max-count="titem.maxcount"
                    :showUploadList="false"
                  >
                    <a-button>上传文件</a-button>
                  </a-upload>
                  <div class="uploadBoxtext">
                    1. 支持文件格式：PNG，JPEG，JPG，PDF等；
                    <br />
                    2. 限制单个文件 ≤ 10M ；
                  </div>
                </div>
              </a-form-item>
              <div v-if="titem.edittype == 'richtxet'" class="richtextbox" v-html="titem.html"></div>
              <vxe-table border class="tableClass" v-if="titem.edittype == 'table'" :data="tableobj[titem.key]">
                <vxe-column v-for="(ttitem, ttindex) in titem.tableInfo" :key="ttindex" width="auto" align="center">
                  <template #header>
                    <span v-if="ttitem.ismust" style="color: red">*</span>
                    {{ ttitem.title }}
                  </template>
                  <template v-if="ttitem.edittype == 'control'" #default="{ row, $rowIndex }">
                    <span class="PI_Box_Item_link" @click="ToPagebyId(row[ttitem.fieldkey])">预览</span>
                    <span class="PI_Box_Item_link" @click="subtractrow($rowIndex, titem.key)">删除</span>
                  </template>
                  <template v-else-if="ttitem.edittype == 'textinput'" #default="{ row }">
                    <a-form-item
                      :style="{ width: '100%', 'max-width': '600px' }"
                      :label="''"
                      :name="[ttitem.key]"
                      :rules="[{ required: ttitem.ismust == true ? true : false, validator: () => getInputRules(row[ttitem.fieldkey], ttitem.title, ttitem.ismust, ttitem.fieldkey) }]"
                    >
                      <a-input @click="Clickfieldkey(ttitem.fieldkey)" v-model:value="row[ttitem.fieldkey]" :showCount="true" :maxlength="ttitem.maxlength" />
                    </a-form-item>
                  </template>
                  <template v-else-if="ttitem.edittype == 'textinputphone'" #default="{ row }">
                    <a-form-item
                      :style="{ width: '100%', 'max-width': '600px' }"
                      :label="''"
                      :name="[ttitem.key]"
                      :rules="[{ required: ttitem.ismust == true ? true : false, validator: () => getInputRules(row[ttitem.fieldkey], ttitem.title, ttitem.ismust, ttitem.fieldkey) }]"
                    >
                      <a-input @click="Clickfieldkey(ttitem.fieldkey)" v-model:value="row[ttitem.fieldkey]" :showCount="true" :maxlength="ttitem.maxlength" />
                    </a-form-item>
                  </template>
                  <template v-else-if="ttitem.edittype == 'textselect'" #default="{ row }">
                    <a-form-item
                      :style="{ width: '100%', 'max-width': '600px' }"
                      :label="''"
                      :name="[ttitem.key]"
                      :rules="[{ required: ttitem.ismust == true ? true : false, validator: () => getSelectRules(row[ttitem.fieldkey], ttitem.title, ttitem.ismust) }]"
                    >
                      <a-select v-model:value="row[ttitem.fieldkey]" style="width: 100%" :options="ttitem.options"></a-select>
                    </a-form-item>
                  </template>
                  <template v-else-if="ttitem.edittype == 'defaultcontact'" #default="{ row, $rowIndex }">
                    <a-form-item
                      :style="{ width: '100%', 'max-width': '600px' }"
                      :label="''"
                      :name="[ttitem.key]"
                      :rules="[{ required: ttitem.ismust == true ? true : false, validator: () => getdefaultcontactRules(row[ttitem.fieldkey], ttitem.title, ttitem.ismust) }]"
                    >
                      <a-select
                        v-model:value="row[ttitem.fieldkey]"
                        style="width: 100%"
                        :options="ttitem.options"
                        @change="Changedefaultcontact($rowIndex, row, titem.key, ttitem.fieldkey)"
                      ></a-select>
                    </a-form-item>
                  </template>
                  <template v-else-if="ttitem.edittype == 'edit'" #default="{ $rowIndex }">
                    <PlusOutlined v-if="$rowIndex + 1 == tableobj[titem.key].length" @click="addrow(titem.tableInfo, titem.key)" />
                    <MinusOutlined v-else @click="subtractrow($rowIndex, titem.key)" />
                  </template>
                  <template v-else #default="{ row }">{{ row[ttitem.fieldkey] }}</template>
                </vxe-column>
              </vxe-table>
            </div>
          </div>
        </div>
      </a-form>
    </div>
    <div class="personalInfoEditFooter">
      <a-button @click="closeDrawer">关闭</a-button>
      <a-button type="primary" @click="saveSubmit">保存</a-button>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
import { UpdateSupplierLicenseFileIds, UpdateSupplierExpand, UpdateSupplierFollow } from '../../../../../servers/companyInfo'

const userData = ref()
const emits = defineEmits(['closeDrawer'])
const uploadkey = ref('')
const VITE_APP_ENV = ref('')
onMounted(() => {
  VITE_APP_ENV.value = import.meta.env.VITE_APP_ENV
  console.log('子组件')
})
const fromref = ref()
const open = ref(false)
const tableInfo = ref()
const tableobj = ref()
const title = ref('')
const activeKey = ref()
const href = ref()
const setVisible = (value, titem): void => {
  titem.img = []
  titem.key.forEach(async (key) => {
    let url = ''
    const id = tableobj.value[key]

    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${id}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${id}`
    }
    console.log('url', url)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })

    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    titem.img.push(previewUrl)
  })

  titem.isshow = value
}
const init = (tableItems, value, titlevalue, uploadkeyvalue, activeKeyid) => {
  console.log('import.meta.env.VITE_APP_ENV', import.meta.env.VITE_APP_ENV)
  href.value = window.location.origin
  open.value = true
  tableInfo.value = tableItems
  tableobj.value = JSON.parse(JSON.stringify(value))
  title.value = titlevalue
  uploadkey.value = uploadkeyvalue
  activeKey.value = activeKeyid
  const userDatastr = localStorage.getItem('userData') || ''
  userData.value = userDatastr != '' ? JSON.parse(userDatastr) : {}
  if (fromref.value != null) {
    fromref.value.resetFields()
  }

  console.log('userData.value', userData.value)
}
const ToPage = (link) => {
  window.open(link, '_blank')
}
const ToPagebyId = async (id) => {
  console.log('window', window.location)
  let url = ''
  if (VITE_APP_ENV.value == 'development') {
    url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${id}`
  } else {
    url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${id}`
  }
  console.log('url', url)

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })
    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    // 在新窗口打开预览
    window.open(previewUrl, '_blank')
    // 可选：一段时间后释放内存
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    return previewUrl
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}

const getNumberRules = (minvalue, maxvalue, ismust) => {
  console.log('getRules', minvalue, maxvalue)
  if ((minvalue == null || maxvalue == null) && ismust == true) {
    return Promise.reject('不能为空')
  }
  if (minvalue > maxvalue && minvalue != null && maxvalue != null) {
    return Promise.reject('最小值不能大于最大值')
  }
  return Promise.resolve()
}
const isNumeric = (str) => {
  return /^\d+$/.test(str)
}
const getInputRules = (value, title, ismust, fieldkey) => {
  if ((value == null || value == '' || value.length == 0) && ismust == true) {
    return Promise.reject(`请输入${title}`)
  }
  if (fieldkey == 'mobile_phone_number' && isNumeric(value) == false) {
    return Promise.reject(`请输入数字`)
  }
  return Promise.resolve()
}
const getSelectRules = (value, title, ismust) => {
  if ((value == null || value == '' || value == 0) && ismust == true) {
    return Promise.reject(`请输入${title}`)
  }
  return Promise.resolve()
}
const getdefaultcontactRules = (value, title, ismust) => {
  if ((value == null || value == '' || value == 0) && ismust == true) {
    return Promise.reject(`请选择${title}`)
  }
  return Promise.resolve()
}
const Clickfieldkey = (key) => {
  console.log('key', key)
}
const Changedefaultcontact = (rowIndex, row, tablekey, fieldkey) => {
  // 1是，2否
  if (row[fieldkey] == 1) {
    // rowIndex之外全部改否
    tableobj.value[tablekey].forEach((item, index) => {
      if (index != rowIndex) {
        item[fieldkey] = 2
      }
    })
  } else {
    // 如果是否，默认第一条为默认联系人
    tableobj.value[tablekey].forEach((item, index) => {
      if (index == 0) {
        item[fieldkey] = 1
      }
    })
  }
}
const handleCheckBoxChange = (key) => {
  console.log('key', key)
  if (key == 'main_categories') {
    message.info('修改主营类目保存后将触发审核')
  }
}
const handleupaloadChange = (e) => {
  console.log('handleupaloadChange', e)
}
const formatCurrentDate = () => {
  const now = new Date()

  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
const beforeUpload = (file, uploadFileList) => {
  let check = true
  // eslint-disable-next-line no-async-promise-executor
  const promise = new Promise(async (resolve, reject) => {
    console.log(file, uploadFileList, '======================')

    const arr = ['image/png', 'image/jpeg', 'application/pdf']
    if (arr.indexOf(file.type) == -1) {
      message.error('支持文件格式：PNG，JPEG，JPG，PDF')
      check = false
    }

    if (file.size / 1024 / 1024 > 10) {
      message.error('文件大小不得大于10M')
      check = false
    }
    if (check) {
      // const params = {
      //   fileModule: 'Supplier',
      //   company_id: userData.value.company_id,
      //   account_id: userData.value.id,
      //   files: file,
      // }
      // const res = await UploadFile(params)
      // console.log('res', res)
      const formData = new FormData()
      formData.append('files', file) // 'file' 是后端接收文件的字段名
      formData.append('fileModule', 'Supplier')
      formData.append('account_id', userData.value.id)
      formData.append('company_id', userData.value.company_id)
      let url = ''
      if (VITE_APP_ENV.value == 'development') {
        url = `${window.location.origin}/api/api/Files/UploadFile`
      } else {
        url = `${window.location.origin}/api/Files/UploadFile`
      }
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
          logintoken: userData.value.login_token,
        },
        // 注意：不要设置 Content-Type 头，让浏览器自动添加边界信息
      })

      if (!response.ok) {
        throw new Error(`上传失败: ${response.status}`)
      }

      const res = await response.json()
      if (res.success == true) {
        file.id = res.data[0].id
        const obj = {
          id: res.data[0].id,
          original_name: res.data[0].name,
          account_id: userData.value.id,
          account_name: userData.value.real_name,
          create_at: formatCurrentDate(),
        }
        tableobj.value[uploadkey.value].push(obj)
        console.log('tableobj.value', tableobj.value)

        console.log('上传成功:', res)
      }

      // eslint-disable-next-line no-promise-executor-return
      return reject()
    }
  })

  // console.log('file', tableobj.value.fileList)

  return promise
}
const removefile = (e) => {
  // console.log('e', e, tableobj.value.fileList)
  let deleteindex = null
  tableobj.value[uploadkey.value].forEach((item, index) => {
    if (item.id == e.id) {
      deleteindex = index
    }
  })
  if (deleteindex != null) {
    tableobj.value[uploadkey.value].splice(deleteindex, 1)
  }
}
const addrow = (tableInfo, tablekey) => {
  console.log('tableInfo', tableInfo, tablekey)
  const obj = {}
  tableInfo.forEach((item) => {
    if (item.fieldkey != '' && item.fieldkey != null) {
      if (item.edittype == 'textinput') {
        obj[item.fieldkey] = ''
      }
      if (item.edittype == 'textselect') {
        obj[item.fieldkey] = null
      }
    }
  })
  if ((tablekey == 'srs_supplier_contact_infos' || tablekey == 'srs_supplier_finance_infos') && tableobj.value[tablekey].length < 5) {
    tableobj.value[tablekey].push(obj)
  } else {
    if (tablekey == 'srs_supplier_contact_infos' || tablekey == 'srs_supplier_finance_infos') {
      message.info('最多允许添加5行数据')
    }
  }
  // license_files
  if (tablekey == 'license_files') {
    tableobj.value[tablekey].push(obj)
  }
}
const subtractrow = (rowIndex, tablekey) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该条数据',
    onOk: () => {
      console.log('tablekey', tablekey, rowIndex)
      let check = false
      if (tablekey == 'srs_supplier_contact_infos' || tablekey == 'srs_supplier_finance_infos') {
        if (tableobj.value[tablekey][rowIndex].is_default == 1) {
          check = true
        }
      }
      tableobj.value[tablekey].splice(rowIndex, 1)
      if ((tablekey == 'srs_supplier_contact_infos' || tablekey == 'srs_supplier_finance_infos') && check == true && tableobj.value[tablekey].length > 0) {
        tableobj.value[tablekey][0].is_default = 1
      }
      // 当删除为文件证明表格，删除到没有文件时，上传文件改为必填
      if (tablekey == 'license_files') {
        console.log('tableInfo.value', tableInfo.value)
        tableInfo.value.forEach((item) => {
          item.tableItem.forEach((titem) => {
            if (titem.key == 'fileList') {
              if (tableobj.value[tablekey].length == 0) {
                titem.ismust = true
              }
            }
          })
        })
      }
    },
  })
}
const closeDrawer = () => {
  emits('closeDrawer')
  open.value = false
}
const saveSubmit = async () => {
  console.log('tableobj.value', tableobj.value)

  await fromref.value
    .validate()
    .then(async () => {
      if (activeKey.value == '1') {
        // let check = false
        // tableobj.value.main_categories.forEach((item) => {
        //   if (tableobj.value.old_main_categories.indexOf(item) == -1) {
        //     check = true
        //   }
        // })
        // if (check) {
        //   tableobj.value.audit_type = 2
        // }
        const params = {
          id: tableobj.value.id,
          factory_scale: tableobj.value.factory_scale,
          // main_categories: tableobj.value.old_main_categories, // 修改前的值
          main_categories: tableobj.value.main_categories, // 修改后的值
          main_regions: tableobj.value.main_regions,
          sku_count: tableobj.value.sku_count != null ? tableobj.value.sku_count : 0,
          main_products: tableobj.value.main_products,
          main_products_min_price: tableobj.value.main_products_min_price,
          main_products_max_price: tableobj.value.main_products_max_price,
          annual_sales: tableobj.value.annual_sales != null ? tableobj.value.annual_sales : 0,
          factory_employee_count: tableobj.value.factory_employee_count != null ? tableobj.value.factory_employee_count : 0,
          audit_type: 2,
          supplier_type: tableobj.value.supplier_type,
        }
        const res = await UpdateSupplierExpand(params)
        if (res.success == true) {
          closeDrawer()
          message.success('保存成功')
        } else {
          message.warning(res.message)
        }
      }
      if (activeKey.value == '2' || activeKey.value == '3') {
        const srs_supplier_contact_infos = JSON.parse(JSON.stringify(tableobj.value.srs_supplier_contact_infos))
        srs_supplier_contact_infos.forEach((item) => {
          item.is_default = item.is_default == 1
        })
        const srs_supplier_finance_infos = JSON.parse(JSON.stringify(tableobj.value.srs_supplier_finance_infos))
        srs_supplier_finance_infos.forEach((item) => {
          item.is_default = item.is_default == 1
        })
        const params = {
          id: tableobj.value.id,
          settlement_method: tableobj.value.settlement_method,
          invoice_type: tableobj.value.invoice_type,
          default_tax_rate: tableobj.value.default_tax_rate,
          srs_supplier_contact_infos,
          srs_supplier_finance_infos,
          audit_type: 2,
        }
        const res = await UpdateSupplierFollow(params)
        if (res.success == true) {
          closeDrawer()
          message.success('保存成功')
        } else {
          message.warning(res.message)
        }
      }
      if (activeKey.value == '4') {
        const arr: any = []
        tableobj.value.license_files.forEach((item) => {
          arr.push(item.id)
        })
        const params = {
          id: tableobj.value.id,
          license_file_ids: arr,
          is_audit: false,
          audit_type: 2,
        }
        const res = await UpdateSupplierLicenseFileIds(params)
        if (res.success == true) {
          closeDrawer()
          message.success('保存成功')
        } else {
          message.warning(res.message)
        }
      }
      console.log('通过1')
    })
    .catch((err) => {
      console.log('error', err)
    })
}
defineExpose({ init })
</script>
<style lang="scss" scoped>
.personalInfoEditBox {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: calc(100% - 60px);
  padding: 24px;
  overflow-y: auto;
  color: #000;

  .ant-form {
    width: 100%;
  }

  .PI_Box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    font-size: 14px;

    .PI_Box_Title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 36px;
      padding-left: 12px;
      background: #ebeef5;
    }

    .PI_Box_Items {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;

      .PI_Box_Item {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        min-height: 28px;
        margin-top: 5px;
        margin-bottom: 5px;
        font-size: 14px;

        .PI_Box_Item_label {
          text-align: right;
        }

        .PI_Box_Item_content {
          width: 80%;
          text-align: left;
          word-wrap: break-word;

          .PI_Box_Item_link {
            color: blue;
            cursor: pointer;
          }
        }

        .tableClass {
          width: 100%;
          margin-top: 0;

          .PI_Box_Item_link {
            margin-left: 10px;
            color: blue;
            cursor: pointer;
          }
        }

        .ant-form-item {
          width: 100%;
          margin-bottom: 0;
        }

        .uploadBox {
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          margin-left: 20px;

          .uploadBoxtext {
            margin-left: 20px;
            font-size: 10px;
            color: #666;
          }
        }

        .richtextbox {
          font-size: 10px;
          color: #666;
        }

        .textnumberarr {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
        }

        .textnumberarrspan {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          font-size: 14px;
        }
      }
    }
  }
}

.personalInfoEditFooter {
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 60px;
  padding-left: 24px;
  border-top: 1px solid #dedede;

  .ant-btn {
    margin-right: 20px;
  }
}
</style>
