// 限制输入数字的指令
import type { Directive } from 'vue'

export const vNumber: Directive = {
  mounted(el: HTMLInputElement) {
    el.addEventListener('input', (e: Event) => {
      const input = e.target as HTMLInputElement
      let value = input.value
      // 只允许输入数字和小数点
      value = value.replace(/[^\d.]/g, '')
      // 只保留第一个小数点，去除多余的小数点
      value = value.replace(/\.(?=.*\.)/g, '')
      // 小数点不能在首位
      if (value.startsWith('.')) {
        value = value.substring(1)
      }
      input.value = value
      // 触发input事件，确保v-model能正确更新
    })
  },
}
