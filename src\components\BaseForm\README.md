## BaseForm使用说明

 ***可配合hooks-useBaseForm使用***

 目前支持类型有
 - text - 普通文本
 - input - 输入框
 - select - 下拉框
 - textarea - 多行输入框
 - slos - 可自定义插入

###  实例
```javascript
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { SettingOutlined } from '@ant-design/icons-vue';

// 配置
const config = ref<BaseFormItem[]>([
  {
    type: 'input' // 上述支持类型
    key: 'name', // 后端返回的字段名
    label: '名称' // label可以传入字符串/插槽，如下
    span: 24,  // 24为占用一行的宽度
     // 传入a-input有的插槽
    slots: {
      /** 例如官方文档的
       * <template #addonAfter>
       *  <setting-outlined />
       * </template>
       * 则如下使用是
       */
      addonAfter: () => h(SettingOutlined)
    }
  },
  {
    type: 'select',
    key: 'sex',
    lable: h('div', { class: 'c-red'}, { deafult: '性别'}),
    span: 12, // 12为占用一半的宽度
    // 这里的props可以传入ant组件库a-select能传的配置项
    props: {
      options: [
        {label: '男', value: 1},
        {lbale: '女', value: 0}
      ]
    },
   
  }
])

/**
 * 定义一个用于双向绑定的form表单传入useBaseForm的modelValue里
 * 用于接口传递与默认值显示
 */
const form = ref({
  name: '张三',
  sex: 1
})

/** 
 * 结构数据说明 - 抛出是数组，名称可自定义
 * AttrFrom - 组件
 * attrFormRef - form组件的实例ref，用于调用ant-form校验方法与清空校验方法
 */
const [AttrForm, attrFormRef] = useBaseForm({
  modelValue: form, // 必填
  formConfig: config.value, // 必填，上面定义的配置项
  rules: attrRules, // 选填 a-form的规则
  ... // 可传入ant组件库a-form能传的配置项
})
``` 

### 实例-无注释
```javascript
<template>
  <div>
    <AttrForm />
  </div>
</template>

<script setup lang="ts">
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { SettingOutlined } from '@ant-design/icons-vue';

const config = ref<BaseFormItem[]>([
  {
    type: 'input'
    key: 'name', 
    label: '名称' 
    span: 24,  
    slots: {
   
      addonAfter: () => h(SettingOutlined)
    }
  },
  {
    type: 'select',
    key: 'sex',
    lable: h('div', { class: 'c-red'}, { deafult: '性别'}),
    span: 12, 
    props: {
      options: [
        {label: '男', value: 1},
        {lbale: '女', value: 0}
      ]
    },
   
  }
])


const form = ref({
  name: '张三',
  sex: 1
})


const [AttrForm, attrFormRef] = useBaseForm({
  modelValue: form,
  formConfig: config.value, 
  rules: attrRules,
})

</script>

```