import { request } from './request'

// 获取商品库存列表
export const GetList = (data) => request({ url: '/api/ProductInfo/GetProductInventoryList', data }, 'POST')

// 获取商品库存详情
export const GetDetail = (data) => request({ url: '/api/ProductInfo/GetProductInventoryList', data }, 'POST')

// 商品类目选项
export const GetCategoryOption = () => request({ url: '/plm/Category/GetCategorySelectOption', data: { type: 1 } }, 'POST')

// 获取商品预览地址 
export const GetProductPreviewUrl = (data) => request({ url: '/api/Files/PreViewUrlByFileId', data }, 'GET')

// 获取商品库存总数
export const GetProductStockTotal = (data) => request({ url: '/api/ProductInfo/GetProductInventoryTotal', data }, 'POST')

