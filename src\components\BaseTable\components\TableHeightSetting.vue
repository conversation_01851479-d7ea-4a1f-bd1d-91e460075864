<template>
  <a-dropdown :trigger="['click']">
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item v-for="item in menuItems" :key="item.key" :class="{ active: type == item.key }">
          <CheckOutlined :style="type != item.key ? `visibility: hidden;` : ''" class="icon" />
          <span :class="`iconfont ${item.icon} icon`"></span>
          {{ item.label }}
        </a-menu-item>
      </a-menu>
    </template>
    <a-button>
      <span v-for="item in menuItems" :key="item.key" v-show="type == item.key" :class="`iconfont ${item.icon}`"></span>
    </a-button>
  </a-dropdown>
</template>

<script setup lang="ts">
import { CheckOutlined } from '@ant-design/icons-vue'

const emits = defineEmits(['update:type'])
defineProps({
  type: {
    type: Number,
    default: 1,
  },
})
const handleMenuClick = (e: any) => {
  emits('update:type', e.key)
}

const menuItems = [
  {
    key: 1,
    icon: 'icon-hanggao-zhongdeng',
    label: '中等',
  },
  {
    key: 2,
    icon: 'icon-hanggao-gao',
    label: '高',
  },
  {
    key: 3,
    icon: 'icon-hanggao-chaogao',
    label: '超高',
  },
]
</script>

<style lang="scss" scoped>
.icon {
  margin-right: 8px;
}

::v-deep(.active) {
  color: #1890ff !important;
}

.ant-dropdown-trigger {
  width: 28px;
  height: 28px;
  padding: 0;
}
</style>
