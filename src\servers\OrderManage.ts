import { request } from './request'

// 获取商品订单列表
export const getOrderList = (data: any) => request({ url: '/srm/PurchaseOrder/GetList', data }, 'POST')

// 获取商品订单详情
export const getOrderDetail = (data: any) => request({ url: '/srm/PurchaseOrder/GetDetail', data }, 'POST')

// 获取入库明细
export const getStoreDetailList = (data: any) => request({ url: '/srm/PurchaseOrder/GetBookingOrderDetailList', data }, 'POST')

// 获取退库明细
export const getReturnDetailList = (data: any) => request({ url: '/srm/PurchaseOrder/GetPurchaseOrderReturnApplyOrderList', data }, 'POST')