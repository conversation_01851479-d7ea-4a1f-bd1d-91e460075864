<template>
  <SearchForm :form-arr="formArr" :page-type="pageType" @query="search" />
  <NewBaseTable ref="tableRef" :page-type="pageType" v-model:form="formArr" v-bind="$attrs">
    <template v-for="slot in Object.keys($slots)" :key="slot" #[slot]="data">
      <slot :name="slot" v-bind="data"></slot>
    </template>
  </NewBaseTable>
</template>

<script setup lang="ts">
import { PageType } from '@/common/enum'
import SearchForm from '@/components/SearchForm/index.vue'
import NewBaseTable from '@/components/NewBaseTable/index.vue'

defineProps<{
  pageType: PageType
}>()

const formArr = defineModel<any[]>('formArr', { required: true })

const tableRef = useTemplateRef<InstanceType<typeof NewBaseTable>>('tableRef')
const search = () => tableRef.value?.search()

defineExpose({
  search,
})
</script>

<style scoped></style>
