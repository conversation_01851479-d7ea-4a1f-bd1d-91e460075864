import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import CopyBtn from '@/components/CopyBtn/index.vue'
import { h, withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'
import { Image } from 'ant-design-vue'

// 通用的空值处理formatter函数
const defaultFormatter = ({ cellValue }) => ([null, undefined, ''].includes(cellValue) ? '--' : cellValue)

// 金额格式化函数，添加￥符号
const priceFormatter = ({ cellValue }) => {
  if ([null, undefined, ''].includes(cellValue)) return '--'
  const numValue = Number(cellValue)
  return isNaN(numValue) ? '--' : `￥${numValue.toFixed(2)}`
}

/** 用于在单元格复制按钮操作 */
VxeUI.renderer.add('copy', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    return h('div', {}, [h('span', {}, value || '--'), value && withDirectives(h(CopyBtn), [[vCopy, String(value)]])])
  },
})

VxeUI.renderer.add('image', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    return value ? h('div', { class: 'vxe-render-image' }, [h(Image, { src: value, alt: '图片', height: '100%', style: { objectFit: 'cover', aspectRatio: '1/1' } })]) : '--'
  },
})

export default VxeUI

export { defaultFormatter, priceFormatter }
