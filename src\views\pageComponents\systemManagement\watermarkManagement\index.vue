<template>
  <div>
    <div class="breadcrumbBox">
      <div class="title">全局水印</div>
    </div>
    <div class="cardBox">
      <div class="card">
        <div class="title">操作界面水印</div>
        <a-button :disabled="!btnPermission[32001]" @click="OperationInterfaceWatermarkRef.open()">设置</a-button>
        <a-button :disabled="!btnPermission[32002]" @click="log(1)">日志</a-button>
        <div class="description">配置后将生效于所有操作界面。</div>
      </div>
    </div>
    <!-- <div class="breadcrumbBox">
      <div class="title">审批设置</div>
    </div>
    <div class="cardBox">
      <div class="card">
        <div class="title">审批流程设置</div>
        <a-button :disabled="!btnPermission[32100]" @click="approvalSetting.open()">设置</a-button>
      </div>
    </div> -->

    <OperationInterfaceWatermark :rolesoptions="rolesOptions" ref="OperationInterfaceWatermarkRef" />
    <log-drawer ref="logDrawerRef" />
    <ApprovalSetting ref="approvalSetting" />
  </div>
</template>

<script setup lang="ts">
import { GetRoleSelectOption } from '@/servers/RoleNew'
import { checkPagePermission } from '@/utils'
import LogDrawer from './components/LogDrawer.vue'
import OperationInterfaceWatermark from './components/OperationInterfaceWatermark.vue'
import ApprovalSetting from './components/ApprovalSetting.vue'

const approvalSetting = ref<any>(null)
const logDrawerRef = ref<any>(null)
const rolesOptions = ref([])
const OperationInterfaceWatermarkRef = ref<any>(null)

const { btnPermission } = usePermission()

const log = (page) => {
  logDrawerRef.value.open(page)
}
onMounted(() => {
  // 检查当前用户是否有权限访问水印管理页面
  const hasWatermarkManagementPermission = checkPagePermission('/watermarkManagement')

  if (hasWatermarkManagementPermission) {
    GetRoleSelectOption({ page: 1, pageSize: 100 }).then((res) => {
      console.log('获取角色列表：', res)
      res.data.forEach((x) => {
        x.label = x.role_name
        x.value = x.role_id
      })
      rolesOptions.value = res.data
    })
  } else {
    console.warn('用户无权限访问水印管理页面，跳过接口调用')
  }
})
</script>

<style lang="scss" scoped>
.breadcrumbBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  margin-bottom: 8px;

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
}

.cardBox {
  border: 1px solid #dcdcdc;
  border-radius: 4px;

  .card {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 12px 24px;
    font-size: 12px;
    color: #000;

    .title {
      margin-right: 40px;
    }

    .description {
      margin-left: 10px;
      color: rgb(0 0 0 / 50%);
    }
  }
}
</style>
