// 角色接口 - 新的 /XY/Role/ 路径
import { requestXY } from './request'

// 新增角色
export const Add = (data) => {
  return requestXY({ url: '/Role/Add', data }, 'POST')
}

// 编辑角色
export const Update = (data) => {
  return requestXY({ url: '/Role/Update', data }, 'POST')
}

// 停用/启用角色
export const UpdateRoleStatus = (data) => {
  return requestXY({ url: '/Role/UpdateRoleStatus', data }, 'POST')
}

// 删除角色
export const Delete = (data) => {
  return requestXY({ url: '/Role/Delete', data }, 'POST')
}

// 批量设置角色关联公司
export const UpdateRoleContacts = (data) => {
  return requestXY({ url: '/Role/UpdateRoleContacts', data }, 'POST')
}

// 查询角色列表
export const GetList = (data) => {
  return requestXY({ url: '/Role/GetList', data }, 'POST')
}

// 复制角色
export const CopyRole = (data) => {
  return requestXY({ url: '/Role/CopyRole', data }, 'POST')
}

// 获取角色信息
export const GetRoleById = (id: number) => {
  return requestXY({ url: `/Role/GetRoleById?id=${id}` }, 'GET')
}

// 根据角色获取权限列表
export const GetListByRole = (data) => {
  return requestXY({ url: '/Role/GetListByRole', data }, 'POST')
}

// 编辑角色权限
export const SaveAuthRole = (data) => {
  return requestXY({ url: '/Role/SaveAuthRole', data }, 'POST')
}

// 获得角色对应的菜单数量，用于判断该角色是否是空菜单权限
export const GetRoleMenuCount = (data) => {
  return requestXY({ url: '/Role/GetRoleMenuCount', data }, 'POST')
}

// 获取角色下拉框
export const GetRoleSelectOption = (data) => {
  return requestXY({ url: '/Role/GetRoleSelectOption', data }, 'POST')
}

// 新增角色分组
export const AddRoleGroup = (data) => {
  return requestXY({ url: '/Role/AddRoleGroup', data }, 'POST')
}

// 编辑角色分组
export const UpdateRoleGroup = (data) => {
  return requestXY({ url: '/Role/UpdateRoleGroup', data }, 'POST')
}

// 删除角色分组
export const DeleteRoleGroup = (data) => {
  return requestXY({ url: '/Role/DeleteRoleGroup', data }, 'POST')
}

// 查询角色分组列表
export const GetRoleGroupList = (data) => {
  return requestXY({ url: '/Role/GetRoleGroupSelectOption', data }, 'POST')
}

// 获取日志
export const GetOpLogInfos = (data) => {
  return requestXY({ url: '/Role/GetOpLogInfos', data }, 'POST')
}

// === 兼容性别名接口 ===

// 兼容旧接口名
export const Detail = GetRoleById
export const DetailsByEdit = GetRoleById
