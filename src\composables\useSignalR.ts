import { ref } from 'vue'
import * as signalR from '@microsoft/signalr'

export function useSignalR(hubUrl: string) {
  const connection = ref<signalR.HubConnection | null>(null)
  const isConnected = ref(false)
  const error = ref<Error | null>(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 30
  const shouldReconnect = ref(true) // 默认允许重连

  // 初始化连接
  const initConnection = () => {
    connection.value = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.elapsedMilliseconds < 60000) {
            return Math.random() * 5000 // 前60秒内随机重连
          }
          return 60000 // 之后每分钟尝试一次
        },
      })
      .configureLogging(signalR.LogLevel.Debug)
      .build()

    // 连接事件监听
    connection.value.onclose(() => {
      isConnected.value = false
      console.log('SignalR 连接已断开')
      tryReconnect()
    })

    connection.value.onreconnecting(() => {
      isConnected.value = false
      console.log('SignalR 正在重新连接...')
    })

    connection.value.onreconnected(() => {
      isConnected.value = true
      reconnectAttempts.value = 0
      console.log('SignalR 重新连接成功')
    })
  }

  // 尝试重新连接
  const tryReconnect = async () => {
    if (!shouldReconnect.value) {
      console.warn('已标记为不再重连，跳过重连逻辑')
      return
    }

    if (reconnectAttempts.value >= maxReconnectAttempts) {
      console.error(`已达到最大重试次数(${maxReconnectAttempts})，停止重连`)
      return
    }

    reconnectAttempts.value++
    console.log(`尝试重新连接(${reconnectAttempts.value}/${maxReconnectAttempts})`)

    try {
      await start()
    } catch (err) {
      console.error('重新连接失败:', err)
      setTimeout(tryReconnect, 5000)
    }
  }

  // 启动连接
  const start = async () => {
    if (!connection.value || isConnected.value) return

    try {
      await connection.value.start()
      isConnected.value = true
      reconnectAttempts.value = 0
      console.log('SignalR 连接成功')
    } catch (err) {
      error.value = err as Error
      console.error('SignalR 连接失败:', err)
      throw err
    }
  }

  // 停止连接
  const stop = async () => {
    if (!connection.value || !isConnected.value) return

    try {
      await connection.value.stop()
      isConnected.value = false
      console.log('SignalR 连接已停止')
    } catch (err) {
      error.value = err as Error
      console.error('停止 SignalR 连接时出错:', err)
    }
  }

  // 监听服务端消息
  const on = (methodName: string, callback: (...args: any[]) => void) => {
    if (!connection.value) return
    connection.value.on(methodName, callback)
  }

  // 移除监听
  const off = (methodName: string, callback?: (...args: any[]) => void) => {
    if (!connection.value) return
    if (callback) {
      connection.value.off(methodName, callback)
    } else {
      connection.value.off(methodName)
    }
  }

  // 调用服务端方法
  const invoke = async (methodName: string, ...args: any[]) => {
    if (!connection.value || !isConnected.value) {
      throw new Error('SignalR 连接未建立')
    }
    return connection.value.invoke(methodName, ...args)
  }

  // 获取连接状态
  const getConnectionState = () => {
    if (!connection.value) return signalR.HubConnectionState.Disconnected
    return connection.value.state
  }

  const disableReconnect = () => {
    shouldReconnect.value = false
  }

  // 初始化
  initConnection()

  return {
    connection,
    isConnected,
    error,
    start,
    stop,
    on,
    off,
    invoke,
    getConnectionState,
    disableReconnect,
  }
}
