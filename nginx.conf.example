# Nginx配置示例 - 用于解决UMC回调页面静态资源404问题
# 请根据您的实际服务器环境调整配置

server {
    listen 8091;
    server_name **************;
    
    # 网站根目录 - 请修改为您的实际部署路径
    root /path/to/your/dist;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # API代理配置 - 转发到后端服务
    location /XY/ {
        proxy_pass http://127.0.0.1:8091;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 处理CORS
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,LoginToken';
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # UMC回调路径处理 - 关键配置
    location /UmcAuth/ {
        try_files $uri $uri/ /index.html;
    }
    
    # SPA路由回退 - 所有其他路径都返回index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 错误页面
    error_page 404 /index.html;
}

# 如果使用Apache服务器，请使用以下.htaccess配置：
# 
# RewriteEngine On
# 
# # 处理静态资源
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteCond %{REQUEST_URI} \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$
# RewriteRule . - [L,R=404]
# 
# # API代理（需要配置反向代理模块）
# RewriteRule ^XY/(.*)$ http://127.0.0.1:8091/XY/$1 [P,L]
# 
# # SPA路由回退
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule . /index.html [L]
# 
# # 启用gzip压缩
# <IfModule mod_deflate.c>
#     AddOutputFilterByType DEFLATE text/plain
#     AddOutputFilterByType DEFLATE text/html
#     AddOutputFilterByType DEFLATE text/xml
#     AddOutputFilterByType DEFLATE text/css
#     AddOutputFilterByType DEFLATE application/xml
#     AddOutputFilterByType DEFLATE application/xhtml+xml
#     AddOutputFilterByType DEFLATE application/rss+xml
#     AddOutputFilterByType DEFLATE application/javascript
#     AddOutputFilterByType DEFLATE application/x-javascript
# </IfModule>
