<template>
  <BasicForm />
  <BrandAddDrawer ref="brandRef" @save-success="getBrandOption" />
</template>

<script setup lang="ts">
import { useBaseForm, type BaseFormItem } from '@/hook/useBaseForm'
import { Tooltip } from 'ant-design-vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { GetCategoryOption } from '@/servers/ProductLibrary'
import { getCommonOption } from '@/utils'
// import Decimal from 'decimal.js'
import BrandAddDrawer from '@/views/pageComponents/productManagement/brand/components/EditDrawer.vue'

const brandRef = useTemplateRef<any>('brandRef')

const form = defineModel<any>('form')

// 行内提示
const rowTip = (label: string, title: VNode[]) => {
  return h('div', { class: 'flex items-center' }, [
    h('div', {}, label),
    h(
      Tooltip,
      { placement: 'topRight' },
      {
        default: () => h(QuestionCircleOutlined, { class: 'c-#666 ml-8px mr-17px' }),
        title: () => title,
      },
    ),
  ])
}

// const setSupplyPrice = () => {
//   basicDrawerForm.value.forEach((item) => {
//     if (item.type === 'input-number' && item.key === 'declared_purchase_price') {
//       item.hidden = !form.value.price_type
//       item.props!.disabled = form.value.price_type === 2
//       item.props!.placeholder = form.value.price_type === 2 ? '不含税单价，自动计算' : '请输入不含税单价'
//       item.props!.showCount = form.value.price_type === 1
//     } else if (item.type === 'select' && item.key === 'declared_purchase_tax_rate') {
//       item.hidden = !form.value.price_type
//     } else if (item.type === 'input-number' && item.key === 'declared_purchase_tax_price') {
//       item.hidden = !form.value.price_type
//       item.props!.disabled = form.value.price_type === 1
//       item.props!.placeholder = form.value.price_type === 1 ? '含税单价，自动计算' : '请输入含税单价'
//       item.props!.showCount = form.value.price_type === 2
//     }
//   })
// }

// 基础信息表单配置
const basicDrawerForm = ref<BaseFormItem[]>([
  {
    type: 'input',
    label: '商品名称(中)',
    key: 'product_name',
    span: 6,
    props: {
      maxlength: 50,
      placeholder: '请输入中文商品名称',
    },
  },
  {
    type: 'input',
    label: '商品名称(英)',
    key: 'product_name_en',
    span: 6,
    props: {
      maxlength: 100,
      placeholder: '请输入英文商品名称',
    },
  },
  {
    type: 'input',
    label: () => rowTip('供应商商品编码', [h('div', null, '用于您内部商品管理，与您的系统保持一致。')]),
    key: 'supplier_product_number',
    span: 6,
    props: {
      maxlength: 50,
      placeholder: '供应商内部商品编码',
    },
  },
  {
    type: 'select',
    label: '商品类型',
    span: 6,
    key: 'product_type',
    props: {
      disabled: true,
      options: [
        { label: '新品', value: 1 },
        { label: '定制品', value: 2 },
        { label: '物料', value: 3 },
      ],
    },
  },
  {
    type: 'select',
    label: () => h('div', { class: 'flex justify-between items-center w-full' }, [h('span', {}, '商品品牌'), h('span', { class: ' c-primary cursor-pointer ml-180', onClick: showBrandAdd }, '维护')]),
    span: 6,
    key: 'brand_id',
    props: {
      placeholder: '商品品牌',
      options: [],
      allowClear: true,
    },
    slots: {
      notFoundContent: () => h('div', { class: 'flex items-center' }, [h('span', {}, '无品牌数据，请先'), h('span', { class: ' c-primary cursor-pointer ', onClick: showBrandAdd }, '添加品牌')]),
    },
  },
  {
    type: 'cascader',
    label: () =>
      rowTip('商品类目', [
        h('div', null, '⚠️为确保您的商品获得精准流量和更高转化率，请务必根据商品真实属性选择正确类目。错误类目可能导致：'),
        h('div', null, '① 搜索降权：系统自动降低展示排名；'),
        h('div', null, '② 活动驳回：无法参与平台促销活动；'),
        h('div', null, '③ 违规风险：严重者将面临商品下架或店铺扣分。'),
      ]),
    span: 6,
    key: 'category_id',
    props: {
      options: [],
      placeholder: '请选择商品类目',
      fieldNames: { label: 'name', value: 'id' },
      allowClear: true,
    },
  },
  {
    type: 'input-number',
    label: () => rowTip('发货时效', [h('div', null, '请填写承诺的发货时间，单位“小时”（如：24），确保准确以免影响订单履约')]),
    span: 6,
    key: 'delivery_time',
    props: {
      maxlength: 9,
      placeholder: '从下单到发货的时间',
      showCount: true,
      onChange: () => {
        let val = form.value.delivery_time
        // 只允许数字
        val = String(val).replace(/[^0-9]/g, '')
        form.value.delivery_time = val
      },
      addonAfter: 'h(小时)',
    },
  },
  {
    type: 'input',
    label: () => rowTip('发货地', [h('div', null, '填写实际发货仓库所在省市（如：广东省深圳市），若多仓发货请用分号“;”分隔 （如：广东省深圳市；浙江省杭州市）')]),
    span: 6,
    key: 'delivery_location',
    props: {
      maxlength: 50,
      placeholder: '请输入发货地',
    },
  },
  {
    type: 'textarea',
    label: () => rowTip('商品卖点', [h('div', null, '请用3-5条短句描述核心优势，用分号“;”分隔。示例：新疆长绒棉|A类婴幼儿标准|无荧光剂')]),
    span: 6,
    key: 'selling_points',
    props: {
      class: 'w-full',
      placeholder: '请提炼3-5个商品核心卖点，吸引买家快速决策',
      maxlength: 50,
    },
  },
  {
    type: 'title',
    label: '申报采购价',
  },
  // {
  //   label: '选择填写价格类型',
  //   type: 'select',
  //   key: 'price_type',
  //   span: 6,
  //   props: {
  //     options: [
  //       { label: '含税单价', value: 2 },
  //       { label: '不含税单价', value: 1 },
  //     ],
  //     onChange: () => {
  //       if (form.value.price_type === 2) {
  //         basicFormRef.value?.clearValidate('declared_purchase_price')
  //       } else {
  //         basicFormRef.value?.clearValidate('declared_purchase_tax_price')
  //       }
  //       setSupplyPrice()
  //     },
  //   },
  // },
  // {
  //   type: 'input-number',
  //   label: () => rowTip('不含税单价', [h('div', null, '商品不含税费的价格。税率为0时，不含税单价=含税单价。')]),
  //   key: 'declared_purchase_price',
  //   span: 6,
  //   hidden: true,
  //   props: {
  //     placeholder: '请输入不含税单价',
  //     showCount: true,
  //     maxlength: 10,
  //     onChange: () => {
  //       if (form.value.price_type !== 1) return
  //       let val = form.value.declared_purchase_price
  //       // 只允许数字和小数点
  //       val = form.value.declared_purchase_price.replace(/[^\d.]/g, '')
  //       // 只允许出现一个小数点
  //       val = val.replace(/^(\d+)\.(\d*)\./, '$1.$2')
  //       // 小数点不能在首位
  //       if (val.startsWith('.')) val = ''
  //       // 保留两位小数
  //       val = val.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2')
  //       form.value.declared_purchase_price = val
  //       form.value.declared_purchase_tax_price = new Decimal(Number(val) || 0)
  //         .mul(new Decimal(1 + Number(form.value.declared_purchase_tax_rate || 0) / 100))
  //         .toDecimalPlaces(2)
  //         .toNumber()
  //     },
  //     addonAfter: 'CNY',
  //   },
  // },
  // {
  //   type: 'select',
  //   label: () => rowTip('税率', [h('div', null, '支持填写 0，零税率则 不含税单价=含税单价。')]),
  //   key: 'declared_purchase_tax_rate',
  //   span: 6,
  //   hidden: true,
  //   props: {
  //     options: [
  //       { label: '0%', value: '0' },
  //       { label: '3%', value: '3' },
  //       { label: '6%', value: '6' },
  //       { label: '9%', value: '9' },
  //       { label: '13%', value: '13' },
  //       { label: '16%', value: '16' },
  //       { label: '17%', value: '17' },
  //     ],
  //     placeholder: '零税率的增值税',
  //     onChange: () => {
  //       let val = form.value.declared_purchase_tax_rate.toString()
  //       // 只允许数字和小数点
  //       val = val.replace(/[^\d.]/g, '')
  //       // 只允许出现一个小数点
  //       val = val.replace(/\.(?=.*\.)/g, '')
  //       // 如果以小数点开头，在前面加0
  //       if (val.startsWith('.')) {
  //         val = `0${val}`
  //       }
  //       // 保留两位小数
  //       val = val.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2')
  //       // 限制范围0-1000
  //       if (Number(val) > 1000) val = '1000'
  //       if (Number(val) < 0) val = '0'
  //       form.value.declared_purchase_tax_rate = val
  //       if (form.value.price_type === 1) {
  //         // 重新计算含税单价
  //         form.value.declared_purchase_tax_price = new Decimal(Number(form.value.declared_purchase_price) || 0)
  //           .mul(new Decimal(1 + (Number(form.value.declared_purchase_tax_rate) || 0) / 100))
  //           .toDecimalPlaces(2)
  //           .toNumber()
  //       } else {
  //         form.value.declared_purchase_price = new Decimal(Number(form.value.declared_purchase_tax_price) || 0)
  //           .div(new Decimal(1 + (Number(form.value.declared_purchase_tax_rate) || 0) / 100))
  //           .toDecimalPlaces(2)
  //           .toNumber()
  //       }
  //     },
  //   },
  // },
  {
    type: 'a-input-number',
    label: '采购单价(含税)',
    key: 'declared_purchase_tax_price',
    span: 6,
    props: {
      addonAfter: 'CNY',
      controls: false,
      max: *********.99,
      precision: 2,
    },
  },
])

// 基础信息表单验证规则
const basicRules: Record<string, Rule[]> = {
  product_name: [
    {
      required: true,
      validator(_rule, value) {
        if (!value?.length) {
          return Promise.reject('请输入商品名称')
        }
        const reg = /^[\u4e00-\u9fa5a-zA-Z0-9 \-_/()·]*$/
        if (value && !reg.test(value)) {
          return Promise.reject('商品名称格式有误')
        }
        return Promise.resolve()
      },
    },
  ],
  product_name_en: [
    {
      required: true,
      validator(_rule, value) {
        if (!value?.length) {
          return Promise.reject('请输入商品名称')
        }
        const reg = /^[a-zA-Z0-9 \-_/()·]*$/
        if (value && !reg.test(value)) {
          return Promise.reject('商品名称格式有误')
        }
        return Promise.resolve()
      },
    },
  ],
  supplier_product_number: [{ required: true, message: '请输入供应商商品编码' }],
  product_type: [{ required: true, message: '请选择商品类型' }],
  brand_id: [{ required: true, message: '请选择商品品牌' }],
  category_id: [{ required: true, message: '请选择商品类目' }],
  delivery_time: [{ required: true, message: '请输入发货时效' }],
  delivery_location: [{ required: true, message: '请输入发货地' }],
  selling_points: [{ required: true, message: '请输入商品卖点' }],
  declared_purchase_price: [
    {
      required: true,
      message: '请输入不含税单价',
      validator: () => {
        if (form.value.price_type === 2) {
          return Promise.resolve()
        }
        if (!form.value.declared_purchase_price) {
          return Promise.reject('请输入不含税单价')
        }
        if (String(form.value.declared_purchase_price).length > 10) {
          return Promise.reject('不含税单价不能超过10位')
        }
        return Promise.resolve()
      },
    },
  ],
  price_type: [{ required: true, message: '请选择填写价格类型' }],
  declared_purchase_tax_rate: [{ required: true, message: '请输入税率' }],
  declared_purchase_tax_price: [{ required: true, message: '清输入采购单价(含税)' }],
}

// 获取商品类目选项
const getCategoryOption = async () => {
  const res: any = await GetCategoryOption()
  const recursiveModifyId = (data: any[]) => {
    data.forEach((item) => {
      if (item.id && item.category_template_id) {
        item.id = `${item.id}_${item.category_template_id}`
      }
      if (item.children?.length) {
        recursiveModifyId(item.children)
      }
    })
  }
  recursiveModifyId(res.data)
  setOption('category_id', res.data)
}

// 获取商品品牌选项
const getBrandOption = async () => {
  const [brandOption] = await getCommonOption([15])
  setOption('brand_id', brandOption)
}

const showBrandAdd = () => {
  brandRef.value.open('add')
}

const [BasicForm, basicFormRef, setOption] = useBaseForm({
  modelValue: form,
  formConfig: basicDrawerForm,
  rules: basicRules,
})

onMounted(() => {
  getCategoryOption()
  getBrandOption()
  // setSupplyPrice()
})

defineExpose({
  validate: (keys?: string[]) => basicFormRef.value?.validate(keys),
  clearValidate: () => basicFormRef.value?.clearValidate(),
})
</script>

<style scoped lang="scss"></style>
