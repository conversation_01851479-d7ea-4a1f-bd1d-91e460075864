<template>
  <div class="main">
    <!-- 过滤器 -->
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.NOTICE_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" :isCheckbox="false" :pageType="PageType.NOTICE_MANAGE" :getList="getListFn" v-model:form="formArr" :auto-search="false">
      <template #left-btn>
        <!-- <a-button id="noticeIncrease" type="primary" @click="increase" :disabled="!btnPermission[610001]">新建通知</a-button> -->
      </template>
      <template #right-btn>
        <!-- <a-button id="noticeBatchSend" @click="beforeBatchSend" :disabled="!btnPermission[61002]">批量发布</a-button> -->
      </template>
      <template #notice_status="{ row }">
        <span>{{ getStatusDisplay(row.notice_status) }}</span>
      </template>
      <template #scope="{ row }">
        <span>{{ scopeName(row.scope) || '--' }}</span>
      </template>
      <template #publish_at="{ row }">
        <span>{{ row.publish_at || '--' }}</span>
      </template>
      <template #scheduled_publish_at="{ row }">
        <span>{{ row.scheduled_publish_at || '--' }}</span>
      </template>
      <template #create_at="{ row }">
        <span>{{ row.create_at || '--' }}</span>
      </template>
      <template #update_at="{ row }">
        <span>{{ row.update_at || '--' }}</span>
      </template>
      <template #operate="{ row }">
        <a-button type="text" @click="detail(row)" :disabled="!btnPermission[610005]">查看</a-button>
        <!-- <a-button type="text"  @click="noticeWithdraw(row)"  :disabled="!canWithdraw(row.notice_status) || !btnPermission[610002]">撤回</a-button>
          <a-button type="text"  @click="beforeSend(row)"  :disabled="!canPublish(row.notice_status) || !btnPermission[610002]">发布</a-button>
          <a-button type="text"  @click="edit(row)"  :disabled="!canEdit(row.notice_status) || !btnPermission[610003]">编辑</a-button>
          <a-button type="text"  @click="del(row)"  :disabled="!canDelete(row.notice_status) || !btnPermission[610004]">删除</a-button> -->
      </template>
    </BaseTable>

    <!-- 确认弹窗 -->
    <a-modal :zIndex="10000" v-model:open="modalData.isShow" :title="modalData.title">
      <div class="modalContent">{{ modalData.content }}</div>
      <template #footer>
        <a-button v-if="modalData.isConfirmBtn" :danger="modalData.okType === 'danger'" type="primary" style="margin-right: 10px" @click="modalData.okFn">{{ modalData.confirmBtnText }}</a-button>
        <a-button v-if="modalData.isCancelBtn" @click="modalData.isShow = false">取消</a-button>
      </template>
    </a-modal>
    <!-- 新增 编辑 -->
    <a-drawer
      v-model:open="drawerVisible"
      @afterVisibleChange="formRef?.clearValidate()"
      width="50vw"
      :title="drawerStatus === 1 ? '新建通知' : '编辑通知'"
      placement="right"
      :maskClosable="false"
      :footer-style="{ textAlign: 'left' }"
    >
      <LoadingOutlined v-show="drawerLoading" class="loadingIcon" />
      <a-form v-if="!drawerLoading" ref="formRef" :model="formData">
        <a-form-item label="通知编码" v-if="drawerStatus == 2" name="code">
          <a-input id="code" disabled v-model:value="formData.code" placeholder="通知编码"></a-input>
        </a-form-item>

        <a-form-item
          label="通知标题"
          name="title"
          :rules="[
            { required: true, message: '请输入通知标题' },
            { max: 200, message: '输入内容不可超过200字符' },
          ]"
        >
          <a-input id="title" v-model:value="formData.title" placeholder="请输入通知标题"></a-input>
        </a-form-item>
        <a-form-item
          label="通知内容"
          name="content"
          :rules="[
            { required: true, message: '请输入通知内容' },
            { max: 5000, message: '输入内容不可超过5000字符' },
          ]"
        >
          <a-textarea id="content" v-model:value="formData.content" placeholder="请输入通知内容" :rows="4" />
        </a-form-item>
        <a-form-item label="通知类型" name="notice_type">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            id="notice_type"
            v-model:value="formData.notice_type"
            disabled
            class="w240"
            :options="noticeTypeOption"
            placeholder="请选择通知类型"
          ></a-select>
        </a-form-item>
        <a-form-item label="通知范围" name="scope">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            id="scope"
            v-model:value="formData.scope"
            disabled
            class="w240"
            :options="scopeOption"
            placeholder="请选择通知范围"
          ></a-select>
        </a-form-item>
        <div style="display: flex; gap: 12px; align-items: center">
          <a-form-item label="发布计划" name="publish_type" :rules="[{ required: true, message: '请选择发布计划' }]">
            <a-radio-group id="publishType" @change="formData.scheduled_publish_at = null" v-model:value="formData.publish_type">
              <a-radio :id="`publishType${item.value}`" v-for="(item, index) in publishTypeOption" :key="index" :value="item.value">{{ item.label }}</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="计划发布时间" name="scheduled_publish_at">
            <a-date-picker
              @change="checkTime"
              :disabled-date="disabledDate"
              id="planPublishTime"
              :disabled="formData.publish_type == 1"
              :valueFormat="'YYYY-MM-DD HH:mm'"
              format="YYYY-MM-DD HH:mm"
              :show-time="{ format: 'HH:mm' }"
              v-model:value="formData.scheduled_publish_at"
              placeholder="请选择计划发布时间"
            />
          </a-form-item>
        </div>
        <a-form-item label="过期时间" name="expired_at" :rules="[{ required: true, message: '请现在过期时间' }]">
          <a-date-picker
            :disabled-date="(current) => disabledDate2(current, formData.scheduled_publish_at)"
            id="expTime"
            class="w240"
            :valueFormat="'YYYY-MM-DD HH:mm'"
            format="YYYY-MM-DD HH:mm"
            :show-time="{ format: 'HH:mm' }"
            v-model:value="formData.expired_at"
            placeholder="请选择过期时间"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button
          id="noticeFormComfirmAndSend"
          style="margin-right: 10px"
          type="primary"
          @click="
            () => {
              formData.save_type = 2
              drawerComfirm()
            }
          "
        >
          保存并发布
        </a-button>
        <a-button
          id="noticeFormComfirmAndSave"
          style="margin-right: 10px"
          @click="
            () => {
              formData.save_type = 1
              drawerComfirm()
            }
          "
        >
          保存草稿
        </a-button>
        <a-button id="noticeFormCancel" @click="drawerVisible = false">取消</a-button>
      </template>
    </a-drawer>
    <!-- 查看 -->
    <detail-drawer ref="detailDrawerRef" />
  </div>
</template>
<script lang="ts" setup>
import { LoadingOutlined } from '@ant-design/icons-vue'
import { onMounted, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { BatchPublishNotify, deleteNotification, DetailByEdit, GetnoticeList, AddNotification, UpdateNotification, revocation, GetDropsSource } from '@/servers/Notice'
import { GetEnum } from '@/servers/Common'

import BaseTable from '@/components/BaseTable/index.vue'
import SearchForm from '@/components/SearchForm/index.vue'
import { PageType } from '@/common/enum'
import eventBus from '@/utils/eventBus'
import { usePermission } from '@/hook/usePermission'
import DetailDrawer from './components/DetailDrawer.vue'

const tableRef = ref()
const formRef = ref()

const noticeTypeOption = ref([{ label: '系统通知', value: 1 }])
const scopeOption = ref([{ label: '全员', value: 1 }])
const publishTypeOption = ref([
  { label: '立即发布', value: 1 },
  { label: '定时发布', value: 2 },
])

const { btnPermission } = usePermission()

const getListFn = ref(GetnoticeList)

const formArr: any = ref([
  // {
  //   label: '搜索编码',
  //   value: null,
  //   type: 'input',
  //   key: 'code',
  // },

  {
    label: '通知标题',
    value: null,
    type: 'input',
    key: 'title',
  },
  {
    label: '通知内容',
    value: null,
    type: 'input',
    key: 'content',
  },
  // {
  //   label: '状态',
  //   value: '30',
  //   key: 'notice_status_list',
  //   type: 'select',
  //   isShow: false,
  //   isQuicks: true,
  //   multiple: false,
  //   quickNotFull: true,
  //   selectArr: [],
  // },
  {
    label: '发布时间',
    value: null,
    type: 'range-picker',
    selectArr: [],
    key: 'publish_at',
    formKeys: ['publish_start_at', 'publish_end_at'],
    placeholder: ['发布开始时间', '发布结束时间'],
  },

  // {
  //   label: '创建时间',
  //   value: null,
  //   type: 'range-picker',
  //   selectArr: [],
  //   key: 'create_at',
  //   formKeys: ['create_start_at', 'create_end_at'],
  //   placeholder: ['创建开始时间', '创建结束时间'],
  // },
  // {
  //   label: '修改时间',
  //   value: null,
  //   type: 'range-picker',
  //   selectArr: [],
  //   key: 'update_at',
  //   formKeys: ['update_start_at', 'update_end_at'],
  //   placeholder: ['修改开始时间', '修改结束时间'],
  // },
])
// 确认框数据
const modalData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  okType: 'primary',
  title: '',
  content: '',
  okFn: () => {
    modalData.isShow = false
  },
})
// 查看
const detailDrawerRef = ref<any>(null)
// 新增 编辑
const drawerLoading = ref(false)
const drawerVisible = ref(false)
const drawerStatus = ref(1)
const formData = ref<any>({
  id: '',
  save_type: null,
  title: null,
  content: null,
  notice_type: null,
  scope: null,
  publish_type: null,
  scheduled_publish_at: null,
  expired_at: null,
})
const oldStatus = ref<any>(null)

const search = () => tableRef.value.search()

onMounted(() => {
  getSelect()
  search()
  // getEnum()
  initScreening()
})
onActivated(() => {})
const checkTime = () => {
  const time1 = formData.value.scheduled_publish_at
  const time2 = formData.value.expired_at
  if (time1 && time2) {
    if (time1 > time2) {
      message.error('计划发布时间不可在过期时间之后')
      formData.value.expired_at = null
    }
  }
}
const disabledDate = (current) => {
  if (!current) return false
  const currentDate = new Date(current)
  const now = new Date()
  now.setHours(0, 0, 0, 1)
  return currentDate.getTime() < now.getTime()
}

const disabledDate2 = (current, A = null) => {
  if (!current) return false
  // console.log(A)
  // 如果 A 存在，则解析为 Date，否则使用当前时间
  const referenceDate = A ? new Date(A) : new Date()
  referenceDate.setHours(0, 0, 0, 1) // 重置为当天的开始时间

  const currentDate = new Date(current)
  return currentDate.getTime() < referenceDate.getTime()
}

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.NOTICE_MANAGE) {
    const arr: any[] = []
    obj.NOTICE_MANAGE.forEach((x) => {
      formArr.value.forEach((y) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item) => {
      item.isShow = true
    })
  }
}

// 新增
const increase = () => {
  drawerLoading.value = false
  formData.value = {
    id: '',
    save_type: null,
    title: null,
    content: null,
    notice_type: 1,
    scope: 1,
    publish_type: 1,
    scheduled_publish_at: null,
    expired_at: null,
  }
  drawerStatus.value = 1
  drawerVisible.value = true
}
// 详情
const detail = (item) => {
  detailDrawerRef.value.open(item.id, btnPermission.value[61005])
}

// 获取下拉选项
const getSelect = () => {
  GetDropsSource()
    .then((res) => {
      console.log('GetDropsSource response:', res)
      // 根据新API文档，返回的数据结构包含 status, types, scopes, publish_types
      formArr.value.forEach((item) => {
        if (item.key === 'notice_status_list') {
          // 将状态对象转换为下拉选项数组
          if (res.data && res.data.status) {
            item.options = Object.entries(res.data.status).map(([key, value]) => ({
              label: value,
              value: key,
            }))
          }
        }
      })
    })
    .catch((error) => {
      console.warn('获取下拉选项失败:', error)
      // 设置默认状态选项
      formArr.value.forEach((item) => {
        if (item.key === 'notice_status_list') {
          item.options = [
            { label: '草稿', value: '草稿' },
            { label: '待发布', value: '待发布' },
            { label: '已发布', value: '已发布' },
          ]
        }
      })
    })
}

// 编辑
const edit = (item) => {
  getNotifyInfo(item.id)
  oldStatus.value = Number(item.status)
  console.log(oldStatus.value, 'oldStatus')

  drawerLoading.value = false
  drawerStatus.value = 2
  drawerVisible.value = true
}

const getNotifyInfo = async (id) => {
  // const res = await GetNotice(id)
  // const { title, content, publish_type, scheduled_publish_at, expired_at } = res.data
  // const newPublishType = publishTypeOption.value.find((x) => x.label === publish_type)?.value
  // formData.value = { ...formData.value, title, content, publish_type: newPublishType, scheduled_publish_at, expired_at }

  DetailByEdit({ id })
    .then((res) => {
      console.log('编辑通知数据:', res)
      const data = res.data

      // 处理通知范围：支持字符串和数字格式
      let scopeValue = 1 // 默认为全员
      if (data.scope === '全员' || data.scope === 1) {
        scopeValue = 1
      }

      // 处理发布类型：支持字符串和数字格式
      let publishTypeValue = 1 // 默认为立即发布
      if (data.publish_type === '立即发布' || data.publish_type === 1) {
        publishTypeValue = 1
      } else if (data.publish_type === '定时发布' || data.publish_type === 2) {
        publishTypeValue = 2
      }

      // 设置表单数据，确保必填字段有默认值
      formData.value = {
        ...data,
        // 确保通知类型为系统通知（固定值）
        notice_type: 1,
        // 确保通知范围有正确的值
        scope: scopeValue,
        // 确保发布类型有正确的值
        publish_type: publishTypeValue,
      }

      console.log('处理后的表单数据:', formData.value)
    })
    .catch((error) => {
      console.error('获取通知详情失败:', error)
    })
}
// 新增 编辑提交
const drawerComfirm = async () => {
  try {
    await formRef.value?.validateFields()
    const params = JSON.parse(JSON.stringify(formData.value))
    if (!params.id) {
      delete params.id
    }
    let postFunc: any = null
    if (formData.value.id) {
      postFunc = UpdateNotification
    } else {
      postFunc = AddNotification
    }
    postFunc(params)
      .then(() => {
        if (formData.value.save_type === 1) {
          eventBus.emit('getNotice')
        }
        message.success('成功')
        drawerVisible.value = false
        search()
      })
      .catch((err) => {
        console.log(2222, err)
      })
  } catch (errorInfo) {
    console.log('Failed:', errorInfo)
  }
}
// 删除
const del = (item) => {
  modalData.title = '删除通知'
  modalData.content = `此操作不可恢复，确定要删除该通知吗？`
  modalData.confirmBtnText = '确定'
  modalData.isCancelBtn = true
  modalData.okType = 'danger'
  modalData.okFn = () => {
    deleteNotification({ id: item.id })
      .then(() => {
        modalData.isShow = false
        message.success('删除成功')
        search()
      })
      .catch(() => {
        modalData.isShow = false
      })
  }
  modalData.isShow = true
}
// 批量发布
const batchSend = (obj, type) => {
  BatchPublishNotify(obj).then(() => {
    eventBus.emit('getNotice')
    message.success(`${type === 1 ? '' : '批量'}发布成功`)
    search()
    modalData.isShow = false
  })
}
// 批量发布
const beforeBatchSend = () => {
  if (tableRef.value.checkItemsArr.length === 0) {
    message.info('请勾选需要的通知')
  } else {
    const arr = tableRef.value.checkItemsArr.map((obj) => obj.id)
    batchSend({ notice_ids: arr }, 2)
  }
}
const beforeSend = (item) => {
  batchSend({ notice_ids: [item.id] }, 1)
}
const noticeWithdraw = (item) => {
  revocation({ id: item.id }).then(() => {
    message.success('撤回成功')
    search()
  })
}

const scopeName = (scope) => {
  return enumData.value?.notice?.scope?.filter((n) => n.value === scope)[0].label || '--'
}

// 状态显示函数 - 兼容新旧API的状态格式
const getStatusDisplay = (status) => {
  // 如果是字符串类型（新API），直接返回
  if (typeof status === 'string') {
    return status || '--'
  }
  // 如果是数字类型（旧API或兼容模式），转换为对应的字符串
  if (typeof status === 'number') {
    switch (status) {
      case 1:
        return '草稿'
      case 2:
        return '待发布'
      case 3:
        return '已发布'
      default:
        return '--'
    }
  }
  return '--'
}

// 状态判断函数 - 兼容新旧API的状态格式
const normalizeStatus = (status) => {
  if (typeof status === 'string') {
    switch (status) {
      case '草稿':
        return 1
      case '待发布':
        return 2
      case '已发布':
        return 3
      default:
        return 0
    }
  }
  return typeof status === 'number' ? status : 0
}

// 是否可以撤回 - 只有待发布状态可以撤回
const canWithdraw = (status) => {
  const normalizedStatus = normalizeStatus(status)
  return normalizedStatus === 2
}

// 是否可以发布 - 草稿和待发布状态可以发布
const canPublish = (status) => {
  const normalizedStatus = normalizeStatus(status)
  return normalizedStatus === 1 || normalizedStatus === 2
}

// 是否可以编辑 - 只有草稿状态可以编辑
const canEdit = (status) => {
  const normalizedStatus = normalizeStatus(status)
  return normalizedStatus === 1
}

// 是否可以删除 - 只有草稿状态可以删除
const canDelete = (status) => {
  const normalizedStatus = normalizeStatus(status)
  return normalizedStatus === 1
}

const enumData = ref<any>([])
// 获取所有枚举选项
const getEnum = () => {
  GetEnum()
    .then((res) => {
      enumData.value = res.data
      formArr.value.forEach((item) => {
        if (item.key == 'notice_status_list') {
          item.selectArr = enumData.value?.notice?.notice_status || []
        }
        if (item.key == 'scope') {
          item.selectArr = enumData.value?.notice?.scope || []
        }
      })
    })
    .catch((error) => {
      console.warn('获取枚举选项失败:', error)
      // 设置默认值
      formArr.value.forEach((item) => {
        if (item.key == 'notice_status_list') {
          item.selectArr = []
        }
        if (item.key == 'scope') {
          item.selectArr = []
        }
      })
    })
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .breadcrumbBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    margin: 8px 0;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #000;
    }
  }

  .btnlist {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-top: 15px;

    .batchBtn {
      margin-left: 10px;
    }
  }

  .formBox {
    .operatingAreaBox {
      display: flex;
      align-items: center;
      height: 32px;

      .tag {
        padding: 0 10px;
        cursor: pointer;
        user-select: none;
      }

      .btn {
        margin-right: 10px;
      }
    }
  }

  .btnBox {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 120px;
    min-width: 120px;
    margin-right: 30px;

    label {
      font-size: 12px;

      &::after {
        display: none !important;
      }
    }
  }
}

.w240 {
  width: 240px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: rgb(0 0 0 / 50%);
  white-space: nowrap;
}

.loadingIcon {
  font-size: 30px;
  color: #1890ff;
}

.TagFilter {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;

  .item {
    margin-right: 20px;
  }
}
</style>
