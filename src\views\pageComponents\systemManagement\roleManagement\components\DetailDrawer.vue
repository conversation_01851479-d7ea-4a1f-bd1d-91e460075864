<template>
  <a-drawer
    :footer="logVisble ? undefined : false"
    v-model:open="detailVisible"
    :width="appStore.isOpenLog ? '91vw' : '45vw'"
    title="查看角色"
    placement="right"
    :maskClosable="false"
    :footer-style="{ textAlign: 'left' }"
    :bodyStyle="{ padding: '0' }"
  >
    <template #extra v-if="logVisble">
      <a-button @click="changeLogVisible">日志</a-button>
    </template>
    <div class="detailAllBox">
      <LoadingOutlined v-show="detailloading" class="loadingIcon" />
      <a-form v-if="!detailloading && target">
        <a-collapse v-model:activeKey="activeKey" :bordered="true" expandIconPosition="end" ghost>
          <template #expandIcon="{ isActive }">
            <DoubleRightOutlined :rotate="isActive ? 270 : 90" />
          </template>

          <a-collapse-panel key="1" header="角色" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">角色名称</p>
                <p class="value">{{ target.role_name }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">使用范围</p>
                <p class="value">{{ target.scope == 1 ? '内部' : '外部' }}联系人</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">状态</p>
                <p class="value">{{ target.status == 0 ? '停用' : '启用' }}</p>
              </a-col>
            </a-row>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="其他" :style="customStyle">
            <a-row :gutter="16">
              <a-col class="gutter-row" :span="8">
                <p class="label">创建时间</p>
                <p class="value">{{ target.create_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">创建人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target.create_user_real_name }}</div>
                      <div v-show="target.create_user_scope == 1">所属公司：{{ target.create_user_company_name ? target.create_user_company_name : '--' }}</div>
                      <div v-show="target.create_user_scope != 1">所属客户：{{ target.create_user_customer_name ? target.create_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target.create_user_department ? target.create_user_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ target.create_user_jobtitlename ? target.create_user_jobtitlename : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target.create_user_real_name ? target.create_user_real_name : '--' }}</span>
                      <span v-if="target.create_user_department || target.create_user_jobtitlename" class="detailValueDescription">
                        （
                        <span v-if="target.create_user_jobtitlename">{{ target.create_user_jobtitlename }}&nbsp;|&nbsp;</span>
                        <span v-if="target.create_user_department">
                          {{ target.create_user_department.length > 10 ? target.create_user_department.slice(0, 10) + '...' : target.create_user_department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改时间</p>
                <p class="value">{{ target.update_at }}</p>
              </a-col>
              <a-col class="gutter-row" :span="8">
                <p class="label">最后修改人</p>
                <p class="value">
                  <a-tooltip :overlayStyle="{ maxWidth: 'none' }">
                    <template #title>
                      <div>用户名称：{{ target.update_user_real_name }}</div>
                      <div v-show="target.update_user_scope == 1">所属公司：{{ target.update_user_company_name ? target.update_user_company_name : '--' }}</div>
                      <div v-show="target.update_user_scope != 1">所属客户：{{ target.update_user_customer_name ? target.update_user_customer_name : '--' }}</div>
                      <div>所在部门：{{ target.update_user_department ? target.update_user_department : '--' }}</div>
                      <div>
                        岗
                        <span style="visibility: hidden">占位</span>
                        位：{{ target.update_user_jobtitlename ? target.update_user_jobtitlename : '--' }}
                      </div>
                    </template>
                    <div style="display: flex; align-items: center">
                      <span class="detailValue">{{ target.update_user_real_name ? target.update_user_real_name : '--' }}</span>
                      <span v-if="target.update_user_department || target.update_user_jobtitlename" class="detailValueDescription">
                        （
                        <span v-if="target.update_user_jobtitlename">{{ target.update_user_jobtitlename }}&nbsp;|&nbsp;</span>
                        <span v-if="target.update_user_department">
                          {{ target.update_user_department.length > 10 ? target.update_user_department.slice(0, 10) + '...' : target.update_user_department }}
                        </span>
                        ）
                      </span>
                    </div>
                  </a-tooltip>
                </p>
              </a-col>
            </a-row>
          </a-collapse-panel>
        </a-collapse>
      </a-form>
      <log-drawer ref="logDrawerRef" class="log" v-if="!detailloading && appStore.isOpenLog" />
    </div>
  </a-drawer>
  <!-- 日志 -->
</template>

<script lang="ts" setup>
import { Detail } from '@/servers/RoleNew'
import { LoadingOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'
import useAppStore from '@/store/modules/app'
import LogDrawer from './LogDrawer.vue'

const activeKey = ref(['1', '2'])
const customStyle = 'overflow: hidden; border: .0625rem solid #eee; margin-bottom: 1.25rem;'

const appStore = useAppStore()
const logDrawerRef = ref()
const detailVisible = ref(false)
const detailloading = ref(false)
const logVisble = ref(false)

const target = ref<any>(null)
const open = (id, boolean) => {
  target.value = null
  detailloading.value = true
  detailVisible.value = true
  logVisble.value = boolean
  console.log('boolean', boolean)
  Detail(id)
    .then((res) => {
      target.value = res.data
      detailloading.value = false
      nextTick(() => {
        logDrawerRef.value?.open(target.value)
      })
    })
    .catch(() => {
      detailloading.value = false
    })
}
const changeLogVisible = () => {
  appStore.changeLogOpenVisible(appStore.isOpenLog ? 0 : 1)
  if (appStore.isOpenLog) {
    nextTick(() => {
      logDrawerRef.value?.open(target.value)
    })
  }
}
// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-collapse-header) {
  background-color: #f4f7fe;
}

::v-deep(.ant-form-item) {
  align-items: flex-start;

  .ant-form-item-label {
    display: flex;
    justify-content: flex-end;
    width: 110px;
    min-width: 110px;
    margin-right: 30px;

    label {
      &::after {
        display: none !important;
      }
    }
  }
}

.detailTitle {
  padding-left: 12px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #000;
}

.w350 {
  width: 350px;
}

.w250 {
  width: 250px;
}

.w150 {
  width: 150px;
}

.w200 {
  width: 200px;
}

.description {
  padding-left: 20px;
  font-size: 12px;
  color: #00000080;
  white-space: nowrap;
}

.detailValueDescription {
  font-size: 12px;
  color: #00000080;
}

.detailBox {
  padding-top: 12px;

  .loadingIcon {
    font-size: 30px;
    color: #1890ff;
  }
}
</style>
