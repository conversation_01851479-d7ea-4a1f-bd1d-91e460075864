<template>
  <div class="flex flex-col w-full">
    <div class="w-full p-4 hover:bg-#f1f3fb flex justify-between items-center hover:c-primary b-rounded-4" @click.stop>
      <div>
        <FileTextFilled class="c-#3c82fe" v-show="file?.type?.indexOf('text') != -1" />
        <FileImageFilled class="c-#f6ad00" v-show="file?.type?.indexOf('image') != -1" />
        <FilePdfFilled class="c-#fe5359" v-show="file?.type?.indexOf('pdf') != -1" />
        <span class="ml-4 hover:c-primary truncate">{{ file.name }}</span>
      </div>
      <DeleteOutlined class="c-#fe5359" v-show="file.status === 'done' && !disabled" @click.stop="handleDelete" />
    </div>
    <a-progress :percent="30" :show-info="false" v-show="file.status === 'uploading'" />
  </div>
</template>

<script setup lang="ts">
import { FileTextFilled, FileImageFilled, FilePdfFilled, DeleteOutlined } from '@ant-design/icons-vue'

defineProps<{
  file: any
  disabled?: boolean
}>()

const emit = defineEmits(['delete', 'preview'])

const handleDelete = () => {
  emit('delete')
}
</script>
<style scoped lang="scss"></style>
