<template>
  <div class="w-full b-1px b-#eaecee b-solid">
    <div class="flex justify-between items-center p-8 bg-#f8f8f9 b-1px b-b-solid b-#eaecee">
      <span>
        <span class="c-#999">协议到货时间：</span>
        <span class="c-#333">{{ info.predict_delivery_date ? dayjs(info.predict_delivery_date).format('YYYY-MM-DD') : '--' }}</span>
      </span>
      <BaseBadge v-if="countSuccessStatus" label="已完成入库" type="success" />
    </div>
    <div class="p-8 flex w-full flex-1">
      <BaseImage :width="96" :height="96" :src="info.image_url"></BaseImage>
      <div class="flex-1 ml-8">
        <div class="text-14px flex justify-between items-center c-#333 flex mb-10">
          <div class="flex-1 relative h-18px">
            <div class="absolute truncate w-full top-0 font-500">{{ info.sku_name }}</div>
          </div>
          <div>
            <span class="text-12px mr-32px">x{{ info.purchase_quantity || 0 }}</span>
            <span>¥{{ formatPrice(info.purchase_tax_price) }}</span>
            <span class="c-#999">(含税)</span>
          </div>
        </div>
        <div class="flex justify-between c-#333">
          <div class="flex">
            <div class="item-text c-#999">
              <span>类目：</span>
              <span>规格：</span>
            </div>
            <div class="item-text c-#333">
              <span>{{ info.all_category || '--' }}</span>
              <span>{{ info.type_specification || '--' }}</span>
            </div>
          </div>
          <div class="flex">
            <div class="item-text c-#999">
              <span>供应商商品编码：</span>
              <span>平台商品编码：</span>
            </div>
            <div class="item-text c-#333">
              <span>{{ info.srs_supplier_prod_code || '--' }}</span>
              <span>{{ info.srs_platform_prod_code || '--' }}</span>
            </div>
          </div>
          <div class="flex">
            <div class="item-text c-#999">
              <span>预约入库数：</span>
              <span>采购入库数：</span>
              <span>退库数：</span>
            </div>
            <div class="item-text c-#333 text-right">
              <span>{{ info.total_scheduled_quantity || '0' }}</span>
              <span>{{ info.purchase_inbound_quantity || '0' }}</span>
              <span>{{ info.actual_return_quantity || '0' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseBadge from '@/components/BaseBadge/index.vue'
import BaseImage from '@/components/BaseImage/index.vue'
import { formatPrice } from '@/utils'
import dayjs from 'dayjs'
import Decimal from 'decimal.js'

const props = defineProps<{
  info: any
}>()

// 采购数量 = 采购入库数 - 退库数，则入库状态标记为已完成
const countSuccessStatus = computed(() => new Decimal(props.info.purchase_inbound_quantity).minus(props.info.actual_return_quantity).toNumber() === props.info.purchase_quantity)
</script>

<style scoped lang="scss">
.item-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  span {
    line-height: 20px;
  }
}
</style>
