<template>
  <a-drawer :headerStyle="{ paddingTop: '0', paddingBottom: '0' }" :destroyOnClose="true" :maskClosable="false" :closable="false" v-model:open="visible" width="40vw" @close="handleCancel">
    <template #title>
      <div class="flex items-center">
        <a-button size="small" @click="handleCancel" type="text" :icon="h(CloseOutlined)" class="mr-12" />
        <a-tabs v-model:activeKey="activeKey" @change="handleChangeTab">
          <a-tab-pane key="1" tab="搜索条件" />
          <a-tab-pane key="2" tab="列表字段" v-if="pageType" />
          <a-tab-pane key="3" tab="列表排序" v-if="pageType" />
        </a-tabs>
      </div>
    </template>
    <div v-show="activeKey === '1'" class="text-#333">
      <div class="mb-4 text-#666">请选择搜索条件，拖动可进行排序</div>
      <div class="setting-header">
        <div class="setting-row setting-search">
          <div v-for="i in searchColumns" :key="i">{{ i }}</div>
        </div>
      </div>
      <div :id="`${tableKey}search`" class="setting-body">
        <div v-for="item in convertnvertFormArr" :key="item.key" class="setting-row setting-search">
          <div class="drag"><HolderOutlined /></div>
          <div>{{ item.label }}</div>
          <div>
            <a-checkbox :checked="item.isShow !== false" @change="handleChangeShowStatus(item)" />
          </div>
        </div>
      </div>
    </div>
    <div v-show="activeKey === '2'" class="text-#333">
      <a-input v-model:value="scrollFilterText" placeholder="输入查找项" @change="changeFilterText" class="w-full mb-4">
        <template #suffix>
          <SearchOutlined style="color: #c0c0c0" />
        </template>
      </a-input>
      <div class="setting-header">
        <div class="setting-row setting-table">
          <div v-for="i in tableColumns" :key="i">{{ i }}</div>
        </div>
      </div>
      <div :id="`${tableKey}table`" class="setting-body">
        <div v-for="(item, index) in convertnvertColumns" :key="item.key" :id="`item${index}`" class="setting-row setting-table">
          <div class="drag"><HolderOutlined /></div>
          <div>{{ item.name }}</div>
          <div>
            <a-input-number v-model:value="item.width" :min="0" />
          </div>
          <div>
            <a-radio-group v-model:value="item.freeze" button-style="solid">
              <a-radio-button :value="1">左侧</a-radio-button>
              <a-radio-button :value="0">不设置</a-radio-button>
              <a-radio-button :value="2">右侧</a-radio-button>
            </a-radio-group>
          </div>
          <div>
            <a-checkbox v-model:checked="item.is_show" :disabled="item.name === '操作'" />
          </div>
        </div>
      </div>
    </div>

    <div v-show="activeKey === '3'" class="text-#333">
      <div class="setting-header">
        <a-button type="link" @click="onAddSort" class="add-btn">添加排序字段</a-button>
      </div>
      <div :id="`${tableKey}Sort`" class="setting-body">
        <div v-for="(item, index) in convertnvertSorts" :key="item.sortField" :id="`item${index}`" class="setting-row setting-table">
          <div class="drag"><HolderOutlined /></div>
          <a-select :getPopupContainer="(triggerNode) => triggerNode.parentNode" ref="select" v-model:value="item.sortField">
            <a-select-option
              v-for="(selItem, idx) in convertnvertColumns.filter((n) => n.name != '操作' && n.is_sort)"
              :key="idx"
              :value="selItem.key"
              :disabled="convertnvertSorts.map((n) => n.sortField).includes(selItem.key) || !selItem.is_show"
            >
              {{ selItem.name }}
            </a-select-option>
          </a-select>
          <div style="width: 6.25vw">
            <a-radio-group v-model:value="item.sortType" button-style="solid">
              <a-radio-button :value="'ASC'">正序</a-radio-button>
              <a-radio-button :value="'DESC'">倒序</a-radio-button>
            </a-radio-group>
          </div>
          <DeleteOutlined class="delete-icon" @click="onDeleteSort(index)" />
        </div>
      </div>
    </div>

    <template #footer>
      <a-space :size="12">
        <a-button type="primary" @click="handleSave">保存</a-button>
        <a-button @click="handleCancel">取消</a-button>
        <a-button v-if="activeKey != '3'" @click="handleReset">恢复默认</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script lang="ts" setup>
import { h } from 'vue'
import { useRoute } from 'vue-router'
import { HolderOutlined, CloseOutlined, SearchOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { PageType } from '@/common/enum'
import { pageTableConfig } from '@/common/pageTableConfig'
import Sortable from 'sortablejs'
import { cloneDeep, setTableConfig } from '@/utils'

const emits = defineEmits(['save', 'reset', 'search'])

const props = defineProps<{
  pageType?: PageType
}>()

const route = useRoute()
// 原始搜索数据
const formArr = defineModel<any[]>('formArr')
// 原始表头数据
const columns = defineModel<any[]>('columns')

const convertnvertColumns = ref<any[]>([])
const convertnvertSorts = ref<any[]>([])

const visible = defineModel('visible')

// 搜索是否初始化
const conditionInit = ref(false)
// 列表是否初始化
const tableInit = ref(false)
// 初始数据
const defaultFormArr = ref<any[]>([])
// 用户拖拽后的数据
const convertnvertFormArr = ref<any[]>([])

const activeKey = ref('1')
// 搜索条件
const searchColumns = ref(['排序', '搜索条件', '显示'])

const tableColumns = ref(['排序', '列表表头', '列宽(像素)', '冻结列', '显示'])

const tableKey = route.path.split('/').pop()

const scrollFilterText = ref('')
// 开启拖拽
const openSearchDrag = () => {
  const el = document.getElementById(`${tableKey}search`)
  new Sortable(el, {
    animation: 300,
    handle: '.drag',
    delay: 10,
    group: 'shared',
    onEnd: (item) => {
      const { oldIndex, newIndex } = item
      const arr: any = [...convertnvertFormArr.value]
      const currRow = arr.splice(oldIndex, 1)[0]
      arr.splice(newIndex, 0, currRow)
      convertnvertFormArr.value = arr
    },
  })
}

const openTableDrag = () => {
  const el = document.getElementById(`${tableKey}table`)
  new Sortable(el, {
    animation: 300,
    handle: '.drag',
    delay: 10,
    group: 'shared',
    onEnd: (item) => {
      const { oldIndex, newIndex } = item
      const currRow = convertnvertColumns.value.splice(oldIndex, 1)[0]
      const arr = cloneDeep(convertnvertColumns.value)
      convertnvertColumns.value = []
      arr.splice(newIndex, 0, currRow)
      convertnvertColumns.value = arr
    },
  })
}

const openSortDrag = () => {
  const el = document.getElementById(`${tableKey}Sort`)
  new Sortable(el, {
    animation: 300,
    handle: '.drag',
    delay: 10,
    group: 'shared',
    onEnd: (item) => {
      const { oldIndex, newIndex } = item
      const [movedItem] = convertnvertSorts.value.splice(oldIndex, 1)
      convertnvertSorts.value.splice(newIndex, 0, movedItem)
    },
  })
}

// 改变显示状态
const handleChangeShowStatus = (item: any) => {
  item.isShow = item.isShow === false
}
const saveFormArrInStorage = () => {
  const name: any = useRoute().path

  // for (const key in PageType) {
  //   if (Number(PageType[key]) === Number(props.pageType)) {
  //     name = key
  //   }
  // }
  const arr: any = []
  formArr.value?.forEach((x) => {
    arr.push({ key: x.key, isShow: x.isShow })
  })
  const obj: any = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  obj[name] = arr
  localStorage.setItem('screeningObj', JSON.stringify(obj))
}
// 保存
const handleSave = () => {
  if (activeKey.value === '1') {
    formArr.value = convertnvertFormArr.value
    nextTick(() => {
      formArr.value?.forEach((x) => {
        if (!x.isShow) {
          x.value = null
        }
      })
      saveFormArrInStorage()
      emits('search')
    })
    handleCancel()
  } else if (activeKey.value === '2') {
    getConvertnvertSorts(true) // 因为可能隐藏了字段所以需要重新获取排序数据
    emits('save', cloneDeep(convertnvertColumns.value.map((i, index) => ({ ...i, index }))))
    handleCancel()
  } else if (activeKey.value === '3') {
    let index = -1
    convertnvertColumns.value.forEach((item) => {
      index = convertnvertSorts.value.findIndex((i) => i.sortField === item.key)
      if (index !== -1) {
        item.sortIndex = index
        item.serverIsSort = true
        item.sortType = convertnvertSorts.value[index].sortType
      } else {
        item.serverIsSort = false
        item.sortType = ''
      }
    })
    emits('save', cloneDeep(convertnvertColumns.value.map((i, index) => ({ ...i, index }))))
    handleCancel()
  }
}
// 恢复默认
const handleReset = async () => {
  if (activeKey.value === '1') {
    formArr.value = cloneDeep(defaultFormArr.value).map((x) => {
      return { ...x, value: x.multiple ? [] : null, isShow: true }
    })
    nextTick(() => {
      saveFormArrInStorage()
      emits('reset')
    })
  } else if (activeKey.value === '2') {
    // 表格列设置：直接使用pageTableConfig中的默认配置
    if (props.pageType && pageTableConfig[props.pageType]) {
      const defaultConfig = cloneDeep(pageTableConfig[props.pageType])
      // 为每个配置项添加index属性
      defaultConfig.forEach((item: any, index: number) => {
        item.index = index + 1
      })
      // 更新columns
      columns.value = defaultConfig
      // 保存到服务器
      await setTableConfig(defaultConfig, props.pageType, 1) // 使用默认行高
      // 触发reset事件让BaseTable重新初始化
      emits('reset')
    }
  }
  handleCancel()
}
// 改变tab
const handleChangeTab = () => {
  if (activeKey.value === '2') {
    nextTick(openTableDrag)
    if (!tableInit.value) tableInit.value = true
  } else if (activeKey.value === '3') {
    nextTick(openSortDrag)
  }
}
// 取消
const handleCancel = () => {
  visible.value = false
  activeKey.value = '1'
  scrollFilterText.value = ''
}

const changeFilterText = () => {
  const filterText = scrollFilterText.value.trim()
  if (!filterText.length) return
  const index = convertnvertColumns.value.findIndex((i) => i.name && i.name.indexOf(filterText) != -1)
  if (index !== -1) {
    tapGo(index)
  }
}
const tapGo = (index) => {
  document.getElementById(`item${index}`)?.scrollIntoView({ behavior: 'smooth' })
}

const onAddSort = () => {
  convertnvertSorts.value.push({ sortField: '', sortType: 'ASC' })
}
const onDeleteSort = (index: number) => {
  convertnvertSorts.value.splice(index, 1)
}

const getConvertnvertSorts = (isChangeColumns: boolean) => {
  if (convertnvertColumns.value) {
    if (isChangeColumns) {
      convertnvertColumns.value.forEach((item) => {
        item.serverIsSort = item.is_show ? item.serverIsSort : false
      })
    }
    const arr = cloneDeep(convertnvertColumns.value)
    if (isChangeColumns) {
      const sortColumnsShow = arr.filter((n) => n.serverIsSort)
      convertnvertSorts.value.forEach((item, i) => {
        const index = sortColumnsShow.findIndex((n) => n.key === item.sortField)
        if (index === -1) {
          convertnvertSorts.value.splice(i, 1)
        }
      })
    } else {
      convertnvertSorts.value = arr
        .filter((n) => n.serverIsSort)
        .map((m) => {
          return {
            sortField: m.key,
            sortType: m.sortType,
            sortIndex: m.sortIndex,
          }
        })
      convertnvertSorts.value = convertnvertSorts.value.filter((n) => n.sortField)
      convertnvertSorts.value.sort((a, b) => {
        return a.sortIndex - b.sortIndex
      })
    }
  }
}

watch(visible, () => {
  if (visible.value) {
    if (formArr.value) convertnvertFormArr.value = cloneDeep(formArr.value)
    if (columns.value) {
      convertnvertColumns.value = cloneDeep(columns.value)
    }
    getConvertnvertSorts(false)
    if (!conditionInit.value) {
      defaultFormArr.value = cloneDeep(formArr.value)
      conditionInit.value = true
    }
    nextTick(() => {
      openSearchDrag()
    })
  }
})
</script>
<style scoped lang="scss">
.setting {
  display: flex;
  gap: 0.8333vw;
  align-items: center;
  padding: 0.3125vw 0.625vw;
  margin-bottom: 0.625vw;

  &-header {
    .setting-row {
      height: 1.6667vw;
      margin-bottom: 0;
      color: #999;

      // background-color: #fafafa;
    }
  }

  &-body {
    .setting-row {
      background-color: #f7f8fa;
    }
  }

  &-row {
    display: flex;
    gap: 0.8333vw;
    align-items: center;
    height: 2.0833vw;
    padding-inline: 0.625vw;
    margin-bottom: 0.625vw;

    .drag {
      cursor: pointer;
    }
  }

  &-search {
    & > div {
      width: 1.6667vw;

      &:nth-child(2) {
        flex: 1;
        text-align: left;
      }
    }
  }

  &-table {
    & > div {
      display: flex;
      align-items: center;
      width: 1.6667vw;
      text-align: center;

      &:nth-child(2) {
        flex: 1;
        text-align: left;
      }

      &:nth-child(3) {
        width: 5.2083vw;
      }

      &:nth-child(4) {
        width: 10.9375vw;
      }
    }
  }
}

.ant-tabs {
  width: 100%;

  ::v-deep(.ant-tabs-nav) {
    height: 2.7083vw;
    margin-bottom: 0;

    &::before {
      border: none;
    }
  }
}

.delete-icon {
  color: red;
  cursor: pointer;
}

.add-btn {
  display: block;
  padding: 0 !important;
  margin-bottom: 0.4167vw;
  margin-left: auto;
}
</style>
