<template>
  <a-drawer v-model:open="openDrawer" placement="right" :width="500" :maskClosable="false" title="新建用户" @after-open-change="afterOpenChange" @close="closeDrawer">
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <a-form-item label="姓名" name="real_name">
        <a-input placeholder="请输入姓名" v-model:value="formState.real_name" :maxlength="20" @input="handleInputChange" />
      </a-form-item>

      <a-form-item label="注册方式" name="type">
        <a-radio-group v-model:value="formState.type" @change="handleRegistrationTypeChange">
          <a-radio :value="1">手机号</a-radio>
          <a-radio :value="2">邮箱</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 手机号注册 -->
      <template v-if="formState.type === 1">
        <a-form-item label="手机号码" name="account">
          <a-input-group compact>
            <a-select
              v-model:value="formState.area_code"
              :options="countryCodeOptions"
              showSearch
              :filter-option="filterCountryOption"
              @change="handleCountryCodeChange"
              style="width: 100px"
              placeholder="+86"
              disabled
            />
            <a-input v-model:value="formState.account" placeholder="请输入手机号码" :maxlength="30" @input="handlePhoneInput" style="width: calc(100% - 120px)" />
          </a-input-group>
        </a-form-item>
      </template>

      <!-- 邮箱注册 -->
      <template v-if="formState.type === 2">
        <a-form-item label="邮箱" name="account">
          <a-input placeholder="请输入邮箱号码" v-model:value="formState.account" :maxlength="100" @input="handleEmailInput" />
        </a-form-item>
      </template>

      <!-- <a-form-item label="所属企业" name="company_id">
        <a-input placeholder="所属企业" v-model:value="currentCompanyName" readonly disabled style="cursor: not-allowed; background-color: #f5f5f5" />
      </a-form-item> -->

      <a-form-item label="所属部门" name="department_ids">
        <a-select
          showArrow
          mode="multiple"
          placeholder="请选择所属部门"
          v-model:value="formState.department_ids"
          :options="departmentOption"
          @click.stop="openDepartmentSelect"
          @deselect="handleDepartmentDeselect"
          :open="false"
        >
          <template #tagRender="{ label, closable, onClose, value }">
            <a-tag class="dragTag" :closable="closable" style="display: flex; align-items: center; margin: 2px; font-size: 12px; cursor: pointer" :key="value" @close="onClose">
              <span>{{ label }}</span>
              <!-- <span class="ml-2" v-if="option?.index == 0">主部门</span> -->
            </a-tag>
          </template>
        </a-select>
      </a-form-item>

      <a-form-item label="直属上级" name="leader_id">
        <a-select
          showArrow
          allowClear
          showSearch
          optionFilterProp="account_name"
          placeholder="请选择直属上级"
          v-model:value="formState.leader_id"
          :options="leaderOption"
          :field-names="{ label: 'account_name', value: 'id' }"
          @search="handleLeaderSearch"
          :filter-option="false"
        />
      </a-form-item>

      <a-form-item label="系统角色" name="role_ids">
        <a-select
          showArrow
          showSearch
          optionFilterProp="label"
          placeholder="请输入角色名称模糊检索"
          v-model:value="formState.role_ids"
          :options="roleOption"
          @search="handleRoleSearch"
          :filter-option="false"
        />
      </a-form-item>
    </a-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="gap-12px flex">
        <a-button :loading="submitLoading" type="primary" @click="handleSave">保存</a-button>
        <a-button :loading="submitLoading" @click="closeDrawer">取消</a-button>
      </div>
    </template>
  </a-drawer>

  <select-depart v-if="showSelectDepart" ref="selectDepartRef" @change="handleDepartmentChange" :tree-data="departmentTreeData" @close="showSelectDepart = false" />

  <!-- <account-invite-modal ref="accountInviteRef" /> -->
</template>

<script setup lang="ts">
import { ref, computed, h, getCurrentInstance, nextTick, onMounted } from 'vue'
import { cloneDeep, createLoadingDebounce } from '@/utils'
import Sortable from 'sortablejs'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { AddAccount, GetAccountSelectOption } from '@/servers/UserManager'
import { GetRoleSelectOption } from '@/servers/RoleNew'
import { GetCompanyTree, getCompanyIdFromUserData } from '@/servers/CompanyArchitecture'
// import { GetList as GetCountryList } from '@/servers/CountryRegion'
import SelectDepart from './SelectDepart.vue'

const { proxy }: any = getCurrentInstance()

const emit = defineEmits(['query', 'close'])
const openDrawer = ref(false)

const selectDepartRef = ref()
const submitLoading = ref(false)

// const accountInviteEl = useTemplateRef<typeof AccountInviteModal>('accountInviteRef')

const formRef = ref<FormInstance>()
// 表单数据
const formState = ref<any>({
  real_name: '',
  type: 1, // 默认手机号注册
  account: '',
  area_code: '+86',
  leader_id: null,
  company_id: '',
  role_ids: '',
  companyBinds: [],
  scope: 1, // 内部联系人
  department: [],
  department_ids: [],
})

// 部门树数据
const departmentTreeData = ref<any[]>([])
// 企业下拉框
const enterpriseOption = ref<any[]>([])
// 部门下拉框
const departmentOption = ref<any[]>([])
// 直接上级下拉框
const leaderOption = ref<any[]>([])
// 系统角色下拉框
const roleOption = ref<any[]>([])
// 国家码下拉框
const countryCodeOptions = ref<any[]>([])
// 当前企业名称（用于显示）
const currentCompanyName = ref('')

// 防抖定时器
let searchTimeout: any = null

const showSelectDepart = ref(false)

// 表单验证规则（响应式）
const rules = computed(() => {
  return {
    real_name: [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      { max: 30, message: '姓名不能超过30个字符', trigger: 'blur' },
      {
        validator: (_, value) => {
          if (!value) return Promise.resolve()
          // 去除空格后检查
          const trimmedValue = value.trim()
          if (trimmedValue !== value) {
            return Promise.reject('姓名不能包含空格')
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ],
    type: [{ required: true, message: '请选择注册方式', trigger: 'change' }],
    account: [
      { required: true, message: formState.value.type === 1 ? '请输入手机号码' : '请输入邮箱', trigger: 'blur' },
      {
        validator: (_, value) => {
          if (!value) return Promise.resolve()

          // 去除空格
          const trimmedValue = value.trim()
          if (trimmedValue !== value) {
            return Promise.reject('不能包含空格')
          }

          if (formState.value.type === 1) {
            // 手机号验证
            if (!/^\d+$/.test(value)) {
              return Promise.reject('手机号只能包含数字')
            }
            if (value.length > 30) {
              return Promise.reject('手机号不能超过30个字符')
            }
            // 检查是否选择了国家码
            if (!formState.value.area_code) {
              return Promise.reject('请选择手机国家码')
            }
            // 中国手机号前缀验证
            if (formState.value.area_code === '+86') {
              if (!/^(13|14|15|16|17|18|19)/.test(value)) {
                return Promise.reject('中国手机号前2位需为13、14、15、16、17、18、19')
              }
            }
          } else {
            // 邮箱验证
            if (value.length > 100) {
              return Promise.reject('邮箱不能超过100个字符')
            }
            if (/[\u4e00-\u9fa5]/.test(value)) {
              return Promise.reject('邮箱不能包含中文字符')
            }
            // 基本邮箱格式验证
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              return Promise.reject('请输入正确的邮箱格式')
            }
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ],
    company_id: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
    department_ids: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
    role_ids: [{ required: true, message: '请选择系统角色', trigger: 'change' }],
  }
})

// 获取国家码列表
const GetCountryCodeList = async () => {
  try {
    // const res = await GetCountryList({ page: 1, pageSize: 500, status: 1 })
    // if (res.success) {
    //   countryCodeOptions.value = res.data.list.map((item: any) => ({
    //     label: `${item.country_region_code} ${item.country_region_name}`,
    //     value: item.country_region_code,
    //   }))
    //   // 确保+86在列表中
    //   if (!countryCodeOptions.value.find((item) => item.value === '+86')) {
    //     countryCodeOptions.value.unshift({ label: '+86 中国', value: '+86' })
    //   }
    // }
  } catch (error) {
    console.error('获取国家码列表失败:', error)
    // 提供默认的国家码选项
    countryCodeOptions.value = [
      { label: '+86 中国', value: '+86' },
      // { label: '+1 美国', value: '+1' },
      // { label: '+44 英国', value: '+44' },
    ]
  }
}

// 获取直接上级下拉框
const GetLeaderList = (id: any, searchText = '') => {
  return GetAccountSelectOption({
    company_id: id,
    // scope: '内部联系人',
    status: ['启用'],
    search_text: searchText,
  }).then((res) => {
    leaderOption.value = res.data.map((item: any) => ({
      ...item,
      account_name: `${item.real_name}（${item.account_id}）`,
    }))
  })
}

// 获取角色下拉框
const GetRoleListData = (searchText = '') => {
  return GetRoleSelectOption({ search_text: searchText }).then((res) => {
    // 核心过滤逻辑：只保留status=1的角色
    const activeRoles = res.data.filter((item: any) => item.status === 1)
    roleOption.value = activeRoles.map((item: any) => ({
      label: item.role_name,
      value: `${item.role_id}`,
    }))
    if (roleOption.value.length > 0 && !searchText) {
      const item = roleOption.value.find((i: any) => i.label.includes('普通用户'))
      if (item) {
        formState.value.role_ids = item.value
      }
    }
  })
}

// 输入处理函数 - 去除空格
const handleInputChange = (e: any) => {
  const value = e.target.value
  formState.value.real_name = value.replace(/\s/g, '')
}

// 注册方式变化处理
const handleRegistrationTypeChange = () => {
  formState.value.account = ''
  if (formState.value.type === 1) {
    formState.value.area_code = '+86'
  }
}

// 国家码选择过滤
const filterCountryOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 国家码变化处理
const handleCountryCodeChange = () => {
  // 清空手机号，重新验证
  if (formState.value.account) {
    formState.value.account = ''
  }
}

// 手机号输入处理
const handlePhoneInput = (e: any) => {
  let value = e.target.value
  // 只允许数字
  value = value.replace(/\D/g, '')
  // 去除空格
  value = value.replace(/\s/g, '')
  formState.value.account = value
}

// 邮箱输入处理
const handleEmailInput = (e: any) => {
  let value = e.target.value
  // 去除空格和中文
  value = value.replace(/\s/g, '').replace(/[\u4e00-\u9fa5]/g, '')
  formState.value.account = value
}

// 上级搜索处理（防抖）
const handleLeaderSearch = (value: string) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    if (formState.value.company_id) {
      GetLeaderList(formState.value.company_id, value)
    }
  }, 300)
}

// 角色搜索处理（防抖）
const handleRoleSearch = (value: string) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    GetRoleListData(value)
  }, 300)
}

const setPathName = (data: any, treeData: any) => {
  data.forEach((item: any) => {
    treeData.forEach((item1: any) => {
      if (item.id == item1.id) {
        item.company_name = item1.company_name
      }
      if (item1.childs && item1.childs.length > 0) {
        setPathName(data, item1.childs)
      }
    })
  })
}

// 获取部门下拉框
const GetDepartmentList = (companyId: any) => {
  console.log('=== GetDepartmentList 开始 ===')
  console.log('传入的企业ID:', companyId)

  return GetCompanyTree({}).then((res) => {
    console.log('GetCompanyTree 接口返回数据:', res.data)
    // 查找指定企业下的所有部门数据
    const findCompanyTree = (nodes: any[], targetId: string): any[] => {
      console.log('findCompanyTree 查找目标ID:', targetId, '类型:', typeof targetId)

      for (const node of nodes) {
        // 检查是否为企业节点（支持数字和字符串两种格式）
        const originalType = (node as any).originalType || node.type
        const isCompanyNode = originalType === 1 || node.type === '企业=1'

        console.log('检查节点:', {
          nodeId: node.id,
          nodeIdType: typeof node.id,
          targetId,
          targetIdType: typeof targetId,
          originalType,
          nodeType: node.type,
          isCompanyNode,
          idMatch: node.id === targetId,
          idMatchStrict: String(node.id) === String(targetId),
        })

        if (String(node.id) === String(targetId) && isCompanyNode) {
          console.log('找到匹配的企业节点:', node)
          // 找到目标企业，返回包含企业本身的完整树结构
          return [node]
        }
        if (node.childs && node.childs.length > 0) {
          const result = findCompanyTree(node.childs, targetId)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }

    // 如果传入的不是企业ID，需要找到对应的企业ID
    const findParentCompanyId = (nodes: any[], targetId: string): string | null => {
      for (const node of nodes) {
        // 检查是否为企业节点（支持数字和字符串两种格式）
        const originalType = (node as any).originalType || node.type
        const isCompanyNode = originalType === 1 || node.type === '企业=1'

        if (isCompanyNode) {
          // 这是企业节点，检查是否包含目标ID
          if (node.id === targetId) {
            return targetId // 传入的就是企业ID
          }
          // 检查企业下的子节点
          const hasTargetInChildren = (children: any[]): boolean => {
            for (const child of children) {
              if (child.id === targetId) {
                return true
              }
              if (child.childs && child.childs.length > 0) {
                if (hasTargetInChildren(child.childs)) {
                  return true
                }
              }
            }
            return false
          }

          if (node.childs && hasTargetInChildren(node.childs)) {
            return node.id // 返回企业ID
          }
        }

        if (node.childs && node.childs.length > 0) {
          const result = findParentCompanyId(node.childs, targetId)
          if (result) {
            return result
          }
        }
      }
      return null
    }

    // 先确定真正的企业ID
    const actualCompanyId = findParentCompanyId(res.data || [], companyId) || companyId
    console.log('查找到的实际企业ID:', actualCompanyId)

    const companyTree = findCompanyTree(res.data || [], actualCompanyId)
    console.log('查找到的企业树:', companyTree)
    setPathName(formState.value.department, companyTree)

    // 标准化数据结构，确保 SelectDepart 组件能正确显示
    // 同时过滤掉 type=1 的企业节点，只保留部门和单位节点
    const normalizeTreeData = (nodes: any[]): any[] => {
      return nodes
        .filter((node) => {
          // 过滤掉 type=1 的企业节点，只保留部门(type=3)和单位(type=2)
          const originalType = (node as any).originalType || node.type
          return originalType !== 1 && node.type !== '企业=1'
        })
        .map((node) => ({
          ...node,
          department_name: node.name || node.department_name || '', // 确保有 department_name 字段
          company_name: node.full_name || node.company_name || '',
          childs: node.childs && node.childs.length > 0 ? normalizeTreeData(node.childs) : [],
        }))
    }

    // 如果企业树存在，需要从企业的子节点开始处理
    let processedData: any[] = []
    if (companyTree.length > 0 && companyTree[0].childs) {
      processedData = normalizeTreeData(companyTree[0].childs)
    } else {
      processedData = normalizeTreeData(companyTree)
    }

    departmentTreeData.value = processedData // 保存标准化后的树形数据

    console.log('=== GetDepartmentList 完成 ===')
    console.log('最终设置的部门树数据:', departmentTreeData.value)
    console.log('部门树数据长度:', departmentTreeData.value.length)
  })
}

// 获取企业下拉框
const GetEnterpriseList = () => {
  return GetCompanyTree({}).then((res) => {
    // 递归查找所有企业节点（type=1）
    const filterCompanies = (nodes: any[]): any[] => {
      const companies: any[] = []

      const traverse = (nodeList: any[]) => {
        nodeList.forEach((node) => {
          if (node.type === 1) {
            companies.push({
              label: node.department_name || node.company_name,
              value: node.id,
              company_category_type: node.company_category_type || 0,
            })
          }
          if (node.childs && node.childs.length > 0) {
            traverse(node.childs)
          }
        })
      }

      traverse(nodes)
      return companies
    }

    enterpriseOption.value = filterCompanies(res.data || [])
    console.log('企业选项:', enterpriseOption.value)
  })
}

// 处理企业下拉框变化后，直接上级下拉框变化（异步版本）
const handleCompanyChangeAsync = async (value: any) => {
  formState.value.leader_id = null
  formState.value.department_ids = []
  formState.value.department = []

  // 获取真正的企业ID用于获取上级列表
  // 对于上级列表，我们需要使用实际选中的ID（可能是部门ID）
  GetLeaderList(value)

  // 对于部门列表，GetDepartmentList内部会自动处理企业ID的查找
  await GetDepartmentList(value)

  console.log('=== 新建用户企业变化后部门数据 ===')
  console.log('企业ID:', value)
  console.log('部门树数据长度:', departmentTreeData.value.length)
  console.log('部门树数据:', departmentTreeData.value)
}

// 打开抽屉
const showDrawer = async (entId?: any) => {
  openDrawer.value = true

  // 先获取基础数据
  await GetRoleListData()
  await GetEnterpriseList()
  await GetCountryCodeList()

  // 确定要使用的企业ID
  let companyId = entId

  // 如果没有传入企业ID，从当前用户数据中获取
  if (!companyId) {
    companyId = getCompanyIdFromUserData()

    // 如果还是没有，尝试从localStorage中的userData获取
    if (!companyId) {
      try {
        const userData = localStorage.getItem('userData')
        if (userData) {
          const userDataObj = JSON.parse(userData)
          companyId = userDataObj.company_id || userDataObj.company?.id
        }
      } catch (error) {
        console.error('获取用户企业信息失败:', error)
      }
    }
  }

  // 设置企业信息
  if (companyId) {
    formState.value.company_id = companyId
    // 从企业列表中找到对应的企业名称
    const currentCompany = enterpriseOption.value.find((item: any) => item.value === companyId)
    if (currentCompany) {
      currentCompanyName.value = currentCompany.label
    } else {
      // 如果在企业列表中没找到，尝试从localStorage获取企业名称
      try {
        const userData = localStorage.getItem('userData')
        if (userData) {
          const userDataObj = JSON.parse(userData)
          currentCompanyName.value = userDataObj.company || '未知企业'
        }
      } catch (error) {
        currentCompanyName.value = '未知企业'
      }
    }
    // 等待部门数据加载完成
    await handleCompanyChangeAsync(companyId)
  }

  nextTick(() => {
    onDrop()
  })
}

// 打开部门选择对话框
const openDepartmentSelect = async () => {
  // 确保有部门数据，如果没有则先获取
  if (!departmentTreeData.value || departmentTreeData.value.length === 0) {
    if (formState.value.company_id) {
      await GetDepartmentList(formState.value.company_id)
    }
  }

  showSelectDepart.value = true
  await nextTick()
  const arr = formState.value.department.map((item: any) => {
    return {
      id: item.id,
      department_name: item.name || item.department_name,
      type: item.type,
      company_name: item.company_name,
    }
  })

  console.log('=== 新建用户部门选择调试 ===')
  console.log('选中的部门数组:', arr)
  console.log('部门树数据:', departmentTreeData.value)
  console.log('部门树数据长度:', departmentTreeData.value.length)

  selectDepartRef.value?.showModal(arr, departmentTreeData.value)
}

// 处理部门选择变化
const handleDepartmentChange = async (departments: any[]) => {
  showSelectDepart.value = false
  formState.value.department = departments
  formState.value.department_ids = formState.value.department.map((item: any) => item.id)
  // 更新选项显示
  departmentOption.value = departments.map((item, index: any) => ({
    label: item.department_name,
    value: item.id,
    key: item.id,
    index,
  }))
}

// 处理部门标签删除
const handleDepartmentDeselect = (value: string) => {
  // 更新 department 数组
  formState.value.department = formState.value.department.filter((item: any) => item.id !== value)
  // 更新选项显示
  departmentOption.value = formState.value.department.map((item: any, index: any) => ({
    label: item.department_name,
    value: item.id,
    index,
    key: item.id,
  }))
}

// 关闭抽屉
const closeDrawer = () => {
  openDrawer.value = false
  submitLoading.value = false // 关闭时重置loading
  emit('close')
}

// 新增成功弹窗
const showInvite = (row: any) => {
  proxy.$confirm.show({
    title: '新建成功',
    content: h('div', null, [
      h('div', null, '创建成功，请让成员通过如下账号和密码登录：'),
      h('div', null, `账号：${row.account || row.user_name}`),
      h('div', null, `密码：${row.user_password || 'WMXY@1122'}`),
      h('div', null, '登录后请用户自行修改密码'),
    ]),
    confirmText: '复制',
    onOk: () => {
      const input = document.createElement('input')
      input.value = `账号：${row.account || row.user_name}\n密码：${row.user_password || 'WMXY@1122'}`
      document.body.appendChild(input)
      input.select()
      document.execCommand('Copy')
      document.body.removeChild(input)
      message.success('复制成功')
    },
  })
}

// 保存用户的核心逻辑
const saveUserCore = async () => {
  try {
    await formRef.value?.validate()

    // 构造保存的参数
    const companyBinds = formState.value.department_ids.map((item: any, index: any) => {
      return {
        company_id: item,
        is_main: index === 0 ? 1 : 0, // 第一个为主部门
      }
    })

    const params = {
      real_name: formState.value.real_name,
      type: formState.value.type === 1 ? '1' : '2',
      account: formState.value.account,
      area_code: formState.value.type === 1 ? formState.value.area_code : '',
      leader_id: formState.value.leader_id || 0,
      company_id: formState.value.company_id,
      role_ids: formState.value.role_ids,
      companyBinds,
      scope: 1, // 内部联系人=1
    }

    const res = await AddAccount(params)
    if (res.success) {
      message.success('保存成功')
      emit('query')
      showInvite(res.data)
      closeDrawer()
    } else {
      message.error(res.message)
    }
  } catch (error: any) {
    // 获取第一个错误信息
    if (error.errorFields) {
      const firstError = error.errorFields[0]
      switch (firstError.name[0]) {
        case 'real_name':
          message.error('请输入姓名')
          break
        case 'type':
          message.error('请选择注册方式')
          break
        case 'area_code':
          message.error('请选择手机国家码')
          break
        case 'account':
          message.error(formState.value.type === 1 ? '请输入手机号码' : '请输入邮箱')
          break
        case 'company_id':
          message.error('请选择所属企业')
          break
        case 'department_ids':
          message.error('请选择所属部门')
          break
        case 'role_ids':
          message.error('请选择系统角色')
          break
        default:
          message.error(firstError.errors[0])
      }
    } else {
      message.error(error.message || '保存失败')
    }
    throw error
  }
}

// 带防抖和loading的保存函数
const handleSave = createLoadingDebounce(saveUserCore, submitLoading, 1000)

// 抽屉状态改变后的回调
const afterOpenChange = (status: boolean) => {
  if (!status) {
    formRef.value?.resetFields()
    // 清空数据
    Object.assign(formState.value, {
      real_name: '',
      type: 1, // 默认手机号注册
      account: '',
      area_code: '+86',
      leader_id: null,
      company_id: '',
      role_ids: '',
      companyBinds: [],
      scope: 1,
      department: [],
      department_ids: [],
    })
    // 清空企业名称
    currentCompanyName.value = ''
  }
}

const onDrop = () => {
  nextTick(() => {
    // 确保 DOM 已渲染
    const el: HTMLElement | null = document.querySelector('.ant-select-selection-overflow')

    if (!el) return
    new Sortable(el, {
      animation: 300,
      handle: '.dragTag', // 拖拽手柄
      delay: 10, // 拖拽延迟
      forceFallback: true, // 强制使用原生拖拽
      onEnd: (item) => {
        const { oldIndex, newIndex } = item

        const currRow = formState.value.department_ids.splice(oldIndex, 1)[0]
        const arr = cloneDeep(formState.value.department_ids)
        arr.splice(newIndex, 0, currRow)

        const currRow1 = formState.value.department.splice(oldIndex, 1)[0]
        const arr1 = cloneDeep(formState.value.department)
        arr1.splice(newIndex, 0, currRow1)

        nextTick(() => {
          // 确保 DOM 同步更新
          formState.value.department_ids = arr
          formState.value.department = arr1
          departmentOption.value = formState.value.department.map((item: any, index: any) => ({
            label: item.name || item.department_name,
            value: item.id,
            index,
          }))
        })
      },
    })
  })
}

onMounted(() => {})

defineExpose({
  show: showDrawer,
})
</script>

<style lang="scss" scoped>
:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }
}
</style>
