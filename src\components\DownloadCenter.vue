<template>
  <a-badge :dot="undownload_count" :offset="[-10, 6]">
    <a-button
      @click="
        () => {
          downloadCenterVisible = true
          search()
        }
      "
      style="margin-right: 4px"
      type="text"
    >
      <template #icon>
        <DownloadOutlined />
      </template>
    </a-button>
  </a-badge>
  <a-drawer :footerStyle="{ paddingLeft: '24px' }" :headerStyle="{ paddingTop: '15px', paddingBottom: '15px' }" v-model:open="downloadCenterVisible" width="700" placement="right">
    <template #title>
      <span>下载中心</span>
      <span style="padding-left: 12px; font-size: 12px; color: rgb(0 0 0 / 50%)">文件仅保持24小时</span>
    </template>
    <div style="display: flex; gap: 8px; margin-bottom: 12px">
      <a-input allow-clear v-model:value="file_name" placeholder="输入查找项"></a-input>
      <a-button
        @click="
          () => {
            pagination.current = 1
            search()
          }
        "
        type="primary"
      >
        搜索
      </a-button>
    </div>
    <a-table rowKey="id" size="small" :pagination="false" :columns="columns" :data-source="filesData" @change="handleTableChange">
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'completion_time'">{{ record.completion_time ? record.completion_time : '--' }}</template>
        <template v-if="column.key === 'file_name'">{{ record.file_name ? record.file_name : '--' }}</template>
        <template v-if="column.key === 'status'">
          <div v-show="record.status === 0" class="export-status">
            <ClockCircleOutlined class="icon status-waiting" />
            等待导出
          </div>
          <div v-show="record.status === 1" class="export-status">
            <LoadingOutlined class="icon status-exporting" />
            导出中
          </div>
          <div v-show="record.status === 2" class="export-status">
            <CheckCircleOutlined class="icon status-success" />
            导出成功
          </div>
          <a-tooltip>
            <!-- <template #title>{{ record.fail_reason }}</template> -->
            <div v-show="record.status === 3" class="export-status">
              <CloseCircleOutlined class="icon status-failed" />
              导出失败
              <!-- <InfoCircleOutlined class="icon2" style="color: #606266" /> -->
            </div>
          </a-tooltip>
          <div v-show="record.status === 4" class="export-status">
            <CloseCircleOutlined class="icon status-expired" />
            过期
          </div>
        </template>
        <template v-if="column.key === 'operate'">
          <div style="display: flex; gap: 8px; align-items: center">
            <!-- 预览按钮 -->
            <!-- <a-button @click="previewFile(record)" style="padding: 0" :disabled="record.status != 2 || isFileExpired(record.completion_time) || !record.file_id" type="link" size="small">
              预览
            </a-button> -->

            <!-- 下载按钮 -->
            <a-badge :dot="false && !record.download_cnt && !isFileExpired(record.completion_time) && record.status == 2" :offset="[2, 6]">
              <a-button @click="download(record)" style="padding: 0" :disabled="record.status != 2 || isFileExpired(record.completion_time)" type="link">
                <span>{{ isFileExpired(record.completion_time) ? '已过期' : '下载' }}</span>
              </a-button>
            </a-badge>

            <!-- 重新导出按钮 - 仅在导出失败且未过期时显示 -->
            <a-button v-if="record.status === 3 && !isFileExpired(record.completion_time)" @click="reExport(record)" style="padding: 0" type="link" size="small">重新导出</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <div style="display: flex; align-items: center; justify-content: flex-start; margin-top: 8px">
      <a-pagination
        @change="pageChange"
        show-quick-jumper
        :total="pagination.total"
        show-size-changer
        v-model:current="pagination.current"
        v-model:page-size="pagination.size"
        :page-size-options="pagination.sizeOption"
        size="small"
      >
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
      <div class="totalBox">
        <div class="text">总数:</div>
        <div class="total">{{ pagination.total }}</div>
      </div>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import eventBus from '@/utils/eventBus'
import { GetDownloadNotice } from '@/servers/Common'
import { DownloadOutlined, ClockCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { GetList, ClickDownload, isFileExpired, ReExport } from '@/servers/DownloadCenter'
import { exportSuccess } from '@/utils/index'
import { message } from 'ant-design-vue'
import useAppStore from '@/store/modules/app'

const appStore = useAppStore()

const downloadCenterVisible = ref(false)
const pagination = ref({
  sizeOption: ['20', '50', '100', '250'],
  size: 20,
  current: 1,
  total: 100,
})
const undownload_count = ref(0)
const loading = ref(false)
const filesData = ref([])
const file_name = ref('')
// 排序状态
const sortInfo = ref<{
  field: string
  order: 'ascend' | 'descend' | ''
}>({
  field: '',
  order: '',
})
const columns = ref([
  { title: '文件名称', key: 'file_name', width: undefined, dataIndex: 'file_name' },
  { title: '导出状态', key: 'status', width: 120 },
  {
    title: '导出时间',
    key: 'completion_time',
    dataIndex: 'completion_time',
    width: 140,
    sorter: true,
    sortOrder: computed(() => {
      if (sortInfo.value.field === 'completion_time' && sortInfo.value.order) {
        return sortInfo.value.order
      }
      return null
    }),
  },
  { title: '操作', key: 'operate', width: 120 },
])
const download = async (item: any) => {
  try {
    // 标记为已下载
    await ClickDownload()

    // 使用新的下载方式，通过 file_id 下载
    if (item.file_id) {
      // 修复：传递正确的file_id而不是file_name
      downloadFile(item.file_id, item.file_name)
    } else {
      message.error('文件ID不存在，无法下载')
    }

    // 刷新列表和通知
    search()
    getDownloadNotice()
  } catch (error) {
    console.error('下载失败:', error)
    message.error('下载失败')
  }
}

// 重新导出
const reExport = async (item: any) => {
  try {
    const res = await ReExport(item.id)
    if (res.success) {
      message.success('重新导出任务已提交，请稍候查看进度')
      search() // 刷新列表
    } else {
      message.error(res.message || '重新导出失败')
    }
  } catch (error) {
    console.error('重新导出失败:', error)
    message.error('重新导出失败')
  }
}

const pageChange = (i: number) => {
  // if (loading.value) return
  pagination.value.current = i
  search()
}

// 处理表格排序变化
const handleTableChange = (_pagination: any, _filters: any, sorter: any) => {
  console.log('排序信息:', sorter)

  if (sorter && sorter.field && sorter.order) {
    sortInfo.value.field = sorter.field
    sortInfo.value.order = sorter.order
  } else {
    // 如果取消排序，清空排序状态
    sortInfo.value.field = ''
    sortInfo.value.order = ''
  }

  // 重置到第一页并重新搜索
  pagination.value.current = 1
  search()
}

// notification.close
// exportBegainMessage
const search = () => {
  loading.value = true

  // 构建请求参数
  const params: any = {
    is_page: true,
    page: pagination.value.current,
    pageSize: pagination.value.size,
    is_get_total: true,
    file_name: file_name.value,
  }

  // 添加排序参数
  if (sortInfo.value.field && sortInfo.value.order) {
    params.sortField = sortInfo.value.field
    params.sortType = sortInfo.value.order === 'ascend' ? 'asc' : 'desc'
  }

  GetList(params)
    .then((res) => {
      loading.value = false
      pagination.value.total = res.data.total
      filesData.value = res.data.list
    })
    .catch(() => {
      loading.value = false
    })
}
const getDownloadNotice = () => {
  GetDownloadNotice().then(async (res: any) => {
    const baseUrl = await appStore.getExteriorDormanName()
    undownload_count.value = res.data.undownload_count
    if (res.data.notice_list && res.data.notice_list.length) {
      search()
      res.data.notice_list.forEach((e: any) => {
        const conentObj = JSON.parse(e.content)
        exportSuccess(conentObj.file_id, baseUrl + conentObj.url, conentObj.file_name)
      })
    }
  })
}
// 下载授权书文件
const downloadFile = async (fileId: number, auth_file_original_name?: string) => {
  if (!fileId) {
    console.warn('缺少文件信息')
    return
  }

  try {
    // 获取用户token
    const userData = JSON.parse(localStorage.getItem('userData') || '{}')
    const loginToken = userData.login_token || ''

    // 构建下载URL
    const baseUrl = import.meta.env.VITE_APP_ENV === 'development' ? '/api/api/Files/DownloadFile' : '/api/Files/DownloadFile'

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        logintoken: loginToken,
      },
      body: JSON.stringify({
        file_id: fileId,
        original_name: auth_file_original_name,
        data_source: 'SRS',
      }),
    })

    if (!response.ok) {
      console.error('下载请求失败:', response.status)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 获取文件blob
    const blob = await response.blob()

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = auth_file_original_name || 'download'
    link.style.display = 'none'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000)
  } catch (error) {
    console.error('文件下载失败:', error)
  }
}
onMounted(() => {
  eventBus.on('downNoticeFile', () => {
    ClickDownload().then(() => {
      search()
      getDownloadNotice()
    })
  })
  getDownloadNotice()
  setInterval(() => {
    getDownloadNotice()
  }, 10000)
})
</script>

<style lang="scss" scoped>
.export-status {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;

  .icon {
    font-size: 14px;
  }
}

.status-waiting {
  color: #faad14;
}

.status-exporting {
  color: #1677ff;
}

.status-success {
  color: #52c41a;
}

.status-failed {
  color: #ff4d4f;
}

.status-expired {
  color: #8c8c8c;
}

.totalBox {
  display: flex;
  align-items: flex-end;
  margin-left: 20px;
  color: #000;

  .text {
    margin-right: 8px;
    font-size: 12px;
  }

  .total {
    font-size: 12px;
    color: #409eff;
  }
}
</style>
