import { request } from './request'
// 获取列表动态字段（列名、字段、排序、固定类型）
// 获取企业资料数据
export const GetSupplier = (data) => {
  return request({ url: '/api/Supplier/GetSupplier', data }, 'GET')
}
// 根据id获取文件列表/api/Files/GetFileList
export const GetFileList = (data) => {
  return request({ url: '/api/Files/GetFileList', data }, 'POST')
}
// 预览文件 /api/Files/ViewByFileId
export const ViewByFileId = (data) => {
  return request({ url: '/api/Files/ViewByFileId', data }, 'GET')
}

// 下拉选项 /api/Common/GetDropdownItems
export const GetDropdownItems = () => {
  return request({ url: '/api/Common/GetDropdownItems' }, 'GET')
}
// 主营区域/api/Common/GetDeliveryRegionList
export const GetDeliveryRegionList = (data) => {
  return request({ url: '/api/Common/GetDeliveryRegionList', data }, 'POST')
}
// 主营类目/api/Common/GetProductCategoryList
export const GetProductCategoryList = (data) => {
  return request({ url: '/api/Common/GetProductCategoryList', data }, 'POST')
}
// 上传文件/api/Files/UploadFile
export const UploadFile = (data) => {
  return request({ url: '/api/Files/UploadFile', data }, 'POST')
}
// 修改文件证书 /api/Supplier/UpdateSupplierLicenseFileIds
export const UpdateSupplierLicenseFileIds = (data) => {
  return request({ url: '/api/Supplier/UpdateSupplierLicenseFileIds', data }, 'POST')
}
// 修改公司信息 /api/Supplier/UpdateSupplierExpand
export const UpdateSupplierExpand = (data) => {
  return request({ url: '/api/Supplier/UpdateSupplierExpand', data }, 'POST')
}
// 获取不加密号码 /api/Supplier/GetMobilephoneNumber
export const GetMobilephoneNumber = (data) => {
  return request({ url: '/api/Supplier/GetMobilephoneNumber', data }, 'GET')
}
// 获取不加密银行卡 /api/Supplier/GetMobilephoneNumber
export const GetCollectionCardNumber = (data) => {
  return request({ url: '/api/Supplier/GetCollectionCardNumber', data }, 'GET')
}
// 修改联系人、财务信息 UpdateSupplierFollow
export const UpdateSupplierFollow = (data) => {
  return request({ url: '/api/Supplier/UpdateSupplierFollow', data }, 'POST')
}
