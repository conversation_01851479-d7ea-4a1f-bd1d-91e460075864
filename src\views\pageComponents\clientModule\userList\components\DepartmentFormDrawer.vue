<template>
  <a-drawer v-model:open="visible" :title="isEdit ? '编辑部门' : '新建部门'" width="500px" placement="right" :maskClosable="false" :closable="true" @close="handleClose">
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" layout="horizontal">
      <a-form-item label="部门类型" name="type" v-if="false">
        <a-select v-model:value="formData.type" placeholder="请选择部门类型" :options="departmentTypeOptions" allow-clear />
      </a-form-item>
      <a-form-item label="部门名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入部门名称" :maxlength="30" show-count @input="handleNameInput" />
      </a-form-item>

      <a-form-item label="所属企业" name="company_id" v-if="false">
        <a-input v-model:value="currentCompanyName" placeholder="所属企业" readonly disabled style="cursor: not-allowed; background-color: #f5f5f5" />
      </a-form-item>

      <a-form-item label="上级部门" name="p_id">
        <!-- 使用树形选择器，支持多层级展开 -->
        <a-tree-select
          v-if="parentDeptOptions.length > 0"
          v-model:value="formData.p_id"
          :tree-data="parentDeptOptions"
          :field-names="{ children: 'childs', label: 'name', value: 'id' }"
          placeholder="选择上级部门"
          allow-clear
          show-search
          tree-default-expand-all
          :tree-line="true"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          tree-node-filter-prop="name"
          @change="handleParentDeptChange"
        >
          <template #title="{ name, originalType }">
            <span>{{ name }}</span>
            <span class="text-gray-400 text-xs ml-2">({{ originalType === 1 ? '企业' : originalType === 2 ? '单位' : '部门' }})</span>
          </template>
        </a-tree-select>
        <a-input v-else placeholder="正在加载上级部门选项..." disabled />
      </a-form-item>

      <a-form-item label="部门负责人" name="header_ids">
        <a-select v-model:value="formData.header_ids" placeholder="选择部门负责人" :options="userOptions" show-search :filter-option="filterUserOption" allow-clear @input="handleUserInput">
          <template #option="{ real_name, account_id, full_name }">
            <div class="user-option">
              <div class="user-name">{{ real_name }}（{{ account_id }}）</div>
              <div class="user-dept">{{ full_name?.replace(/>/g, ' > ') || '' }}</div>
            </div>
          </template>
        </a-select>
      </a-form-item>

      <!-- <a-form-item label="OA系统ID" name="oa_id">
        <a-input
          v-model:value="formData.oa_id"
          placeholder="请输入OA系统ID（可选）"
          :maxlength="20"
        />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息（可选）"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-item> -->
    </a-form>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button @click="handleClose">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { AddDepartment, EditDepartment, GetCompanyTree, AddUnit, UnitGetList, getCompanyIdFromUserData, GetCompanyById, type TreeNode } from '@/servers/CompanyArchitecture'
import { GetAccountSelectOption } from '@/servers/UserManager'

// Props
interface Props {
  // 父部门ID，如果指定则为添加子部门
  parentId?: string
  // 当前选中的企业ID
  companyId?: string
}

const props = withDefaults(defineProps<Props>(), {
  parentId: '',
  companyId: '',
})

// Emits
const emit = defineEmits<{
  success: []
  close: []
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const isEdit = ref(false)
const editId = ref('')

const formRef = ref<FormInstance>()
const formData = reactive({
  name: '',
  type: '',
  p_id: '',
  company_id: '',
  header_ids: undefined as number | undefined,
  oa_id: '',
  remark: '',
})

// 选项数据
const parentDeptOptions = ref<TreeNode[]>([])
const companyOptions = ref<TreeNode[]>([])
const userOptions = ref<any[]>([])
// 当前企业名称（用于显示）
const currentCompanyName = ref('')

// 部门类型选项
const departmentTypeOptions = ref([
  { label: '部门', value: '3' },
  { label: '单位(分公司)', value: '2' },
])

// 表单验证规则
const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { max: 30, message: '部门名称不能超过30个字符', trigger: 'blur' },
  ],
  type: [{ required: true, message: '请选择部门类型', trigger: 'change' }],
  company_id: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
}

// 计算属性
const parentId = computed(() => props.parentId)

// 监听器
watch(
  () => formData.company_id,
  (newVal) => {
    if (newVal) {
      loadUserOptions(newVal)
      // 当企业ID变化时，重新加载上级部门选项
      loadParentDeptOptions()
    } else {
      userOptions.value = []
      parentDeptOptions.value = []
    }
  },
)

// 监听部门类型变化，重新加载上级部门选项
watch(
  () => formData.type,
  () => {
    if (formData.company_id) {
      loadParentDeptOptions()
    }
  },
)

// 方法定义
const open = async (editData?: any) => {
  visible.value = true
  isEdit.value = !!editData
  editId.value = editData?.id || ''

  try {
    // 加载基础数据
    await loadCompanyOptions()
    await loadParentDeptOptions()

    if (editData) {
      // 编辑模式，通过API获取详细信息
      await loadEditData(editData)
    } else {
      // 新建模式，设置默认值
      resetForm()

      // 设置企业信息
      await setCompanyInfo()

      // 等待下一个 tick 确保数据已经更新
      await nextTick()

      // 设置默认的上级部门
      if (props.parentId) {
        formData.p_id = props.parentId
      } else if (parentDeptOptions.value.length > 0) {
        // 如果没有指定父级ID，默认选择第一个企业节点
        const firstCompany = parentDeptOptions.value.find((node) => (node as any).originalType === 1 || node.type === '企业=1')
        if (firstCompany) {
          formData.p_id = String(firstCompany.id)
        }
      }
    }
  } catch (error) {
    console.error('打开部门表单失败:', error)
    message.error('加载数据失败，请重试')
  }
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    p_id: '',
    company_id: '',
    header_ids: undefined,
    oa_id: '',
    remark: '',
  })
  // 清空企业名称
  currentCompanyName.value = ''
  formRef.value?.clearValidate()
}

// 加载编辑数据
const loadEditData = async (editData: any) => {
  try {
    const res = await GetCompanyById({ id: editData.id, type: editData.type || 3 })
    const data = res.data

    if (data) {
      // 填充表单数据
      Object.assign(formData, {
        name: data.name || '',
        type: typeof data.type === 'number' ? String(data.type) : data.type === '企业' ? '1' : data.type === '单位' ? '2' : '3',
        p_id: String(data.p_id || editData.p_id || ''), // 确保转换为字符串
        company_id: editData.company_id || data.company_id || '', // 优先使用原始editData中的company_id
        // 修复部门负责人回显：处理header_info对象结构
        header_ids: data.header_info && data.header_info.id ? Number(data.header_info.id) : editData.header_info && editData.header_info.id ? Number(editData.header_info.id) : undefined,
        oa_id: editData.oa_id || '',
        remark: '',
      })

      // 设置企业名称显示
      currentCompanyName.value = editData.company_name || data.company_name || '未知企业'

      console.log('编辑数据回显:', {
        原始数据: editData,
        API数据: data,
        表单数据: formData,
      })

      // 加载用户选项（在设置company_id后）
      if (formData.company_id) {
        await loadUserOptions(formData.company_id)
      }
    }
  } catch (error) {
    console.error('获取部门详情失败:', error)
    message.error('获取部门详情失败')

    // 如果API调用失败，使用原始editData填充表单
    Object.assign(formData, {
      name: editData.name || editData.department_name || '',
      type: editData.type || '',
      p_id: editData.p_id || '',
      company_id: editData.company_id || '',
      // 修复部门负责人回显：处理header_info对象结构
      header_ids: editData.header_info && editData.header_info.id ? Number(editData.header_info.id) : editData.header_ids ? editData.header_ids : undefined,
      oa_id: editData.oa_id || '',
      remark: editData.remark || '',
    })

    // 尝试从localStorage获取企业名称
    try {
      const userData = localStorage.getItem('userData')
      if (userData) {
        const userDataObj = JSON.parse(userData)
        currentCompanyName.value = userDataObj.company || '未知企业'
      } else {
        currentCompanyName.value = '未知企业'
      }
    } catch (error) {
      currentCompanyName.value = '未知企业'
    }

    // 即使API调用失败，也要尝试加载用户选项
    if (formData.company_id) {
      await loadUserOptions(formData.company_id)
    }
  }
}

// 从组织架构树中获取根企业ID
const getRootCompanyId = (treeData: TreeNode[]): string | null => {
  // 查找第一个type为1的节点（根企业）
  for (const node of treeData) {
    const originalType = (node as any).originalType || node.type
    if (originalType === 1 || node.type === '企业=1') {
      return String(node.id)
    }
  }
  return null
}

// 设置企业信息
const setCompanyInfo = async () => {
  // 确定要使用的企业ID
  let companyId: string | null = props.companyId

  // 如果没有传入企业ID，从组织架构树中获取根企业ID
  if (!companyId) {
    // 先尝试从组织架构树中获取根企业ID
    if (companyOptions.value.length > 0) {
      companyId = getRootCompanyId(companyOptions.value)
    }

    // 如果还是没有，从当前用户数据中获取
    if (!companyId) {
      companyId = getCompanyIdFromUserData()

      // 如果还是没有，尝试从localStorage中的userData获取
      if (!companyId) {
        try {
          const userData = localStorage.getItem('userData')
          if (userData) {
            const userDataObj = JSON.parse(userData)
            companyId = userDataObj.company_id || userDataObj.company?.id || null
          }
        } catch (error) {
          console.error('获取用户企业信息失败:', error)
        }
      }
    }
  }

  // 设置企业信息
  if (companyId) {
    formData.company_id = companyId

    // 从企业列表中找到对应的企业名称
    const currentCompany = companyOptions.value.find((item: any) => item.id === companyId)
    if (currentCompany) {
      currentCompanyName.value = currentCompany.department_name || currentCompany.company_name || '未知企业'
    } else {
      // 如果在企业列表中没找到，尝试从localStorage获取企业名称
      try {
        const userData = localStorage.getItem('userData')
        if (userData) {
          const userDataObj = JSON.parse(userData)
          currentCompanyName.value = userDataObj.company || '未知企业'
        }
      } catch (error) {
        currentCompanyName.value = '未知企业'
      }
    }
  }
}

const loadCompanyOptions = async () => {
  try {
    const res = await GetCompanyTree({})
    const data = res.data || []

    // 只显示企业节点
    const filterCompanies = (nodes: TreeNode[]): TreeNode[] => {
      return nodes
        .filter((node) => node.type === 1)
        .map((node) => ({
          ...node,
          childs: node.childs ? filterCompanies(node.childs) : [],
        }))
    }

    companyOptions.value = filterCompanies(data)
    console.log('companyOptions', companyOptions.value)
  } catch (error: any) {
    console.error('加载企业选项失败:', error)
    const errorMessage = error?.message || '加载企业选项失败'
    message.error(errorMessage)
  }
}

const loadParentDeptOptions = async () => {
  try {
    // 根据部门类型选择不同的接口
    if (formData.type === '2') {
      // 选择单位类型，调用UnitGetList接口获取单位列表
      if (!formData.company_id) {
        parentDeptOptions.value = []
        return
      }

      const res = await UnitGetList({ company_id: formData.company_id })
      const unitList = res.data || []

      // 转换为树形结构格式
      parentDeptOptions.value = unitList
        .filter((unit: any) => {
          // 过滤掉当前编辑的单位
          if (isEdit.value && unit.id === editId.value) {
            return false
          }
          return true
        })
        .map((unit: any) => ({
          id: unit.id,
          p_id: unit.p_id || '',
          name: unit.name || unit.full_name || '',
          full_name: unit.full_name || unit.name || '',
          department_name: unit.name || unit.full_name || '', // 兼容字段
          company_name: unit.company_name || '',
          type: '单位=2',
          originalType: 2,
          order: 0,
          is_internal_company: false,
          oa_id: 0,
          childs: [],
        }))
    } else {
      // 选择部门类型，调用GetCompanyTree接口获取组织架构树
      const res = await GetCompanyTree({})
      const data = res.data || []

      // 数据标准化处理
      const normalizeData = (nodes: any[]): any[] => {
        return nodes.map((node) => {
          // 处理 type 字段
          let nodeTypeString: '企业=1' | '单位=2' | '部门=3'
          switch (node.type) {
            case 1:
              nodeTypeString = '企业=1'
              break
            case 2:
              nodeTypeString = '单位=2'
              break
            case 3:
              nodeTypeString = '部门=3'
              break
            default:
              nodeTypeString = '部门=3'
          }

          // 简化的数据结构，专门为 TreeSelect 组件优化
          const normalizedNode = {
            id: String(node.id), // 确保 ID 是字符串
            name: node.name || '',
            full_name: node.full_name || '',
            type: nodeTypeString,
            originalType: node.type,
            childs: node.childs && node.childs.length > 0 ? normalizeData(node.childs) : [],

            // TreeSelect 需要的字段
            key: String(node.id),
            value: String(node.id),
            title: node.name || '',
            label: node.name || '',

            // 其他字段
            id_with_type: node.id_with_type || '',
            p_id_with_type: node.p_id_with_type || '',
            p_id: node.p_id || 0,
            parent_type: nodeTypeString,
            order: node.order || 0,
            oa_id: node.oa_id || 0,

            // 兼容性字段
            department_name: node.name || '',
            company_name: node.full_name || '',
          }

          return normalizedNode
        })
      }

      const normalizedData = normalizeData(data)

      // 过滤掉当前编辑的部门及其子部门
      const filterDepts = (nodes: any[]): any[] => {
        return nodes
          .filter((node) => {
            if (isEdit.value && String(node.id) === editId.value) {
              return false
            }
            return true
          })
          .map((node) => ({
            ...node,
            childs: node.childs && node.childs.length > 0 ? filterDepts(node.childs) : [],
          }))
      }

      const filteredData = filterDepts(normalizedData)
      parentDeptOptions.value = filteredData

      console.log('=== 部门表单上级部门选项调试 ===')
      console.log('原始数据:', data)
      console.log('标准化数据:', normalizedData)
      console.log('过滤后数据:', filteredData)
      console.log('上级部门选项:', parentDeptOptions.value)

      console.log('=== 部门表单调试信息 ===')
      console.log('过滤后的数据:', filteredData)
    }
  } catch (error: any) {
    console.error('加载上级部门选项失败:', error)
    const errorMessage = error?.message || '加载上级部门选项失败'
    message.error(errorMessage)
  }
}

const loadUserOptions = async (companyId: string) => {
  try {
    const res = await GetAccountSelectOption({
      company_id: companyId,
      // scope: '外部联系人',
      status: ['启用'], // 只获取启用状态的用户
    })
    userOptions.value =
      res.data?.map((item: any) => ({
        value: Number(item.id),
        label: `${item.real_name}（${item.account_id}）`,
        real_name: item.real_name,
        account_id: item.account_id,
        account_name: item.account_name,
        full_name: item.full_name,
      })) || []
  } catch (error: any) {
    console.error('加载用户选项失败:', error)
    const errorMessage = error?.message || '加载用户选项失败'
    message.error(errorMessage)
  }
}

const filterUserOption = (input: string, option: any) => {
  const searchText = input.toLowerCase()
  return (
    option.label?.toLowerCase().includes(searchText) ||
    option.real_name?.toLowerCase().includes(searchText) ||
    option.account_name?.toLowerCase().includes(searchText) ||
    option.full_name?.toLowerCase().includes(searchText)
  )
}

// 处理上级部门变化
const handleParentDeptChange = (value: string) => {
  console.log('上级部门选择变化:', value)
  formData.p_id = value
}

// 处理部门名称输入，去除空格
const handleNameInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  formData.name = target.value.replace(/\s+/g, '')
}

// 处理用户输入防抖
let userInputTimer: NodeJS.Timeout | null = null
const handleUserInput = (value: string) => {
  if (userInputTimer) {
    clearTimeout(userInputTimer)
  }
  userInputTimer = setTimeout(() => {
    // 这里可以添加防抖后的处理逻辑，比如搜索用户
    console.log('用户输入:', value)
  }, 300)
}

// 获取上级部门的类型信息
const getParentDeptType = (parentId: string): number => {
  if (!parentId) return 0

  // 在上级部门选项中查找对应的部门
  const findParentType = (nodes: TreeNode[]): number => {
    for (const node of nodes) {
      if (String(node.id) === parentId) {
        // 使用原始数字类型判断
        const originalType = (node as any).originalType || node.type
        if (typeof originalType === 'number') {
          return originalType
        }
        // 如果是字符串类型，进行转换
        if (node.type === '企业=1') return 1
        if (node.type === '单位=2') return 2
        if (node.type === '部门=3') return 3
        return 3 // 默认为部门
      }
      if (node.childs && node.childs.length > 0) {
        const result = findParentType(node.childs)
        if (result > 0) return result
      }
    }
    return 0
  }

  return findParentType(parentDeptOptions.value)
}

// 处理 p_id 参数的特殊逻辑
const processParentId = (pId: string): string | number => {
  if (!pId) return ''

  const parentType = getParentDeptType(pId)
  // 当选择"上级部门"的值type=1的时候需要传0 (说明这是选的公司, 上级部门为0)
  if (parentType === 1) {
    return 0
  }

  return pId
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    console.log('提交数据:', formData) // 调试日志

    if (isEdit.value) {
      // 编辑模式
      const editData = {
        id: editId.value,
        name: formData.name,
        ...(formData.p_id && { p_id: processParentId(formData.p_id) }),
        ...(formData.header_ids && { header_ids: formData.header_ids }),
      }
      await EditDepartment(editData)
      // message.success('部门更新成功')
    } else {
      // 新建模式，根据类型选择不同接口
      if (formData.type === '2') {
        // 单位创建
        const unitData = {
          name: formData.name,
          ...(formData.p_id && { p_id: processParentId(formData.p_id) }),
          ...(formData.header_ids && { header_ids: formData.header_ids }),
        }
        await AddUnit(unitData)
        // message.success('单位创建成功')
      } else {
        // 部门创建
        const deptData = {
          name: formData.name,
          ...(formData.p_id && { p_id: processParentId(formData.p_id) }),
          ...(formData.header_ids && { header_ids: formData.header_ids }),
        }
        await AddDepartment(deptData)
        // message.success('部门创建成功')
      }
    }

    emit('success')
    handleClose()
  } catch (error: any) {
    console.error('提交失败:', error)
    const errorMessage = error?.message || (isEdit.value ? '部门更新失败' : '部门创建失败')
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
  emit('close')
}

// 暴露方法
defineExpose({
  open,
})
</script>

<style lang="scss" scoped>
:deep(.ant-drawer-body) {
  padding: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-select-selector) {
  min-height: 32px;
}

:deep(.ant-tree-select-selector) {
  min-height: 32px;
}

.user-option {
  padding: 4px 0;

  .user-name {
    font-weight: 500;
    line-height: 1.4;
    color: #262626;
  }

  .user-dept {
    margin-top: 2px;
    font-size: 12px;
    line-height: 1.3;
    color: #8c8c8c;
  }
}
</style>
