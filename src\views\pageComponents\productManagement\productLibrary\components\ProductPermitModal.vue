<template>
  <a-modal v-model:open="visible" title="选择商品所需许可证" :footer="false" width="900px">
    <div class="mb-12">
      <span>若找不到所需的许可证，可</span>
      <span class="c-primary ml-4px cursor-pointer" @click="handleAddPermit">新增类目资质</span>
    </div>
    <vxe-table :data="tableData" size="small" border class="mb-24" max-height="400px" ref="tableRef" :row-config="{ keyField: 'id' }">
      <vxe-column type="seq" title="序号" width="60" align="center"></vxe-column>
      <vxe-column type="checkbox" width="60" align="center"></vxe-column>
      <vxe-column field="certificate_code" title="资质/证书编码" />
      <vxe-column field="certificate_name" title="资质/证书" />
      <vxe-column field="manufacturer_name" title="生产商" />
      <vxe-column field="validity_period" title="资质有效期" width="200">
        <template #default="{ row }">
          <span>{{ row.validity_period_start_time?.split(' ')[0] }} 至 {{ row.validity_period_end_time?.split(' ')[0] }}</span>
        </template>
      </vxe-column>
    </vxe-table>
    <div class="flex">
      <a-space>
        <a-button type="primary" @click="handleConfirm">确定</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </div>
  </a-modal>
  <AddCategory ref="addCategoryRef" @search="getList" />
</template>

<script setup lang="ts">
import { GetList } from '@/servers/Certificate'
import AddCategory from '@/views/pageComponents/productManagement/qualificationCertificate/components/AddCategory.vue'
import { message } from 'ant-design-vue'

const addCategoryRef = ref<any>()

const props = defineProps<{
  selectedPermitIds: number[]
}>()

const emits = defineEmits(['select'])
const visible = defineModel<boolean>('visible')
const tableRef = ref<any>()

const tableData = ref<any[]>([])

const handleConfirm = () => {
  const selectRecors = tableRef.value.getCheckboxRecords()
  if (selectRecors.length > 3) {
    message.error('最多选择3个许可证')
    return
  }
  emits('select', selectRecors)
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleAddPermit = () => {
  addCategoryRef.value.showDrawer()
}

const getList = () => {
  GetList({
    page: 1,
    pageSize: 99999,
  }).then((res) => {
    tableData.value = res.data.list.filter((i) => i.expired_status !== 0)
    const list = tableData.value.filter((i) => props.selectedPermitIds.includes(i.id))
    list.forEach((i) => {
      tableRef.value.setCheckboxRow(i, true)
    })
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss"></style>
