<template>
  <div class="architecture-left-panel">
    <!-- 企业/单位选择器 -->
    <div class="mb-8px">
      <a-tree-select
        v-model:value="memberSelectCompany"
        :tree-data="deptAndCompanyTree"
        :tree-line="true && { showLeafIcon: false }"
        h
        :fieldNames="{ label: 'name', value: 'id', children: 'childs' }"
        class="w-full"
        placeholder="选择企业/单位"
        @change="handleCompanyChange"
      />
    </div>

    <!-- 部门左侧区域 -->
    <div class="department-left flex-1 flex flex-col">
      <!-- 搜索过滤区 -->
      <div class="bg-#FAFAFB p-12px box-border mb-12px">
        <a-select
          class="w-full"
          v-model:value="filterValue"
          placeholder="搜索部门"
          :options="headerOption"
          show-search
          :filter-option="filterHeaderOption"
          allowClear
          :field-names="{ label: 'department_name', value: 'id' }"
          @change="handleSelectTreeFilter"
        >
          <template #option="{ department_name, company_name }">
            <div class="select-option" :title="department_name">
              <div class="option-name truncate">{{ department_name }}</div>
              <div class="option-info text-#999 truncate">
                {{ company_name?.replaceAll('/', '>') }}
              </div>
            </div>
          </template>
        </a-select>
      </div>

      <!-- 树形结构区 -->
      <div class="arch-left-list flex-1 relative overflow-hidden">
        <a-spin :spinning="spinning">
          <a-tree
            ref="treeRef"
            class="absolute w-full"
            node-key="id"
            :fieldNames="treeProps"
            v-model:expandedKeys="expandedKeys"
            :default-expanded-keys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            :tree-data="deptTree"
            :auto-expand-parent="true"
            @select="handleSelectDept"
          >
            <template #title="data">
              <div class="flex w-full items-center justify-between">
                <a-tooltip :title="data.name" v-if="isEllipsis(data.name)" placement="topLeft">
                  <span class="truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
                </a-tooltip>
                <span v-else class="truncate" :style="{ width: `${120 - (data.level || 0) * 8}px` }">{{ data.name }}</span>
                <a-space class="action-icons">
                  <a-tooltip placement="bottom" v-if="checkPermission('arch_add')">
                    <template #title>
                      <span>添加子部门</span>
                    </template>
                    <PlusOutlined class="text-10px c-#999 mt-8px" @click="handleAddSonDept(data)" />
                  </a-tooltip>
                  <a-dropdown v-if="![1, 2].includes((data as any).originalType || 3)">
                    <MoreOutlined class="c-#999 mt-8px" />
                    <template #overlay>
                      <a-menu mode="vertical" :selectable="false">
                        <a-menu-item key="1" @click="handleViewDept(data)">
                          <div class="gap-8px flex items-center">
                            <span>查看详情</span>
                          </div>
                        </a-menu-item>
                        <a-menu-item key="2" @click="handleEditDept(data.id)" v-if="!isInternalUser && checkPermission('arch_edit')">
                          <div class="gap-8px flex items-center">
                            <span>编辑信息</span>
                          </div>
                        </a-menu-item>
                        <!-- <a-menu-item key="4" @click="handleMoveUp(data.id)" v-if="checkPermission('arch_updown')">
                          <div class="gap-8px flex items-center">
                            <span>上移</span>
                          </div>
                        </a-menu-item>
                        <a-menu-item key="5" @click="handleMoveDown(data.id)" v-if="checkPermission('arch_updown')">
                          <div class="gap-8px flex items-center">
                            <span>下移</span>
                          </div>
                        </a-menu-item> -->
                        <a-menu-item key="6" @click="handleDeleteDept(data.id)" v-if="!isInternalUser && checkPermission('arch_delete') && btnPermission[211008]">
                          <div class="gap-8px flex items-center text-red-500">
                            <span>删除</span>
                          </div>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </div>
            </template>
          </a-tree>
        </a-spin>
      </div>

      <!-- 底部操作区 -->
      <div class="tree-bottom flex mt-12px">
        <!-- 展开全部按钮 - 内部用户时占满整个宽度 -->
        <div :class="['expand-all-btn flex cursor-pointer items-center justify-center hover:bg-gray-50 transition-colors', isInternalUser ? 'flex-1' : 'flex-1']" @click="toggleExpandAll">
          <!-- <DoubleRightOutlined :class="['expand-icon', { 'rotate-90': isAllExpanded }]" /> -->
          <component :is="DoubleRightOutlined" :class="['expand-icon', isAllExpanded ? 'rotate-up' : 'rotate-down']" />
          <span class="expand-text">{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
        </div>
        <!-- 新建部门按钮 - 内部用户时隐藏 -->
        <div
          v-if="!isInternalUser"
          class="add-dept-btn flex flex-1 cursor-pointer items-center justify-center text-white hover:bg-blue-600 transition-colors"
          @click="handleAddDept"
          v-show="checkPermission('arch_add')"
        >
          <PlusCircleOutlined class="mr-4px" />
          <span class="add-text">新建部门</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined, MoreOutlined, DoubleRightOutlined, PlusCircleOutlined } from '@ant-design/icons-vue'
import { GetCompanyTreeAuto, DeleteDepartment, type TreeNode, type SearchOption } from '@/servers/CompanyArchitecture'
import { checkPagePermission } from '@/utils'
import { usePermission } from '@/hook/usePermission'

const { btnPermission } = usePermission()

// 获取当前用户信息，判断是否为内部用户
const getCurrentUserScope = () => {
  try {
    const userDataStr = localStorage.getItem('userData')
    if (userDataStr) {
      const userData = JSON.parse(userDataStr)
      return userData.scope || 2 // 默认为外部用户
    }
    return 2 // 默认为外部用户
  } catch (error) {
    console.error('获取用户scope失败:', error)
    return 2 // 默认为外部用户
  }
}

// 判断是否为内部用户（scope=1为内部用户，scope=2为外部用户）
const isInternalUser = computed(() => getCurrentUserScope() === 1)

// 简单的深拷贝函数，避免依赖lodash
const cloneDeep = <T,>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map((item) => cloneDeep(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = cloneDeep(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

// Props
interface Props {
  // 当前选中的部门/企业ID
  selectedId?: string
  // 权限检查函数
  // eslint-disable-next-line no-unused-vars
  checkPermission?: (_permission: string) => boolean
}

// 修正默认值，确保类型匹配
const props = withDefaults(defineProps<Props>(), {
  selectedId: '',
  // 默认实现：忽略未使用的参数（下划线表示有意未使用）
  checkPermission: () => () => true,
})

// Emits
const emit = defineEmits<{
  // 选择部门/企业变化
  'select-change': [id: string, data: TreeNode | null]
  // 添加部门
  'add-dept': [parentId?: string]
  // 编辑部门
  'edit-dept': [id: string]
  // 查看部门详情 - 优化传参，增加 type 参数
  'view-dept': [id: string, type: number]
  // 部门操作成功（删除、移动等）
  'dept-operation-success': []
}>()

// 响应式数据
const memberSelectCompany = ref<string>('')
const filterValue = ref<any>(null)
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const isAllExpanded = ref(true)
const spinning = ref(false)
const treeRef = ref()

// 数据源
const deptAndCompanyTree = ref<TreeNode[]>([])
const enterpriseAccountOption = ref<TreeNode[]>([])
const deptTree = ref<TreeNode[]>([])
const headerOption = ref<SearchOption[]>([])

// 树形结构字段映射
const treeProps = ref({
  children: 'childs',
  title: 'name',
  key: 'id',
})

// 缓存所有节点的keys，避免重复计算
const allKeysCache = ref<string[]>([])

// 先定义 getAllKeys 函数
const getAllKeys = (data: TreeNode[]): string[] => {
  const keys: string[] = []

  // 使用迭代而非递归，避免深层嵌套时的性能问题
  const stack = [...data]

  while (stack.length > 0) {
    const node = stack.pop()!
    keys.push(String(node.id))

    // 将子节点添加到栈中（逆序添加以保持遍历顺序）
    if (node.childs?.length) {
      for (let i = node.childs.length - 1; i >= 0; i--) {
        stack.push(node.childs[i])
      }
    }
  }

  return keys
}

// 监听部门树变化，更新缓存
watch(
  deptTree,
  (newVal) => {
    allKeysCache.value = getAllKeys(newVal)
  },
  { deep: true, immediate: true },
)

// 监听展开状态变化
watch(
  expandedKeys,
  (newVal) => {
    // 使用缓存的keys，避免重复计算
    const allKeys = allKeysCache.value
    isAllExpanded.value = allKeys.length > 0 && allKeys.every((key) => newVal.includes(key))
  },
  { deep: true },
)

// 监听外部选中状态变化
watch(
  () => props.selectedId,
  (newVal) => {
    if (newVal && newVal !== selectedKeys.value[0]) {
      selectedKeys.value = [newVal]
    }
  },
  { immediate: true },
)

// 生命周期
onMounted(async () => {
  console.log('=== ArchitectureLeftPanel 组件挂载 ===')

  // 检查当前用户是否有权限访问用户管理页面
  const hasUserManagementPermission = checkPagePermission('/userLists')
  console.log('用户管理权限检查结果:', hasUserManagementPermission)

  // 临时移除权限检查，确保数据能够加载
  // if (hasUserManagementPermission) {
  console.log('开始加载组织架构数据...')
  await getAllDept('init')
  console.log('组织架构数据加载完成')
  // changeDeptTree() 会在 getAllDept 中自动调用，不需要在这里重复调用
  // } else {
  //   console.warn('用户无权限访问用户管理页面，跳过组织架构数据加载')
  // }
})

// 方法定义

const getAllDept = async (type?: string) => {
  try {
    // 获取真实的接口数据
    const res = await GetCompanyTreeAuto()
    const data = res.data // 优先使用接口数据，接口失败时使用测试数据

    // 数据格式转换和标准化
    const normalizeData = (nodes: any[]): TreeNode[] => {
      return nodes.map((node) => {
        // 处理 type 字段：接口返回数字类型，需要转换为字符串格式
        let nodeTypeString: '企业=1' | '单位=2' | '部门=3'
        let parentTypeString: '企业=1' | '单位=2' | '部门=3'

        switch (node.type) {
          case 1:
            nodeTypeString = '企业=1'
            break
          case 2:
            nodeTypeString = '单位=2'
            break
          case 3:
            nodeTypeString = '部门=3'
            break
          default:
            nodeTypeString = '部门=3'
        }

        switch (node.parent_type) {
          case 1:
            parentTypeString = '企业=1'
            break
          case 2:
            parentTypeString = '单位=2'
            break
          case 3:
            parentTypeString = '部门=3'
            break
          default:
            parentTypeString = '企业=1'
        }

        // 新接口返回的数据结构
        const normalizedNode: TreeNode = {
          id_with_type: node.id_with_type || '',
          p_id_with_type: node.p_id_with_type || '',
          id: node.id,
          p_id: node.p_id || 0,
          parent_type: parentTypeString,
          name: node.name || '',
          full_name: node.full_name || '',
          type: nodeTypeString,
          order: node.order || 0,
          oa_id: node.oa_id || 0,
          childs: node.childs && node.childs.length > 0 ? normalizeData(node.childs) : [],

          // 兼容性字段
          department_name: node.name || '',
          company_name: node.full_name || '',
        }

        // 保留原始数字类型以便兼容
        ;(normalizedNode as any).originalType = node.type

        return normalizedNode
      })
    }

    const normalizedData = normalizeData(data)

    // 设置层级信息
    const setLevel = (nodes: TreeNode[], level = 1, parentPath = '') => {
      nodes.forEach((node) => {
        node.level = level
        node.class = `level-${level}`
        if (node.childs?.length) {
          setLevel(node.childs, level + 1, `${parentPath}/${node.name}`)
        }
      })
    }

    setLevel(normalizedData)
    enterpriseAccountOption.value = cloneDeep(normalizedData)

    // 过滤企业节点 - 保留企业节点及其所有子节点
    const filterNodes = (nodes: TreeNode[]): TreeNode[] => {
      return nodes
        .filter((node) => node.type === '企业=1')
        .map((node) => ({
          ...node,
          // 企业节点的子节点保持原样，不再递归过滤
          childs: node.childs || [],
        }))
    }

    deptAndCompanyTree.value = filterNodes(normalizedData)

    // 初始化时自动选择第一个企业
    if (type === 'init' && deptAndCompanyTree.value.length > 0) {
      memberSelectCompany.value = String(deptAndCompanyTree.value[0].id)
    }

    // 无论是初始化还是刷新，都需要更新部门树
    if (memberSelectCompany.value) {
      changeDeptTree()
    }

    findHeaderOption()
  } catch (error: any) {
    console.error('获取组织架构数据失败:', error)
    // 显示接口返回的错误信息
    const errorMessage = error?.message || '获取组织架构数据失败'
    message.error(errorMessage)
  }
}

const changeDeptTree = () => {
  if (!memberSelectCompany.value) {
    deptTree.value = []
    console.log('没有选择企业，清空部门树') // 调试日志
    return
  }

  console.log('当前选择的企业ID:', memberSelectCompany.value) // 调试日志
  console.log('企业账户选项数据:', enterpriseAccountOption.value) // 调试日志
  console.log('企业账户选项数据长度:', enterpriseAccountOption.value.length) // 调试日志

  const findCompanyTree = (data: TreeNode[], companyId: string): TreeNode[] => {
    for (const item of data) {
      if (String(item.id) === companyId) {
        console.log('找到匹配的企业:', item) // 调试日志
        // 返回包含企业本身的数组，而不是只返回子节点
        return [item]
      }
      if (item.childs?.length) {
        const result = findCompanyTree(item.childs, companyId)
        if (result.length > 0) return result
      }
    }
    return []
  }

  deptTree.value = findCompanyTree(enterpriseAccountOption.value, memberSelectCompany.value)
  console.log('设置的部门树数据:', deptTree.value) // 调试日志
  console.log('部门树数据长度:', deptTree.value.length) // 调试日志

  // 自动展开前两层 - 平衡性能和用户体验
  nextTick(() => {
    // 获取前两层的keys
    const keys: string[] = []
    const getFirstTwoLevels = (nodes: TreeNode[], currentLevel = 1, parentPath = '') => {
      console.log(`处理第${currentLevel}层，节点数量:`, nodes.length, '路径:', parentPath)

      nodes.forEach((node) => {
        const nodePath = parentPath ? `${parentPath}/${node.name}` : node.name
        console.log(`第${currentLevel}层节点:`, node.name, 'ID:', node.id, '子节点数量:', node.childs?.length || 0)

        // 第1层和第2层的节点都要展开
        if (currentLevel <= 2) {
          keys.push(String(node.id))
          console.log(`添加到展开列表: ${node.id} (${node.name})`)
        }

        // 如果当前是第1层且有子节点，继续处理第2层
        if (node.childs?.length && currentLevel === 1) {
          getFirstTwoLevels(node.childs, currentLevel + 1, nodePath)
        }
      })
    }

    console.log('开始处理架构树展开，deptTree.value:', deptTree.value)
    getFirstTwoLevels(deptTree.value)

    console.log('最终展开的节点keys:', keys)

    // 立即设置展开的keys
    expandedKeys.value = [...keys]
    console.log('立即设置expandedKeys:', expandedKeys.value)

    // 延迟设置选中状态，确保Tree组件已经渲染完成
    setTimeout(() => {
      // 自动选择第一级企业节点
      if (deptTree.value.length > 0 && deptTree.value[0]) {
        const enterpriseNode = deptTree.value[0]
        const enterpriseNodeId = String(enterpriseNode.id)

        // 确保选择的是企业节点（第一级）
        const originalType = (enterpriseNode as any).originalType || enterpriseNode.type
        if (originalType === 1 || enterpriseNode.type === '企业=1') {
          // 设置选中状态
          selectedKeys.value = [enterpriseNodeId]
          console.log('设置初始选中企业节点:', enterpriseNodeId, '企业名称:', enterpriseNode.name)

          // 确保选中状态生效后再触发事件
          nextTick(() => {
            // 触发选择变化事件
            emit('select-change', enterpriseNodeId, enterpriseNode)
            console.log('初始选择企业节点完成:', enterpriseNodeId, '企业名称:', enterpriseNode.name)
          })
        } else {
          console.warn('第一个节点不是企业节点，跳过自动选择')
        }
      }
    }, 200) // 增加延迟时间，确保组件完全渲染

    // 延迟验证展开状态
    setTimeout(() => {
      console.log('延迟验证 - expandedKeys:', expandedKeys.value)
      console.log('延迟验证 - selectedKeys:', selectedKeys.value)
    }, 300)
  })
}

const findHeaderOption = () => {
  const options: SearchOption[] = []

  const traverse = (nodes: TreeNode[], parentPath = '') => {
    nodes.forEach((node) => {
      if (node.type !== '企业=1') {
        // 不包括企业节点
        // 使用原始数字类型
        const typeNumber = (node as any).originalType || 3

        options.push({
          id: String(node.id),
          department_name: node.name,
          company_name: parentPath || node.name, // 使用父级路径，而不是包含自身名称的路径
          type: typeNumber,
        })
      }

      if (node.childs?.length) {
        const currentPath = parentPath ? `${parentPath}/${node.name}` : node.name
        traverse(node.childs, currentPath)
      }
    })
  }

  traverse(enterpriseAccountOption.value)
  headerOption.value = options
  console.log('搜索选项数据生成完成，数量:', options.length) // 简化的调试日志
}

// 事件处理方法
const handleCompanyChange = () => {
  // 清除之前的手动选中样式（但不影响正常选中状态）
  clearManualSelectedStyles()

  // 先清空选中状态，让新的企业树重新设置
  selectedKeys.value = []
  filterValue.value = null

  // 重新构建部门树（会自动设置新的选中状态）
  changeDeptTree()
  findHeaderOption()

  // 注意：changeDeptTree() 中已经会自动选择第一个节点并触发 select-change 事件
}

const handleSelectDept = (selectedKeysValue: string[]) => {
  // 清除所有手动添加的选中样式
  clearManualSelectedStyles()

  if (selectedKeysValue.length > 0) {
    const selectedId = selectedKeysValue[0]

    const findNode = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        if (String(node.id) === String(selectedId)) return node
        if (node.childs?.length) {
          const result = findNode(node.childs)
          if (result) return result
        }
      }
      return null
    }

    const selectedNode = findNode(deptTree.value)
    console.log('架构树选择:', selectedId, '节点类型:', selectedNode?.type || 'unknown')

    emit('select-change', selectedId, selectedNode)
  }
}

// 清除手动添加的选中样式（但保留正常的选中状态）
const clearManualSelectedStyles = () => {
  // 只移除手动添加的强制选中样式类，不影响正常的选中状态
  document.querySelectorAll('.force-selected').forEach((el) => {
    el.classList.remove('force-selected')
    // 不移除 ant-tree-treenode-selected，让组件自然管理
  })
}

// 防抖处理搜索过滤
let searchTimeout: NodeJS.Timeout | null = null

const handleSelectTreeFilter = (newVal: string) => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  if (!newVal) {
    // 清除选中状态时只清除手动样式，保留正常选中状态
    selectedKeys.value = []
    return
  }

  // 使用防抖，避免频繁操作
  searchTimeout = setTimeout(() => {
    // 清除之前的手动样式（但不影响即将设置的选中状态）
    clearManualSelectedStyles()

    // 定位到选中节点并展开路径
    const findAndExpandPath = (nodes: TreeNode[], targetId: string, path: string[] = []): boolean => {
      for (const node of nodes) {
        const currentPath = [...path, String(node.id)]

        if (String(node.id) === targetId) {
          expandedKeys.value = [...new Set([...expandedKeys.value, ...path])]
          selectedKeys.value = [targetId]

          // 触发选择事件
          emit('select-change', targetId, node)
          return true
        }

        if (node.childs?.length) {
          if (findAndExpandPath(node.childs, targetId, currentPath)) {
            return true
          }
        }
      }
      return false
    }

    findAndExpandPath(deptTree.value, newVal)
    searchTimeout = null
  }, 150) // 150ms 防抖延迟
}

const filterHeaderOption = (input: string, option: any) => {
  return option.department_name.toLowerCase().includes(input.toLowerCase())
}

const toggleExpandAll = () => {
  // 使用缓存的keys，避免重复计算
  const allKeys = allKeysCache.value

  // 如果节点数量很多（超过100个），显示loading状态
  if (allKeys.length > 100) {
    spinning.value = true
    // 使用 requestAnimationFrame 确保UI更新的流畅性
    requestAnimationFrame(() => {
      if (isAllExpanded.value) {
        expandedKeys.value = []
      } else {
        expandedKeys.value = [...allKeys] // 使用展开运算符创建新数组
      }
      nextTick(() => {
        spinning.value = false
      })
    })
  } else {
    // 节点数量较少时，直接更新，无需loading状态
    if (isAllExpanded.value) {
      expandedKeys.value = []
    } else {
      expandedKeys.value = [...allKeys] // 使用展开运算符创建新数组
    }
  }
}

// 操作方法
const handleAddDept = () => {
  emit('add-dept')
}

const handleAddSonDept = (data: TreeNode) => {
  emit('add-dept', String(data.id))
}

const handleEditDept = (id: string) => {
  emit('edit-dept', id)
}

const handleViewDept = (data: TreeNode) => {
  const id = String(data.id)
  console.log('查看部门详情:', id, '节点数据:', data)

  // 获取准确的 type 参数的多重保障机制
  let type = 3 // 默认为部门

  // 方法1: 优先使用原始数字类型
  const originalType = (data as any).originalType
  if (typeof originalType === 'number') {
    type = originalType
    console.log('使用原始数字类型:', type)
  } else {
    // 方法2: 根据字符串类型转换为数字
    switch (data.type) {
      case '企业=1':
        type = 1
        break
      case '单位=2':
        type = 2
        break
      case '部门=3':
        type = 3
        break
      default: {
        // 方法3: 如果字符串类型也不匹配，尝试从树结构中查找
        const foundNode = findNodeInTree(deptTree.value, id)
        if (foundNode) {
          const foundOriginalType = (foundNode as any).originalType
          if (typeof foundOriginalType === 'number') {
            type = foundOriginalType
            console.log('从树结构中找到正确类型:', type)
          }
        }
        break
      }
    }
  }

  console.log('查看节点详情 - ID:', id, '类型:', data.type, '数字类型:', type, '节点名称:', data.name)
  emit('view-dept', id, type)
}

// 辅助函数：在树结构中查找节点
const findNodeInTree = (nodes: TreeNode[], targetId: string): TreeNode | null => {
  for (const node of nodes) {
    if (String(node.id) === String(targetId)) {
      return node
    }
    if (node.childs?.length) {
      const result = findNodeInTree(node.childs, targetId)
      if (result) return result
    }
  }
  return null
}

// 处理删除部门（添加二次确认）
// 处理删除部门（添加二次确认弹窗）
const handleDeleteDept = async (id: string) => {
  try {
    // 二次确认弹窗（使用 Modal.confirm）
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除部门？删除前，请先移除部门内所有人员再进行操作，否则会操作失败.',
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger', // 确认按钮为危险色
      // 点击确认按钮的回调
      onOk: async () => {
        spinning.value = true // 显示加载状态
        try {
          // 调用删除接口
          await DeleteDepartment({
            id,
            p_id: '',
            company_id: memberSelectCompany.value,
            name: '',
            header_ids: 0,
            oa_id: '',
          })
          message.success('删除成功')
          await getAllDept() // 刷新部门树
          emit('dept-operation-success') // 通知父组件
        } catch (error: any) {
          const errorMessage = error?.message || '删除失败'
          message.error(errorMessage)
        } finally {
          spinning.value = false // 关闭加载状态
        }
      },
      // 点击取消按钮的回调（可选）
      onCancel: () => {
        // 无需操作，弹窗会自动关闭
      },
    })
  } catch (error) {
    console.error('弹窗操作异常:', error)
  }
}

// const handleMoveUp = async (id: string) => {
//   try {
//     await DepartmentMove({
//       department_id: id,
//       direction: true,
//       company_id: memberSelectCompany.value, // 添加company_id字段
//     })
//     message.success('上移成功')
//     await getAllDept()
//     // 通知父组件刷新用户列表
//     emit('dept-operation-success')
//   } catch (error: any) {
//     // 显示接口返回的错误信息
//     const errorMessage = error?.message || '上移失败'
//     message.error(errorMessage)
//   }
// }

// const handleMoveDown = async (id: string) => {
//   try {
//     await DepartmentMove({
//       department_id: id,
//       direction: false,
//       company_id: memberSelectCompany.value, // 添加company_id字段
//     })
//     message.success('下移成功')
//     await getAllDept()
//     // 通知父组件刷新用户列表
//     emit('dept-operation-success')
//   } catch (error: any) {
//     // 显示接口返回的错误信息
//     const errorMessage = error?.message || '下移失败'
//     message.error(errorMessage)
//   }
// }

// 检测文本是否需要省略号
const isEllipsis = (text: string) => {
  // 创建一个隐藏的 span 元素用于测量文本宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'fixed'
  span.style.whiteSpace = 'nowrap'
  span.style.fontSize = '14px'
  span.style.fontFamily = 'inherit'
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  // 120px 是设置的最大宽度
  return width > 120
}

// 暴露方法给父组件
defineExpose({
  refresh: getAllDept,
  getSelectedCompany: () => memberSelectCompany.value,
  getSelectedDept: () => selectedKeys.value[0] || '',
})
</script>

<style lang="scss" scoped>
.architecture-left-panel {
  position: relative;
  z-index: 1;
  display: flex !important;
  flex-direction: column !important;
  flex-grow: 0 !important; /* 防止扩展 */
  flex-shrink: 0 !important; /* 防止被压缩 */
  width: 230px !important;
  min-width: 230px !important;
  max-width: 230px !important;
  height: 100%;
  margin-right: 16px;
}

.department-left {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
}

.arch-left-list {
  flex: 1;
  max-height: calc(100vh - 200px);
  overflow: visible auto !important;
  border-top: 1px solid #f0f0f0;

  :deep(.ant-tree-treenode) {
    display: flex;
    align-items: center;
    width: 204px;
    height: 32px;
    color: #666;

    &:hover {
      color: #409eff;
      background-color: #eef2fa;

      .action-icons {
        opacity: 1 !important;
      }
    }

    .ant-tree-node-content-wrapper {
      display: inline-block;
      flex: 1;

      &:hover {
        background-color: #eef2fa;
      }
    }
  }

  :deep(.ant-tree-switcher) {
    line-height: 32px;
  }

  :deep(.ant-tree-treenode-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;
  }

  :deep(.ant-tree-node-selected) {
    background-color: #eef2fa !important;
  }

  :deep(.ant-tree-treenode-selected .ant-tree-node-content-wrapper) {
    color: #409eff !important;
    background-color: #eef2fa !important;
  }

  :deep(.ant-tree-node-content-wrapper-selected) {
    color: #409eff !important;
    background-color: #eef2fa !important;
  }

  // 强制为选中的节点添加样式（通过动态类名）
  :deep(.force-selected) {
    .ant-tree-node-content-wrapper {
      color: #409eff !important;
      background-color: #eef2fa !important;
    }
  }

  :deep(.anticon-caret-down) {
    color: #c0c0c0;
  }

  :deep(.action-icons) {
    // 强制显示（避免被挤压隐藏）
    display: flex !important;
    align-items: center;

    // 固定按钮区域最小宽度（确保能放下2个图标）
    min-width: 50px !important;
    opacity: 0;
    transition: opacity 0.2s;
  }
}

// 移除复杂的节点样式，使用简洁的参考样式

.tree-bottom {
  overflow: hidden;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 6px 6px;

  .expand-all-btn {
    padding: 8px;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    transition: all 0.2s;

    .expand-icon {
      margin-right: 4px;
      font-size: 14px;
      color: #666;
      transition: transform 0.2s;

      // &.rotate-90 {
      //   transform: rotate(90deg);
      // }
    }

    .expand-text {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
    }

    &:hover {
      background-color: #f5f5f5;

      .expand-icon {
        color: #1890ff;
      }

      .expand-text {
        color: #1890ff;
      }
    }
  }

  .add-dept-btn {
    padding: 8px;
    background: #1890ff;
    transition: all 0.2s;

    .add-text {
      font-size: 12px;
      font-weight: 500;
      color: #fff;
    }

    &:hover {
      background-color: #40a9ff;
    }
  }
}

// expand-icon 样式已移到 .expand-all-btn 内部
.rotate-down {
  transform: rotate(90deg);
}

.rotate-up {
  transform: rotate(-90deg);
}

.select-option {
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  .option-name {
    margin-bottom: 2px;
    font-weight: 500;
    color: #262626;
  }

  .option-info {
    font-size: 12px;
    color: #8c8c8c;
  }
}

// 企业选择器样式优化
:deep(.ant-tree-select) {
  .ant-select-selector {
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
    }

    &.ant-select-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }
}

// 搜索框样式优化
:deep(.ant-select) {
  .ant-select-selector {
    border-color: #d9d9d9;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
    }

    &.ant-select-focused {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
    }
  }
}

// 确保搜索下拉框能正确显示
:deep(.ant-select-dropdown) {
  z-index: 9999 !important;
}

// 搜索选项样式
:deep(.ant-select-item-option) {
  padding: 8px 12px !important;

  &:hover {
    background-color: #f5f5f5 !important;
  }
}
</style>
