import { notification } from 'ant-design-vue'
import { h } from 'vue'
import { LoadingOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import { DownloadFile } from '@/servers/DownloadCenter'

interface NotificationData {
  type: 'progress' | 'complete' | 'failed'
  title: string
  message: string
  taskId?: string
  fileId?: string
  fileName?: string
  downloadUrl?: string
  duration?: number // 新增可选的时长参数
}

// 存储通知实例，用于管理进度通知的更新和关闭
const notificationInstances = new Map<string, any>()

// 显示导出通知
export const showExportNotification = (data: NotificationData) => {
  const { type, title, message: msg, taskId, downloadUrl } = data

  // 支持后端字段格式：file_id/fileId, file_name/fileName
  const fileId = (data as any).file_id || data.fileId
  const fileName = (data as any).file_name || data.fileName

  // 根据文件名生成消息内容
  let finalMessage = msg
  if (type === 'complete' && fileName) {
    finalMessage = `【${fileName}】导出文件已经准备完成，文件下载链接将于24小时后失效，请尽快下载。`
  }

  // 调试信息：检查下载相关参数
  if (type === 'complete') {
    console.log('🔍 导出通知调试 - type:', type, 'fileId:', fileId, 'fileName:', fileName, 'downloadUrl:', downloadUrl)
    console.log('🔍 原始数据:', data)
    console.log('🔍 是否显示下载按钮:', !!(fileId || downloadUrl))
  }

  // 如果是进度通知，检查是否已存在相同任务的通知
  if (type === 'progress' && taskId) {
    const existingKey = `progress_${taskId}`
    if (notificationInstances.has(existingKey)) {
      // 关闭现有通知，创建新的
      const instance = notificationInstances.get(existingKey)
      if (instance && instance.destroy) {
        instance.destroy()
      }
      notificationInstances.delete(existingKey)
    }
  }

  // 如果是完成通知，关闭对应的进度通知
  if (type === 'complete' && taskId) {
    const progressKey = `progress_${taskId}`
    if (notificationInstances.has(progressKey)) {
      const progressInstance = notificationInstances.get(progressKey)
      if (progressInstance && progressInstance.destroy) {
        progressInstance.destroy()
      }
      notificationInstances.delete(progressKey)
    }
  }

  // 创建下载处理函数
  const handleDownload = async () => {
    try {
      if (fileId && fileName) {
        await DownloadFile(Number(fileId), fileName)
        notification.success({ message: '文件下载成功' })
      } else if (downloadUrl) {
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = fileName || 'download'
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        notification.success({ message: '文件下载成功' })
      }
    } catch (error) {
      console.error('下载失败:', error)
      notification.error({ message: '下载失败，请重试' })
    }
  }

  // 创建通知内容
  const createContent = () => {
    const iconMap = {
      progress: h(LoadingOutlined, { spin: true, style: { color: '#1890ff' } }),
      complete: h(CheckCircleOutlined, { style: { color: '#52c41a' } }),
      failed: h(CloseCircleOutlined, { style: { color: '#ff4d4f' } }),
    }

    return h(
      'div',
      {
        class: 'export-notification-content',
        style: {
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
          padding: '8px',
        },
      },
      [
        // 主要内容区域
        h('div', { style: { display: 'flex', alignItems: 'flex-start', gap: '8px' } }, [
          h('div', { style: { marginTop: '1px', lineHeight: '1.2' } }, iconMap[type]), // 图标容器，与标题精确对齐
          h('div', { style: { flex: 1, minWidth: 0 } }, [
            h('div', { style: { fontWeight: '600', marginBottom: '4px', lineHeight: '1.2' } }, title),
            h('div', { style: { color: '#666', fontSize: '13px', lineHeight: '1.4', wordWrap: 'break-word' } }, finalMessage),
          ]),
        ]),
        // 下载按钮区域（单独一行，右对齐）
        type === 'complete' && (fileId || downloadUrl)
          ? h(
              'div',
              {
                style: { display: 'flex', justifyContent: 'flex-end', marginTop: '4px' },
              },
              [
                h(
                  'button',
                  {
                    style: {
                      backgroundColor: '#1890ff',
                      color: 'white',
                      border: 'none',
                      padding: '4px 12px',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px',
                    },
                    onClick: handleDownload,
                    onMouseover: (e: Event) => {
                      const target = e.target as HTMLElement
                      target.style.backgroundColor = '#40a9ff'
                    },
                    onMouseout: (e: Event) => {
                      const target = e.target as HTMLElement
                      target.style.backgroundColor = '#1890ff'
                    },
                  },
                  '立即下载',
                ),
              ],
            )
          : null,
      ].filter(Boolean),
    )
  }

  // 显示通知
  const instance = notification.open({
    message: createContent(),
    // duration: data.duration !== undefined ? data.duration : type === 'progress' ? 0 : 5, // 优先使用传入的duration
    duration: type === 'progress' ? 5 : 5, // 优先使用传入的duration
    placement: 'topRight',
    style: {
      width: '400px',
    },
    onClose: () => {
      // 清理实例引用
      if (taskId) {
        const key = `${type}_${taskId}`
        notificationInstances.delete(key)
      }
    },
  })

  // 存储实例引用
  if (taskId) {
    const key = `${type}_${taskId}`
    notificationInstances.set(key, instance)
  }
}

// 清理所有通知
export const clearAllExportNotifications = () => {
  notificationInstances.forEach((instance) => {
    if (instance && instance.destroy) {
      instance.destroy()
    }
  })
  notificationInstances.clear()
}
