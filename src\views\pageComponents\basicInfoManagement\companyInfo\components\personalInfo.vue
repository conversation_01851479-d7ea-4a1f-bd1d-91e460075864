<template>
  <div class="personalInfoBox">
    <div class="PI_Box" v-for="(item, index) in tableInfo" :key="index">
      <div class="PI_Box_Title">{{ item.title }}</div>
      <div class="PI_Box_Items">
        <template v-for="(titem, tindex) in item.tableItem" :key="tindex">
          <div v-if="titem.ishide != true" class="PI_Box_Item" :style="{ width: titem.width + '%' }">
            <div v-if="titem.type != 'table'" class="PI_Box_Item_label" :style="{ width: titem.labelwidth + 'px' }">{{ titem.label }}：</div>
            <div v-if="titem.type == 'text'" class="PI_Box_Item_content">
              {{ tableobj[titem.key] == 'null' ? '' : tableobj[titem.key] }}
              {{ !!titem.suffix ? titem.suffix : '' }}
              {{ titem.key == 'business_license_validity' && tableobj.is_long == true ? '（长期）' : '' }}
            </div>
            <div v-if="titem.type == 'textnumberarr'" class="PI_Box_Item_content">
              {{ tableobj[titem.key] }}~{{ tableobj[titem.key2] }}
              {{ !!titem.suffix ? titem.suffix : '' }}
            </div>
            <div v-if="titem.type == 'textoptions'" class="PI_Box_Item_content">
              <template v-for="(otiem, oindex) in titem.options" :key="oindex">
                <template v-if="otiem.value == tableobj[titem.key]">
                  {{ otiem.label }}
                </template>
              </template>
            </div>
            <div v-if="titem.type == 'textmoreoptions'" class="PI_Box_Item_content">
              <template v-for="(ootitem, ooindex) in tableobj[titem.key]" :key="ooindex">
                <template v-for="(otiem, oindex) in titem.options" :key="oindex">
                  <template v-if="ootitem == otiem.value">{{ otiem.label }}{{ ooindex + 1 != tableobj[titem.key].length ? '、' : '' }}</template>
                </template>
              </template>
            </div>
            <div v-if="titem.type == 'link'" class="PI_Box_Item_content">
              {{ tableobj[titem.key] }}
              <span class="PI_Box_Item_link" @click="ToPage(titem.link)">{{ titem.linkbtn }}</span>
            </div>
            <div v-if="titem.type == 'linkkey'" class="PI_Box_Item_content">
              <span class="PI_Box_Item_link" @click="ToPagebyId(tableobj[titem.key])">{{ titem.linkbtn }}</span>
            </div>
            <div v-if="titem.type == 'linkkeyarr'" class="PI_Box_Item_content">
              <span class="PI_Box_Item_link" @click="setVisible(true, titem)">{{ titem.linkbtn }}</span>
              <a-image-preview-group
                v-if="titem.isshow"
                :style="{ display: 'none' }"
                :preview="{
                  visible: titem.isshow,
                  onVisibleChange: (e) => setVisible(e, titem),
                }"
              >
                <template v-for="(imgitem, imgindex) in titem.img" :key="imgindex">
                  <a-image :width="0" :src="imgitem" />
                </template>
              </a-image-preview-group>
            </div>
            <vxe-table border class="tableClass" v-if="titem.type == 'table'" :data="tableobj[titem.key]">
              <template v-for="(ttitem, ttindex) in titem.tableInfo" :key="ttindex">
                <vxe-column v-if="ttitem.ishide != true" width="auto" align="center">
                  <template #header>
                    <span v-if="ttitem.ismust" style="color: red">*</span>
                    {{ ttitem.title }}
                  </template>
                  <template v-if="ttitem.type == 'control'" #default="{ row }">
                    <span class="PI_Box_Item_link" @click="ToPagebyId(row[ttitem.fieldkey])">预览</span>
                  </template>
                  <template v-if="ttitem.type == 'text'" #default="{ row }">
                    <div>{{ row[ttitem.fieldkey] }}</div>
                    <a-button
                      @click="looknopass(row, ttitem.fieldkey)"
                      v-if="(ttitem.fieldkey == 'mobile_phone_number' || ttitem.fieldkey == 'collection_card_number') && !row.isGetNum"
                      type="link"
                      class="p-0 c-gray"
                    >
                      点击查看监控字段，系统会记录点击操作
                    </a-button>
                  </template>
                  <template v-if="ttitem.type == 'textoptions'" #default="{ row }">
                    <template v-for="(otiem, oindex) in ttitem.options" :key="oindex">
                      <template v-if="otiem.value == row[ttitem.fieldkey]">
                        {{ otiem.label }}
                      </template>
                    </template>
                  </template>
                </vxe-column>
              </template>
            </vxe-table>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue'
import { GetMobilephoneNumber, GetCollectionCardNumber } from '../../../../../servers/companyInfo'

const href = ref()
const VITE_APP_ENV = ref('')
const userData = ref()
onMounted(() => {
  href.value = window.location.origin
  VITE_APP_ENV.value = import.meta.env.VITE_APP_ENV
  const userDatastr = localStorage.getItem('userData') || ''
  userData.value = userDatastr != '' ? JSON.parse(userDatastr) : {}
  console.log('子组件')
})
const tableInfo = ref()
const tableobj = ref()
const setVisible = (value, titem) => {
  titem.img = []
  titem.key.forEach(async (key) => {
    let url = ''
    const id = tableobj.value[key]

    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${id}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${id}`
    }
    console.log('url', url)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })

    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    titem.img.push(previewUrl)
  })

  titem.isshow = value
}
const init = (tableItems, value) => {
  tableInfo.value = tableItems
  tableobj.value = JSON.parse(JSON.stringify(value))
}
const looknopass = async (row, fieldkey) => {
  if (fieldkey == 'mobile_phone_number' && row[fieldkey].indexOf('*') > -1) {
    const res = await GetMobilephoneNumber({ id: row.id, supplier_id: row.supplier_id })
    if (res.success == true) {
      row[fieldkey] = res.data[fieldkey]
      row.isGetNum = true
    } else {
      message.warning(res.message)
    }
  }
  if (fieldkey == 'collection_card_number' && row[fieldkey].indexOf('*') > -1) {
    const res = await GetCollectionCardNumber({ id: row.id, supplier_id: row.supplier_id })
    if (res.success == true) {
      row[fieldkey] = res.data[fieldkey]
      row.isGetNum = true
    } else {
      message.warning(res.message)
    }
  }
}
const ToPage = (link) => {
  window.open(link, '_blank')
}

const ToPagebyId = async (id) => {
  console.log('window', window.location)
  let url = ''
  if (VITE_APP_ENV.value == 'development') {
    url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${id}`
  } else {
    url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${id}`
  }

  console.log('url', url)

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })
    console.log('response11', response)

    if (!response.ok) {
      message.warning('获取失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)
    // 在新窗口打开预览
    window.open(previewUrl, '_blank')
    // 可选：一段时间后释放内存
    setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    return previewUrl
  } catch (error) {
    console.error('转换失败:', error)
    throw error
  }
}
defineExpose({ init })
</script>
<style lang="scss" scoped>
.personalInfoBox {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;

  .PI_Box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    font-size: 14px;

    .PI_Box_Title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 36px;
      padding-left: 12px;
      background: #ebeef5;
    }

    .PI_Box_Items {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;

      .PI_Box_Item {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        min-height: 28px;
        margin-top: 5px;
        margin-bottom: 5px;
        font-size: 14px;

        .PI_Box_Item_label {
          text-align: right;
        }

        .PI_Box_Item_content {
          width: 80%;
          text-align: left;
          word-wrap: break-word;

          .PI_Box_Item_link {
            color: blue;
            cursor: pointer;
          }
        }

        .tableClass {
          width: 100%;
          margin-top: 0;

          .PI_Box_Item_link {
            color: blue;
            cursor: pointer;
          }

          .colorgrey {
            font-size: 14px;
            color: #b4b6b9;
          }
        }
      }
    }
  }
}
</style>
